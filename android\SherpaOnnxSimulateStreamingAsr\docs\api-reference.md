# API 参考文档

## 核心 API 类

### 1. SimulateStreamingAsr

主要的语音识别管理类，采用单例模式。

```kotlin
object SimulateStreamingAsr {
    // 属性
    val recognizer: OfflineRecognizer
    val vad: Vad
    
    // 方法
    fun initOfflineRecognizer(assetManager: AssetManager?, application: Application)
    fun initVad(assetManager: AssetManager?)
}
```

#### 方法详解

##### initOfflineRecognizer

```kotlin
fun initOfflineRecognizer(assetManager: AssetManager? = null, application: Application)
```

**功能**: 初始化离线语音识别器

**参数**:
- `assetManager`: Android 资源管理器，用于加载模型文件
- `application`: 应用程序上下文

**使用示例**:
```kotlin
SimulateStreamingAsr.initOfflineRecognizer(this.assets, this.application)
```

**配置选项**:
- `asrModelType`: ASR 模型类型 (默认: 15)
- `numThreads`: 处理线程数 (默认: 2)
- `useHr`: 是否使用同音词替换 (默认: false)

##### initVad

```kotlin
fun initVad(assetManager: AssetManager? = null)
```

**功能**: 初始化语音活动检测器

**参数**:
- `assetManager`: Android 资源管理器

**使用示例**:
```kotlin
SimulateStreamingAsr.initVad(this.assets)
```

### 2. OfflineRecognizer

离线语音识别器类，负责执行语音识别任务。

```kotlin
class OfflineRecognizer(
    assetManager: AssetManager? = null,
    config: OfflineRecognizerConfig
) {
    // 方法
    fun createStream(): OfflineStream
    fun decode(stream: OfflineStream)
    fun getResult(stream: OfflineStream): OfflineRecognizerResult
    fun release()
}
```

#### 方法详解

##### createStream

```kotlin
fun createStream(): OfflineStream
```

**功能**: 创建一个新的识别流

**返回值**: `OfflineStream` 对象

**使用示例**:
```kotlin
val stream = SimulateStreamingAsr.recognizer.createStream()
```

##### decode

```kotlin
fun decode(stream: OfflineStream)
```

**功能**: 对音频流进行解码识别

**参数**:
- `stream`: 要解码的音频流

##### getResult

```kotlin
fun getResult(stream: OfflineStream): OfflineRecognizerResult
```

**功能**: 获取识别结果

**参数**:
- `stream`: 已解码的音频流

**返回值**: `OfflineRecognizerResult` 包含识别文本和元数据

### 3. OfflineStream

音频流处理类，用于向识别器输入音频数据。

```kotlin
class OfflineStream {
    fun acceptWaveform(samples: FloatArray, sampleRate: Int)
    fun inputFinished()
    fun release()
}
```

#### 方法详解

##### acceptWaveform

```kotlin
fun acceptWaveform(samples: FloatArray, sampleRate: Int)
```

**功能**: 向流中输入音频波形数据

**参数**:
- `samples`: 音频样本数组 (Float 格式)
- `sampleRate`: 采样率 (通常为 16000)

**使用示例**:
```kotlin
stream.acceptWaveform(audioSamples, 16000)
```

##### inputFinished

```kotlin
fun inputFinished()
```

**功能**: 标记音频输入结束

**使用示例**:
```kotlin
stream.inputFinished()
val result = recognizer.getResult(stream)
```

### 4. Vad (语音活动检测)

语音活动检测器，用于检测音频中的语音片段。

```kotlin
class Vad(
    assetManager: AssetManager? = null,
    var config: VadModelConfig
) {
    fun acceptWaveform(samples: FloatArray)
    fun isSpeechDetected(): Boolean
    fun reset()
    fun empty(): Boolean
    fun release()
}
```

#### 方法详解

##### acceptWaveform

```kotlin
fun acceptWaveform(samples: FloatArray)
```

**功能**: 输入音频数据进行语音活动检测

**参数**:
- `samples`: 音频样本数组 (建议 512 样本窗口)

**使用示例**:
```kotlin
SimulateStreamingAsr.vad.acceptWaveform(audioWindow)
```

##### isSpeechDetected

```kotlin
fun isSpeechDetected(): Boolean
```

**功能**: 检查是否检测到语音活动

**返回值**: `true` 如果检测到语音，`false` 否则

**使用示例**:
```kotlin
if (SimulateStreamingAsr.vad.isSpeechDetected()) {
    // 开始语音识别
}
```

##### reset

```kotlin
fun reset()
```

**功能**: 重置 VAD 状态

**使用场景**: 开始新的录音会话时

## 配置类 API

### 5. OfflineRecognizerConfig

离线识别器配置类。

```kotlin
data class OfflineRecognizerConfig(
    var modelConfig: OfflineModelConfig = OfflineModelConfig(),
    var ruleFsts: String = "",
    var hr: HomophoneReplacerConfig = HomophoneReplacerConfig()
)
```

#### 属性说明

- `modelConfig`: 模型配置
- `ruleFsts`: 规则有限状态转换器路径
- `hr`: 同音词替换配置

### 6. VadModelConfig

语音活动检测配置类。

```kotlin
data class VadModelConfig(
    var sileroVadModelConfig: SileroVadModelConfig = SileroVadModelConfig(),
    var sampleRate: Int = 16000,
    var numThreads: Int = 1,
    var provider: String = "cpu",
    var debug: Boolean = false
)
```

#### 属性说明

- `sileroVadModelConfig`: Silero VAD 模型配置
- `sampleRate`: 采样率 (默认: 16000)
- `numThreads`: 线程数 (默认: 1)
- `provider`: 计算提供者 ("cpu" 或 "gpu")
- `debug`: 调试模式

### 7. SileroVadModelConfig

Silero VAD 模型具体配置。

```kotlin
data class SileroVadModelConfig(
    var model: String = "",
    var threshold: Float = 0.5F,
    var minSilenceDuration: Float = 0.25F,
    var minSpeechDuration: Float = 0.25F,
    var windowSize: Int = 512,
    var maxSpeechDuration: Float = 5.0F
)
```

#### 属性说明

- `model`: 模型文件路径
- `threshold`: 语音检测阈值 (0.0-1.0)
- `minSilenceDuration`: 最小静音持续时间 (秒)
- `minSpeechDuration`: 最小语音持续时间 (秒)
- `windowSize`: 处理窗口大小 (样本数)
- `maxSpeechDuration`: 最大语音持续时间 (秒)

## 数据类 API

### 8. OfflineRecognizerResult

识别结果数据类。

```kotlin
data class OfflineRecognizerResult(
    val text: String,
    val tokens: Array<String>,
    val timestamps: FloatArray,
    val lang: String,
    val emotion: String,
    val event: String
)
```

#### 属性说明

- `text`: 识别的文本内容
- `tokens`: 词元数组
- `timestamps`: 时间戳数组
- `lang`: 检测到的语言
- `emotion`: 情感信息
- `event`: 事件信息

### 9. SpeechSegment

语音片段数据类。

```kotlin
class SpeechSegment(val start: Int, val samples: FloatArray)
```

#### 属性说明

- `start`: 片段开始位置
- `samples`: 音频样本数据

## UI 组件 API

### 10. HomeScreen

主屏幕组合函数。

```kotlin
@Composable
fun HomeScreen()
```

**功能**: 渲染语音识别主界面

**包含组件**:
- 录音控制按钮
- 识别结果列表
- 复制功能按钮

### 11. HelpScreen

帮助屏幕组合函数。

```kotlin
@Composable
fun HelpScreen()
```

**功能**: 显示应用帮助信息

### 12. MainScreen

主屏幕容器组合函数。

```kotlin
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(modifier: Modifier = Modifier)
```

**功能**: 提供应用主要的导航和布局结构

**包含组件**:
- 顶部应用栏
- 底部导航栏
- 导航主机

## 导航 API

### 13. NavRoutes

导航路由定义。

```kotlin
sealed class NavRoutes(val route: String) {
    object Home : NavRoutes("home")
    object Help : NavRoutes("help")
}
```

### 14. BarItem

导航栏项目数据类。

```kotlin
data class BarItem(
    val title: String,
    val image: ImageVector,
    val route: String
)
```

### 15. NavBarItems

导航栏项目配置对象。

```kotlin
object NavBarItems {
    val BarItems = listOf(
        BarItem("Home", Icons.Filled.Home, "home"),
        BarItem("Help", Icons.Filled.Info, "help")
    )
}
```

## 使用示例

### 完整的语音识别流程

```kotlin
// 1. 初始化组件
SimulateStreamingAsr.initOfflineRecognizer(assets, application)
SimulateStreamingAsr.initVad(assets)

// 2. 设置音频录制
val audioRecord = AudioRecord(
    MediaRecorder.AudioSource.MIC,
    16000,
    AudioFormat.CHANNEL_IN_MONO,
    AudioFormat.ENCODING_PCM_16BIT,
    bufferSize
)

// 3. 开始录制和处理
audioRecord.startRecording()

// 4. 音频处理循环
while (isRecording) {
    val samples = readAudioSamples()
    
    // VAD 处理
    SimulateStreamingAsr.vad.acceptWaveform(samples)
    
    if (SimulateStreamingAsr.vad.isSpeechDetected()) {
        // ASR 处理
        val stream = SimulateStreamingAsr.recognizer.createStream()
        stream.acceptWaveform(samples, 16000)
        stream.inputFinished()
        
        val result = SimulateStreamingAsr.recognizer.getResult(stream)
        println("识别结果: ${result.text}")
        
        stream.release()
    }
}

// 5. 清理资源
audioRecord.stop()
audioRecord.release()
```

### 错误处理示例

```kotlin
try {
    SimulateStreamingAsr.initOfflineRecognizer(assets, application)
} catch (e: Exception) {
    Log.e(TAG, "初始化识别器失败: ${e.message}")
    // 处理初始化失败
}

try {
    val result = recognizer.getResult(stream)
    if (result.text.isNotEmpty()) {
        // 处理识别结果
    }
} catch (e: Exception) {
    Log.e(TAG, "识别失败: ${e.message}")
    // 处理识别错误
}
```

## 性能优化建议

### 1. 内存管理

```kotlin
// 及时释放资源
stream.release()
recognizer.release()
vad.release()

// 重用对象
val streamPool = mutableListOf<OfflineStream>()
```

### 2. 线程优化

```kotlin
// 使用适当的调度器
CoroutineScope(Dispatchers.IO).launch {
    // 音频 I/O 操作
}

CoroutineScope(Dispatchers.Default).launch {
    // CPU 密集型处理
}
```

### 3. 缓冲区管理

```kotlin
// 使用固定大小的缓冲区
val bufferSize = AudioRecord.getMinBufferSize(
    sampleRate, channelConfig, audioFormat
) * 2

// 避免频繁的内存分配
val reusableBuffer = ShortArray(bufferSize)
```

这个 API 参考文档提供了 SherpaOnnx 应用中所有主要类和方法的详细说明，包括参数、返回值、使用示例和最佳实践建议。