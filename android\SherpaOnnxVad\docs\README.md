# SherpaOnnxVad Android 应用文档

## 项目概述

SherpaOnnxVad 是一个基于 Sherpa-ONNX 的 Android 语音活动检测（Voice Activity Detection, VAD）应用。该应用使用 Silero VAD 模型来实时检测音频流中的语音活动，并通过可视化界面向用户展示检测结果。

## 主要功能模块

### 1. 核心模块

#### 1.1 VAD 引擎模块 (`Vad.kt`)
- **功能**: 封装 Sherpa-ONNX VAD 功能的 Kotlin 接口
- **主要组件**:
  - `SileroVadModelConfig`: Silero VAD 模型配置
  - `VadModelConfig`: VAD 模型总体配置
  - `Vad`: VAD 核心类，提供语音检测功能
  - `SpeechSegment`: 语音片段数据结构

#### 1.2 主界面模块 (`MainActivity.kt`)
- **功能**: 应用主界面，负责用户交互和音频处理
- **主要功能**:
  - 音频录制管理
  - 实时 VAD 处理
  - UI 状态更新
  - 权限管理

### 2. UI 模块

#### 2.1 布局文件 (`activity_main.xml`)
- **组件**:
  - 录制控制按钮
  - 语音状态指示圆圈
  - 约束布局容器

#### 2.2 视觉资源
- **drawable 资源**:
  - `black_circle.xml`: 静音状态指示器（黑色圆圈）
  - `red_circle.xml`: 语音状态指示器（红色圆圈）

### 3. 原生库模块

#### 3.1 JNI 库
- **库文件**: `libsherpa-onnx-jni.so`
- **支持架构**: arm64-v8a, armeabi-v7a, x86, x86_64
- **功能**: 提供 Sherpa-ONNX C++ 库的 JNI 接口

#### 3.2 模型文件
- **模型**: `silero_vad.onnx`
- **位置**: `assets/` 目录
- **用途**: Silero VAD 深度学习模型

## 代码执行流程

### 应用启动流程

```mermaid
flowchart TD
    A[应用启动] --> B[MainActivity.onCreate]
    B --> C[请求录音权限]
    C --> D[初始化 VAD 模型]
    D --> E[设置 UI 组件]
    E --> F[等待用户操作]
    
    D --> D1[创建 VadModelConfig]
    D1 --> D2[加载 silero_vad.onnx 模型]
    D2 --> D3[初始化 Vad 实例]
```

### 录音和 VAD 处理流程

```mermaid
flowchart TD
    A[用户点击开始按钮] --> B[检查录音权限]
    B --> C[初始化麦克风]
    C --> D[创建 AudioRecord]
    D --> E[开始录音]
    E --> F[启动处理线程]
    F --> G[循环读取音频数据]
    
    G --> H[读取音频缓冲区]
    H --> I[转换为浮点数组]
    I --> J[调用 vad.acceptWaveform]
    J --> K[检测语音活动]
    K --> L[更新 UI 状态]
    L --> M{继续录音?}
    
    M -->|是| G
    M -->|否| N[停止录音]
    N --> O[释放资源]
    O --> P[重置 UI]
    
    K --> K1[vad.isSpeechDetected]
    K1 --> K2{检测到语音?}
    K2 -->|是| K3[显示红色圆圈]
    K2 -->|否| K4[显示黑色圆圈]
```

### VAD 模型处理流程

```mermaid
flowchart TD
    A[音频数据输入] --> B[vad.acceptWaveform]
    B --> C[JNI 调用原生库]
    C --> D[Sherpa-ONNX 处理]
    D --> E[Silero VAD 模型推理]
    E --> F[计算语音概率]
    F --> G[应用阈值判断]
    G --> H[返回检测结果]
    H --> I[vad.isSpeechDetected]
    I --> J[UI 状态更新]
    
    E --> E1[窗口大小: 512 样本]
    E1 --> E2[阈值: 0.5]
    E2 --> E3[最小语音时长: 0.25s]
    E3 --> E4[最小静音时长: 0.25s]
```

### 权限管理流程

```mermaid
flowchart TD
    A[应用启动] --> B[检查录音权限]
    B --> C{权限已授予?}
    C -->|是| D[继续初始化]
    C -->|否| E[请求权限]
    E --> F[用户响应]
    F --> G{权限被授予?}
    G -->|是| H[记录权限状态]
    G -->|否| I[显示错误并退出]
    H --> D
```

## 技术特性

### 音频处理参数
- **采样率**: 16kHz
- **音频格式**: PCM 16-bit
- **声道**: 单声道
- **缓冲区大小**: 512 样本

### VAD 模型配置
- **模型类型**: Silero VAD
- **检测阈值**: 0.5
- **窗口大小**: 512 样本
- **最小语音持续时间**: 0.25 秒
- **最小静音持续时间**: 0.25 秒
- **最大语音持续时间**: 5.0 秒

### 性能优化
- **多线程处理**: 音频处理在独立线程中进行
- **实时响应**: UI 更新通过 `runOnUiThread` 确保流畅性
- **资源管理**: 及时释放 AudioRecord 和 VAD 资源

## 依赖关系

### Android 依赖
- **最低 SDK**: API 21 (Android 5.0)
- **目标 SDK**: API 33 (Android 13)
- **核心库**: AndroidX, Material Design

### 原生库依赖
- **Sherpa-ONNX**: 语音处理核心库
- **ONNX Runtime**: 深度学习推理引擎
- **Silero VAD**: 预训练语音活动检测模型

## 使用说明

1. **启动应用**: 应用会自动请求录音权限
2. **开始检测**: 点击 "Start" 按钮开始语音活动检测
3. **观察状态**: 圆圈颜色变化表示语音检测状态
   - 黑色: 静音状态
   - 红色: 检测到语音
4. **停止检测**: 点击 "Stop" 按钮停止检测

## 扩展可能性

- **多模型支持**: 可扩展支持其他 VAD 模型
- **参数调优**: 运行时调整检测参数
- **音频保存**: 保存检测到的语音片段
- **网络功能**: 将检测结果上传到服务器
- **可视化增强**: 添加音频波形显示