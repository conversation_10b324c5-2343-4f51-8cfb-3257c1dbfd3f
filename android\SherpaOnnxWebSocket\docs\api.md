# SherpaOnnx WebSocket API 文档

## 概述

SherpaOnnx WebSocket API 提供实时语音识别服务，客户端通过 WebSocket 连接发送音频数据，服务器返回识别结果。

## 连接信息

### 基本信息
- **协议**: WebSocket (ws://) 或 WebSocket Secure (wss://)
- **默认端口**: 6006
- **默认服务器**: ws://*************:6006
- **连接类型**: 长连接

### 连接建立流程

```mermaid
sequenceDiagram
    participant Client as Android Client
    participant Server as WebSocket Server
    
    Client->>Server: WebSocket Handshake Request
    Note over Client,Server: HTTP Upgrade to WebSocket
    Server->>Client: WebSocket Handshake Response
    Note over Client,Server: Connection Established
    
    Client->>Server: Audio Data (Binary)
    Server->>Client: Recognition Result (JSON)
    
    loop Continuous Recognition
        Client->>Server: Audio Data (Binary)
        Server->>Client: Recognition Result (JSON)
    end
    
    Client->>Server: Close Connection
    Server->>Client: Close Acknowledgment
```

## 数据格式规范

### 音频数据格式 (客户端 → 服务器)

#### 音频参数
- **采样率**: 16000 Hz
- **声道**: 单声道 (Mono)
- **位深度**: 32-bit Float
- **字节序**: Little Endian
- **数据间隔**: 100ms (0.1秒)

#### 数据结构

```
音频数据包结构:
┌─────────────────────────────────────┐
│ Float32 Array (Little Endian)      │
├─────────────────────────────────────┤
│ Sample 1 (4 bytes)                 │
│ Sample 2 (4 bytes)                 │
│ ...                                │
│ Sample N (4 bytes)                 │
└─────────────────────────────────────┘

每个样本值范围: [-1.0, 1.0]
数据包大小: 1600 samples × 4 bytes = 6400 bytes (100ms)
```

#### 音频处理流程

```mermaid
flowchart LR
    A["麦克风输入"] --> B["AudioRecord"]
    B --> C["16-bit PCM"]
    C --> D["归一化处理"]
    D --> E["32-bit Float"]
    E --> F["Little Endian"]
    F --> G["WebSocket发送"]
    
    subgraph "数据转换"
        C --> |"÷ 32768.0f"| D
        D --> |"Float32"| E
        E --> |"ByteBuffer"| F
    end
```

#### 代码示例

```kotlin
// 音频数据处理示例
val buffer = ShortArray(bufferSize)
val ret = audioRecord?.read(buffer, 0, buffer.size)

if (ret != null && ret > 0) {
    // 转换为Float数组并归一化
    val samples = FloatArray(ret) { buffer[it] / 32768.0f }
    
    // 创建ByteBuffer
    val byteBuffer = ByteBuffer.allocate(4 * samples.size)
        .order(ByteOrder.LITTLE_ENDIAN)
    
    // 写入Float数据
    for (f in samples) {
        byteBuffer.putFloat(f)
    }
    
    // 发送到服务器
    websocketClient?.send(byteBuffer.array())
}
```

### 识别结果格式 (服务器 → 客户端)

#### JSON 响应结构

```json
{
    "text": "识别的文本内容",
    "segment": 12345
}
```

#### 字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `text` | String | 是 | 识别出的文本内容，可能为空字符串 |
| `segment` | Long | 是 | 语音片段的唯一标识符，用于排序和去重 |

#### 响应示例

```json
// 示例1: 正常识别结果
{
    "text": "你好世界",
    "segment": 1001
}

// 示例2: 空识别结果
{
    "text": "",
    "segment": 1002
}

// 示例3: 长文本识别
{
    "text": "这是一段较长的语音识别结果，包含多个词语和标点符号。",
    "segment": 1003
}
```

#### 数据处理流程

```mermaid
flowchart TD
    A["接收JSON消息"] --> B["Gson解析"]
    B --> C["提取text字段"]
    B --> D["提取segment字段"]
    C --> E["存储到HashMap"]
    D --> E
    E --> F["按segment排序"]
    F --> G["生成显示文本"]
    G --> H["更新UI"]
```

## WebSocket 事件处理

### 连接事件

#### onOpen 事件

```kotlin
override fun onOpen(handshakedata: ServerHandshake?) {
    Log.i(TAG, "WebSocket连接已建立")
    isConnected = true
    runOnUiThread {
        recordButton.isEnabled = true
        connectButton.text = getString(R.string.disconnect)
    }
}
```

**触发时机**: WebSocket 连接成功建立时
**处理逻辑**: 
- 更新连接状态
- 启用录音按钮
- 更新UI显示

#### onMessage 事件

```kotlin
override fun onMessage(message: String?) {
    Log.i(TAG, "收到识别结果: $message")
    
    val speechContent = gson.fromJson<SpeechContent>(
        message,
        object : TypeToken<SpeechContent?>() {}.type
    )
    
    val text = speechContent.text
    val segment = speechContent.segment
    
    recognitionText[segment] = text
    runOnUiThread {
        textView.text = getDisplayResult()
    }
}
```

**触发时机**: 收到服务器发送的识别结果时
**处理逻辑**:
- 解析JSON消息
- 提取文本和片段信息
- 更新识别结果存储
- 刷新UI显示

#### onClose 事件

```kotlin
override fun onClose(code: Int, reason: String?, remote: Boolean?) {
    Log.i(TAG, "WebSocket连接已关闭: code=$code, reason=$reason")
    isConnected = false
    runOnUiThread {
        recordButton.isEnabled = false
        connectButton.text = getString(R.string.connect)
        textView.text = getString(R.string.hint)
    }
}
```

**触发时机**: WebSocket 连接关闭时
**处理逻辑**:
- 更新连接状态
- 禁用录音功能
- 重置UI状态

#### onError 事件

```kotlin
override fun onError(ex: Exception?) {
    Log.e(TAG, "WebSocket错误: $ex")
    runOnUiThread {
        textView.text = "连接错误: $ex"
    }
}
```

**触发时机**: WebSocket 连接或通信出现错误时
**处理逻辑**:
- 记录错误日志
- 显示错误信息给用户

## 错误处理

### 错误类型

```mermaid
flowchart TD
    A["WebSocket错误"] --> B["连接错误"]
    A --> C["通信错误"]
    A --> D["数据错误"]
    
    B --> B1["网络不可达"]
    B --> B2["服务器拒绝"]
    B --> B3["超时"]
    
    C --> C1["发送失败"]
    C --> C2["连接中断"]
    
    D --> D1["JSON解析错误"]
    D --> D2["数据格式错误"]
```

### 错误码定义

| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 1000 | 正常关闭 | 无需处理 |
| 1001 | 端点离开 | 尝试重连 |
| 1002 | 协议错误 | 检查协议版本 |
| 1003 | 不支持的数据类型 | 检查数据格式 |
| 1006 | 异常关闭 | 网络检查 |
| 1011 | 服务器错误 | 稍后重试 |

### 重连机制

```mermaid
stateDiagram-v2
    [*] --> Connected
    Connected --> Disconnected: 连接断开
    Disconnected --> Reconnecting: 自动重连
    Reconnecting --> Connected: 重连成功
    Reconnecting --> Failed: 重连失败
    Failed --> Reconnecting: 延迟重试
    Failed --> [*]: 放弃重连
```

**重连策略**:
- **初始延迟**: 1秒
- **最大延迟**: 30秒
- **退避算法**: 指数退避
- **最大重试**: 5次

## 性能优化

### 数据传输优化

```mermaid
gantt
    title 音频数据传输时序
    dateFormat X
    axisFormat %s
    
    section 音频采集
    采集100ms数据    :active, collect, 0, 1
    格式转换        :active, convert, 0, 1
    
    section 网络传输
    数据发送        :active, send, 1, 2
    等待响应        :wait, response, 2, 3
    
    section 结果处理
    JSON解析        :active, parse, 3, 4
    UI更新         :active, ui, 4, 5
```

**优化措施**:
- **并行处理**: 音频采集和网络传输并行
- **缓冲管理**: 合理的缓冲区大小
- **压缩传输**: 可选的音频压缩
- **批量发送**: 减少网络开销

### 内存优化

```kotlin
// 音频缓冲区复用
class AudioBufferPool {
    private val bufferPool = mutableListOf<ByteArray>()
    
    fun getBuffer(size: Int): ByteArray {
        return bufferPool.removeFirstOrNull() ?: ByteArray(size)
    }
    
    fun returnBuffer(buffer: ByteArray) {
        if (bufferPool.size < MAX_POOL_SIZE) {
            bufferPool.add(buffer)
        }
    }
}
```

## 安全考虑

### 传输安全

```mermaid
flowchart LR
    A["音频数据"] --> B["TLS加密"]
    B --> C["WSS传输"]
    C --> D["服务器解密"]
    D --> E["语音识别"]
    E --> F["结果加密"]
    F --> G["WSS返回"]
    G --> H["客户端解密"]
```

**安全措施**:
- **WSS协议**: 使用 WebSocket Secure
- **证书验证**: 验证服务器证书
- **数据加密**: TLS 1.2+ 加密传输
- **身份认证**: 可选的令牌认证

### 隐私保护

```mermaid
flowchart TD
    A["音频采集"] --> B["实时传输"]
    B --> C["服务器处理"]
    C --> D["立即删除"]
    D --> E["结果返回"]
    
    F["本地存储"] --> G["不存储音频"]
    G --> H["仅存储结果"]
    H --> I["定期清理"]
```

**隐私策略**:
- **实时处理**: 不存储音频文件
- **数据最小化**: 只传输必要数据
- **本地清理**: 定期清理识别结果
- **用户控制**: 用户可随时停止

## 测试和调试

### 连接测试

```bash
# 使用 wscat 测试 WebSocket 连接
npm install -g wscat
wscat -c ws://*************:6006

# 发送测试数据
> {"test": "connection"}
```

### 音频数据验证

```kotlin
// 音频数据完整性检查
fun validateAudioData(data: ByteArray): Boolean {
    if (data.size % 4 != 0) {
        Log.e(TAG, "音频数据大小不是4的倍数")
        return false
    }
    
    val floatBuffer = ByteBuffer.wrap(data)
        .order(ByteOrder.LITTLE_ENDIAN)
        .asFloatBuffer()
    
    while (floatBuffer.hasRemaining()) {
        val sample = floatBuffer.get()
        if (sample < -1.0f || sample > 1.0f) {
            Log.w(TAG, "音频样本超出范围: $sample")
        }
    }
    
    return true
}
```

### 性能监控

```kotlin
// 性能指标收集
class PerformanceMonitor {
    private var audioPacketsSent = 0
    private var recognitionResults = 0
    private var averageLatency = 0L
    
    fun recordAudioSent() {
        audioPacketsSent++
    }
    
    fun recordRecognitionResult(latency: Long) {
        recognitionResults++
        averageLatency = (averageLatency + latency) / 2
    }
    
    fun getStats(): String {
        return "发送: $audioPacketsSent, 接收: $recognitionResults, 延迟: ${averageLatency}ms"
    }
}
```

## 常见问题

### Q: 连接失败怎么办？
**A**: 
1. 检查网络连接
2. 验证服务器地址和端口
3. 确认服务器是否运行
4. 检查防火墙设置

### Q: 音频识别不准确？
**A**: 
1. 确保麦克风权限已授予
2. 检查环境噪音
3. 保持设备与嘴部适当距离
4. 确认音频参数配置正确

### Q: 识别结果延迟高？
**A**: 
1. 检查网络延迟
2. 优化音频缓冲区大小
3. 使用更快的网络连接
4. 考虑本地部署服务器

### Q: 内存使用过高？
**A**: 
1. 定期清理识别结果
2. 优化音频缓冲区管理
3. 及时释放 AudioRecord 资源
4. 监控内存泄漏

## 版本兼容性

| 客户端版本 | 服务器版本 | 兼容性 | 说明 |
|------------|------------|--------|------|
| 1.0.x | 1.0.x | ✅ | 完全兼容 |
| 1.0.x | 1.1.x | ⚠️ | 部分兼容 |
| 1.1.x | 1.0.x | ❌ | 不兼容 |

**升级建议**: 保持客户端和服务器版本同步更新