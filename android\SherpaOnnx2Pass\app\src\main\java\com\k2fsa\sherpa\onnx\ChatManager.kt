package com.k2fsa.sherpa.onnx

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 聊天管理器 - 负责聊天记录的存储和管理
 */
class ChatManager private constructor(private val context: Context) {

    companion object {
        private const val TAG = "ChatManager"
        private const val PREFS_NAME = "chat_prefs"
        private const val KEY_CHAT_HISTORY = "chat_history_"
        private const val MAX_MESSAGES_PER_MEETING = 100 // 每个会议最多保存100条聊天记录

        @Volatile
        private var INSTANCE: ChatManager? = null

        fun getInstance(context: Context): ChatManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ChatManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()

    /**
     * 保存聊天消息
     */
    fun saveChatMessage(message: ChatMessage) {
        try {
            val messages = getChatHistory(message.meetingRecordId).toMutableList()
            messages.add(message)
            
            // 限制消息数量
            if (messages.size > MAX_MESSAGES_PER_MEETING) {
                messages.removeAt(0) // 移除最旧的消息
            }
            
            val json = gson.toJson(messages)
            prefs.edit()
                .putString(KEY_CHAT_HISTORY + message.meetingRecordId, json)
                .apply()
            
            Log.d(TAG, "聊天消息已保存: ${message.meetingRecordId}")
        } catch (e: Exception) {
            Log.e(TAG, "保存聊天消息失败", e)
        }
    }

    /**
     * 获取指定会议的聊天历史
     */
    fun getChatHistory(meetingRecordId: String): List<ChatMessage> {
        return try {
            val json = prefs.getString(KEY_CHAT_HISTORY + meetingRecordId, null)
            if (json != null) {
                val type = object : TypeToken<List<ChatMessage>>() {}.type
                gson.fromJson(json, type) ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取聊天历史失败", e)
            emptyList()
        }
    }

    /**
     * 清空指定会议的聊天历史
     */
    fun clearChatHistory(meetingRecordId: String) {
        try {
            prefs.edit()
                .remove(KEY_CHAT_HISTORY + meetingRecordId)
                .apply()
            Log.d(TAG, "聊天历史已清空: $meetingRecordId")
        } catch (e: Exception) {
            Log.e(TAG, "清空聊天历史失败", e)
        }
    }

    /**
     * 获取最近的聊天记录（用于在详情页显示）
     */
    fun getRecentChatHistory(meetingRecordId: String, limit: Int = 3): List<ChatMessage> {
        val allMessages = getChatHistory(meetingRecordId)
        return if (allMessages.size <= limit) {
            allMessages
        } else {
            allMessages.takeLast(limit)
        }
    }

    /**
     * 检查是否有聊天记录
     */
    fun hasChatHistory(meetingRecordId: String): Boolean {
        return getChatHistory(meetingRecordId).isNotEmpty()
    }

    /**
     * 删除指定会议的所有聊天数据
     */
    fun deleteChatData(meetingRecordId: String) {
        clearChatHistory(meetingRecordId)
    }
}