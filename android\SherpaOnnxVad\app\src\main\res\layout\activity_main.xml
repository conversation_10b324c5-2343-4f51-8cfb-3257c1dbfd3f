<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.k2fsa.sherpa.onnx.vad.MainActivity">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="bottom"
        android:orientation="vertical"
        >

        <Space
            android:layout_width="match_parent"
            android:layout_height="10dp" />

        <LinearLayout
            android:id="@+id/powerCircle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/black_circle"
            android:orientation="vertical" />

        <Space
            android:layout_width="match_parent"
            android:layout_height="200dp" />

        <Button
            android:id="@+id/record_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/start" />



    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>
