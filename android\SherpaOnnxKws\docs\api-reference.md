# SherpaOnnxKws API参考文档

## 核心类API

### 1. KeywordSpotter

关键词检测的核心类，负责加载模型和执行检测。

#### 构造函数
```kotlin
KeywordSpotter(
    assetManager: AssetManager? = null,
    val config: KeywordSpotterConfig
)
```

**参数说明:**
- `assetManager`: Android资源管理器，用于从assets目录加载模型文件
- `config`: 关键词检测器配置对象

#### 主要方法

##### createStream(keywords: String = ""): OnlineStream
创建一个新的音频流处理对象。

**参数:**
- `keywords`: 要检测的关键词字符串，多个关键词用换行符分隔

**返回值:**
- `OnlineStream`: 音频流对象

**示例:**
```kotlin
val stream = kws.createStream("你好\n蛋哥蛋哥")
```

##### decode(stream: OnlineStream)
对音频流进行解码和关键词检测。

**参数:**
- `stream`: 要处理的音频流对象

##### isReady(stream: OnlineStream): Boolean
检查检测器是否准备好处理音频数据。

**参数:**
- `stream`: 音频流对象

**返回值:**
- `Boolean`: true表示准备就绪，false表示还需要更多音频数据

##### getResult(stream: OnlineStream): KeywordSpotterResult
获取关键词检测结果。

**参数:**
- `stream`: 音频流对象

**返回值:**
- `KeywordSpotterResult`: 检测结果对象

##### reset(stream: OnlineStream)
重置音频流的检测状态。

**参数:**
- `stream`: 要重置的音频流对象

##### release()
释放检测器占用的资源。

### 2. OnlineStream

音频流处理类，负责接收和处理实时音频数据。

#### 构造函数
```kotlin
OnlineStream(var ptr: Long = 0)
```

**参数说明:**
- `ptr`: JNI层对象指针

#### 主要方法

##### acceptWaveform(samples: FloatArray, sampleRate: Int)
接收音频波形数据。

**参数:**
- `samples`: 音频样本数组（浮点数格式，范围-1.0到1.0）
- `sampleRate`: 音频采样率（Hz）

**示例:**
```kotlin
val samples = FloatArray(1600) // 100ms的16kHz音频
stream.acceptWaveform(samples, 16000)
```

##### inputFinished()
标记音频输入结束。

##### release()
释放音频流占用的资源。

### 3. MainActivity

应用主活动类，负责UI交互和业务逻辑控制。

#### 主要属性

```kotlin
private lateinit var kws: KeywordSpotter
private lateinit var stream: OnlineStream
private var audioRecord: AudioRecord?
private lateinit var recordButton: Button
private lateinit var textView: TextView
private lateinit var inputText: EditText
```

#### 主要方法

##### onCreate(savedInstanceState: Bundle?)
Activity生命周期方法，负责初始化。

##### onclick()
录音按钮点击事件处理方法。

**功能:**
- 开始/停止录音
- 设置关键词
- 管理录音状态

##### processSamples()
音频处理线程方法，在后台线程中运行。

**功能:**
- 持续读取麦克风数据
- 将音频数据发送给检测器
- 处理检测结果
- 更新UI显示

##### initMicrophone(): Boolean
初始化麦克风录音。

**返回值:**
- `Boolean`: 初始化是否成功

##### initModel()
初始化关键词检测模型。

## 配置类API

### 4. KeywordSpotterConfig

关键词检测器配置数据类。

```kotlin
data class KeywordSpotterConfig(
    var featConfig: FeatureConfig = FeatureConfig(),
    var modelConfig: OnlineModelConfig = OnlineModelConfig(),
    var maxActivePaths: Int = 4,
    var keywordsFile: String = "keywords.txt",
    var keywordsScore: Float = 1.5f,
    var keywordsThreshold: Float = 0.25f,
    var numTrailingBlanks: Int = 2,
)
```

**参数说明:**
- `featConfig`: 特征提取配置
- `modelConfig`: 模型配置
- `maxActivePaths`: 最大活跃路径数
- `keywordsFile`: 关键词文件路径
- `keywordsScore`: 关键词得分权重
- `keywordsThreshold`: 关键词检测阈值
- `numTrailingBlanks`: 尾随空白数量

### 5. FeatureConfig

音频特征提取配置数据类。

```kotlin
data class FeatureConfig(
    var sampleRate: Int = 16000,
    var featureDim: Int = 80,
    var dither: Float = 0.0f
)
```

**参数说明:**
- `sampleRate`: 音频采样率（Hz）
- `featureDim`: 特征维度
- `dither`: 抖动参数，用于增加音频的随机性

### 6. OnlineModelConfig

在线模型配置数据类。

```kotlin
data class OnlineModelConfig(
    var transducer: OnlineTransducerModelConfig = OnlineTransducerModelConfig(),
    var paraformer: OnlineParaformerModelConfig = OnlineParaformerModelConfig(),
    var zipformer2Ctc: OnlineZipformer2CtcModelConfig = OnlineZipformer2CtcModelConfig(),
    var neMoCtc: OnlineNeMoCtcModelConfig = OnlineNeMoCtcModelConfig(),
    var tokens: String = "",
    var numThreads: Int = 1,
    var debug: Boolean = false,
    var provider: String = "cpu",
    var modelType: String = "",
    var modelingUnit: String = "",
    var bpeVocab: String = "",
)
```

## 结果类API

### 7. KeywordSpotterResult

关键词检测结果数据类。

```kotlin
data class KeywordSpotterResult(
    val keyword: String,
    val tokens: Array<String>,
    val timestamps: FloatArray,
)
```

**属性说明:**
- `keyword`: 检测到的关键词字符串
- `tokens`: 词元数组
- `timestamps`: 时间戳数组

## 工具函数API

### 8. getFeatureConfig

获取特征配置的工具函数。

```kotlin
fun getFeatureConfig(sampleRate: Int, featureDim: Int): FeatureConfig
```

**参数:**
- `sampleRate`: 采样率
- `featureDim`: 特征维度

**返回值:**
- `FeatureConfig`: 配置好的特征配置对象

## 常量定义

### 音频参数
```kotlin
private const val REQUEST_RECORD_AUDIO_PERMISSION = 200
private val audioSource = MediaRecorder.AudioSource.MIC
private val sampleRateInHz = 16000
private val channelConfig = AudioFormat.CHANNEL_IN_MONO
private val audioFormat = AudioFormat.ENCODING_PCM_16BIT
```

### 处理参数
```kotlin
val interval = 0.1 // 100ms处理间隔
val bufferSize = (interval * sampleRateInHz).toInt() // 缓冲区大小
```

## 使用示例

### 基本使用流程

```kotlin
// 1. 创建配置
val config = KeywordSpotterConfig(
    featConfig = getFeatureConfig(sampleRate = 16000, featureDim = 80),
    modelConfig = getKwsModelConfig(type = 0)!!,
    keywordsFile = getKeywordsFile(type = 0),
)

// 2. 创建检测器
val kws = KeywordSpotter(
    assetManager = application.assets,
    config = config,
)

// 3. 创建音频流
val stream = kws.createStream("你好\n蛋哥蛋哥")

// 4. 处理音频数据
val samples = FloatArray(1600) // 音频数据
stream.acceptWaveform(samples, 16000)

// 5. 检测关键词
if (kws.isReady(stream)) {
    kws.decode(stream)
    val result = kws.getResult(stream)
    if (result.keyword.isNotBlank()) {
        println("检测到关键词: ${result.keyword}")
        kws.reset(stream)
    }
}

// 6. 释放资源
stream.release()
kws.release()
```

## 注意事项

1. **权限要求**: 使用前必须获取`RECORD_AUDIO`权限
2. **线程安全**: 音频处理应在后台线程中进行
3. **资源管理**: 使用完毕后必须调用`release()`方法释放资源
4. **音频格式**: 支持16kHz采样率的16位PCM格式
5. **关键词格式**: 多个关键词用换行符分隔，支持拼音和汉字格式
6. **JNI依赖**: 依赖`sherpa-onnx-jni`本地库