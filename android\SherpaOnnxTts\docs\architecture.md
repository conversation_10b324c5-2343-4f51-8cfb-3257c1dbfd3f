# SherpaOnnxTts 架构设计文档

## 系统架构概览

### 整体架构图

```mermaid
graph TB
    subgraph "Android Application Layer"
        UI["用户界面层<br/>MainActivity"]
        BL["业务逻辑层<br/>TTS Engine"]
    end
    
    subgraph "Native Layer"
        JNI["JNI接口层<br/>sherpa-onnx-jni"]
        ONNX["ONNX Runtime<br/>模型推理引擎"]
    end
    
    subgraph "Model Layer"
        VITS["VITS模型"]
        Matcha["Matcha模型"]
        Kokoro["Kokoro模型"]
    end
    
    subgraph "Audio Layer"
        AT["AudioTrack<br/>实时播放"]
        MP["MediaPlayer<br/>文件播放"]
    end
    
    subgraph "Resource Layer"
        Assets["Assets资源"]
        Storage["内部存储"]
    end
    
    UI --> BL
    BL --> JNI
    JNI --> ONNX
    ONNX --> VITS
    ONNX --> Matcha
    ONNX --> Kokoro
    BL --> AT
    BL --> MP
    BL --> Assets
    BL --> Storage
```

## 分层架构设计

### 1. 表现层 (Presentation Layer)

```mermaid
classDiagram
    class MainActivity {
        -tts: OfflineTts
        -text: EditText
        -sid: EditText
        -speed: EditText
        -generate: Button
        -play: Button
        -stop: Button
        -track: AudioTrack
        -mediaPlayer: MediaPlayer
        +onCreate()
        +onClickGenerate()
        +onClickPlay()
        +onClickStop()
        +callback(samples: FloatArray)
        -initTts()
        -initAudioTrack()
        -copyAssets()
    }
    
    MainActivity --> OfflineTts
    MainActivity --> AudioTrack
    MainActivity --> MediaPlayer
```

**职责**:
- 用户界面管理
- 用户交互处理
- 音频播放控制
- 资源文件管理

### 2. 业务逻辑层 (Business Logic Layer)

```mermaid
classDiagram
    class OfflineTts {
        -ptr: Long
        -config: OfflineTtsConfig
        +sampleRate(): Int
        +numSpeakers(): Int
        +generate(text, sid, speed): GeneratedAudio
        +generateWithCallback(): GeneratedAudio
        +allocate()
        +free()
        -newFromAsset()
        -newFromFile()
    }
    
    class GeneratedAudio {
        +samples: FloatArray
        +sampleRate: Int
        +save(filename: String): Boolean
    }
    
    class OfflineTtsConfig {
        +model: OfflineTtsModelConfig
        +ruleFsts: String
        +ruleFars: String
        +maxNumSentences: Int
        +silenceScale: Float
    }
    
    OfflineTts --> GeneratedAudio
    OfflineTts --> OfflineTtsConfig
```

**职责**:
- TTS核心功能实现
- 模型配置管理
- 音频数据处理
- JNI接口封装

### 3. 数据访问层 (Data Access Layer)

```mermaid
classDiagram
    class OfflineTtsModelConfig {
        +vits: OfflineTtsVitsModelConfig
        +matcha: OfflineTtsMatchaModelConfig
        +kokoro: OfflineTtsKokoroModelConfig
        +numThreads: Int
        +debug: Boolean
        +provider: String
    }
    
    class OfflineTtsVitsModelConfig {
        +model: String
        +lexicon: String
        +tokens: String
        +dataDir: String
        +dictDir: String
        +noiseScale: Float
        +noiseScaleW: Float
        +lengthScale: Float
    }
    
    class OfflineTtsMatchaModelConfig {
        +acousticModel: String
        +vocoder: String
        +lexicon: String
        +tokens: String
        +dataDir: String
        +dictDir: String
        +noiseScale: Float
        +lengthScale: Float
    }
    
    class OfflineTtsKokoroModelConfig {
        +model: String
        +voices: String
        +tokens: String
        +dataDir: String
        +lexicon: String
        +dictDir: String
        +lengthScale: Float
    }
    
    OfflineTtsModelConfig --> OfflineTtsVitsModelConfig
    OfflineTtsModelConfig --> OfflineTtsMatchaModelConfig
    OfflineTtsModelConfig --> OfflineTtsKokoroModelConfig
```

**职责**:
- 模型配置数据结构
- 参数验证和管理
- 配置对象创建

## 设计模式应用

### 1. 工厂模式 (Factory Pattern)

```mermaid
classDiagram
    class TtsConfigFactory {
        <<utility>>
        +getOfflineTtsConfig(): OfflineTtsConfig
        -createVitsConfig(): OfflineTtsVitsModelConfig
        -createMatchaConfig(): OfflineTtsMatchaModelConfig
        -createKokoroConfig(): OfflineTtsKokoroModelConfig
    }
    
    TtsConfigFactory --> OfflineTtsConfig
    TtsConfigFactory --> OfflineTtsVitsModelConfig
    TtsConfigFactory --> OfflineTtsMatchaModelConfig
    TtsConfigFactory --> OfflineTtsKokoroModelConfig
```

**应用场景**: `getOfflineTtsConfig()` 函数根据不同参数创建相应的TTS配置对象。

### 2. 回调模式 (Callback Pattern)

```mermaid
sequenceDiagram
    participant MainActivity
    participant OfflineTts
    participant NativeLib
    
    MainActivity->>OfflineTts: generateWithCallback(callback)
    OfflineTts->>NativeLib: 开始生成音频
    loop 音频块生成
        NativeLib->>MainActivity: callback(samples)
        MainActivity->>MainActivity: 处理音频数据
        MainActivity->>NativeLib: 返回继续标志
    end
    NativeLib->>OfflineTts: 生成完成
    OfflineTts->>MainActivity: 返回结果
```

**应用场景**: 实时音频流处理，允许在音频生成过程中进行实时播放。

### 3. 单例模式 (Singleton Pattern)

```mermaid
classDiagram
    class OfflineTts {
        -ptr: Long
        +allocate()
        +free()
        +finalize()
    }
    
    note for OfflineTts : "通过ptr管理单一的\n本地资源实例"
```

**应用场景**: 每个 `OfflineTts` 实例管理唯一的本地资源指针。

## 数据流架构

### 文本到语音数据流

```mermaid
flowchart LR
    A["用户文本输入"] --> B["参数验证"]
    B --> C["文本预处理"]
    C --> D["模型推理"]
    D --> E["音频后处理"]
    E --> F["实时播放"]
    E --> G["文件保存"]
    
    subgraph "处理层次"
        H["UI层"] --> I["业务层"]
        I --> J["JNI层"]
        J --> K["本地层"]
    end
    
    B -.-> H
    C -.-> I
    D -.-> J
    E -.-> K
```

## 并发架构设计

### 线程模型

```mermaid
graph TD
    subgraph "主线程 (UI Thread)"
        A["UI更新"]
        B["用户交互"]
        C["状态管理"]
    end
    
    subgraph "工作线程 (Worker Thread)"
        D["TTS生成"]
        E["文件操作"]
        F["模型加载"]
    end
    
    subgraph "音频线程 (Audio Thread)"
        G["AudioTrack播放"]
        H["音频回调"]
        I["缓冲区管理"]
    end
    
    A <--> D
    B --> D
    C <--> D
    D --> H
    H --> G
    D --> E
```

### 线程同步机制

```mermaid
sequenceDiagram
    participant UI as 主线程
    participant Worker as 工作线程
    participant Audio as 音频线程
    
    UI->>Worker: 启动TTS生成
    UI->>UI: 禁用UI控件
    Worker->>Audio: 开始音频回调
    loop 音频生成
        Audio->>Audio: 播放音频块
        Audio->>Worker: 回调状态
    end
    Worker->>UI: runOnUiThread()
    UI->>UI: 启用UI控件
```

## 内存管理架构

### 资源生命周期

```mermaid
stateDiagram-v2
    [*] --> Created: new OfflineTts()
    Created --> Initialized: 加载模型
    Initialized --> Active: 开始使用
    Active --> Active: 生成音频
    Active --> Released: free()
    Released --> [*]: finalize()
    
    Active --> Error: 异常发生
    Error --> Released: 清理资源
```

### JNI内存管理

```mermaid
flowchart TD
    A["Java对象创建"] --> B["JNI本地对象创建"]
    B --> C["本地内存分配"]
    C --> D["使用阶段"]
    D --> E{"对象销毁?"}
    E -->|是| F["调用finalize()"]
    E -->|否| D
    F --> G["释放本地内存"]
    G --> H["清理JNI引用"]
    H --> I["垃圾回收"]
```

## 错误处理架构

### 异常处理层次

```mermaid
flowchart TD
    A["用户输入错误"] --> B["UI层验证"]
    C["业务逻辑错误"] --> D["业务层处理"]
    E["JNI调用错误"] --> F["JNI层处理"]
    G["本地库错误"] --> H["本地层处理"]
    
    B --> I["Toast提示"]
    D --> J["Log记录"]
    F --> K["异常抛出"]
    H --> L["错误码返回"]
    
    I --> M["用户重试"]
    J --> M
    K --> M
    L --> M
```

## 配置管理架构

### 配置层次结构

```mermaid
graph TD
    A["应用配置"] --> B["TTS配置"]
    B --> C["模型配置"]
    C --> D["VITS配置"]
    C --> E["Matcha配置"]
    C --> F["Kokoro配置"]
    
    B --> G["规则配置"]
    G --> H["FST规则"]
    G --> I["FAR规则"]
    
    B --> J["音频配置"]
    J --> K["采样率"]
    J --> L["静音比例"]
```

## 扩展性设计

### 模型扩展架构

```mermaid
classDiagram
    class TtsModelInterface {
        <<interface>>
        +generate(text: String): GeneratedAudio
        +getSampleRate(): Int
        +getNumSpeakers(): Int
    }
    
    class VitsModel {
        +generate(text: String): GeneratedAudio
        +getSampleRate(): Int
        +getNumSpeakers(): Int
    }
    
    class MatchaModel {
        +generate(text: String): GeneratedAudio
        +getSampleRate(): Int
        +getNumSpeakers(): Int
    }
    
    class KokoroModel {
        +generate(text: String): GeneratedAudio
        +getSampleRate(): Int
        +getNumSpeakers(): Int
    }
    
    TtsModelInterface <|-- VitsModel
    TtsModelInterface <|-- MatchaModel
    TtsModelInterface <|-- KokoroModel
```

## 性能优化架构

### 优化策略

```mermaid
mindmap
  root((性能优化))
    内存优化
      对象池
      内存复用
      及时释放
    计算优化
      多线程
      ONNX优化
      CPU亲和性
    IO优化
      异步加载
      缓存机制
      预加载
    音频优化
      缓冲区优化
      实时处理
      低延迟
```

## 总结

该架构设计具有以下特点：

1. **分层清晰**: UI、业务逻辑、数据访问层次分明
2. **模块化**: 各功能模块职责单一，耦合度低
3. **可扩展**: 支持多种TTS模型，易于添加新模型
4. **高性能**: 多线程处理，内存优化，实时音频
5. **健壮性**: 完善的错误处理和资源管理
6. **跨平台**: 基于ONNX的模型支持多平台部署