plugins {
    id 'com.android.application'
}

android {
    namespace 'com.example.asrclient'
    compileSdk 33

    defaultConfig {
        applicationId "com.example.asrclient"
        minSdk 21
        targetSdk 33
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // 启用矢量图形支持
        vectorDrawables.useSupportLibrary = true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    // 解决 AAPT2 问题
    aaptOptions {
        noCompress "tflite"
        noCompress "lite"
    }

    packagingOptions {
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
    }
}

dependencies {
    // Android 核心库
    implementation 'androidx.appcompat:appcompat:1.3.0'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    // WebSocket 客户端
    implementation 'org.java-websocket:Java-WebSocket:1.5.3'

    // JSON 处理
    implementation 'com.google.code.gson:gson:2.10.1'

    // 网络请求
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'

    // 音频处理
    implementation 'androidx.media:media:1.6.0'
}
