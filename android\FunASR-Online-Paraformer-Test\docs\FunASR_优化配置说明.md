# FunASR Paraformer 语音识别优化配置说明

## 问题描述
在使用FunASR Paraformer模型进行实时语音识别时，发现存在**一句话被错误拆分**的问题，影响了语音转文字的连续性和准确性。

## 优化方案

### 1. VAD模型参数优化

#### 问题原因
默认的VAD（语音活动检测）参数过于敏感，容易在语音中的短暂停顿处错误地判断为句子结束。

#### 优化配置
```python
vad_kwargs = {
    "max_end_silence_time": 1200,      # 尾部静音检测时间：800ms → 1200ms
    "speech_noise_thres": 0.8,         # 语音噪声阈值：提高对噪声的容忍度
    "max_start_silence_time": 3000,    # 最大起始静音时间
    "min_speech_time": 300,            # 最小语音时长，避免短音频被误判
}
```

#### 优化效果
- **减少语音提前截断**：增加尾部静音检测时间，避免在自然停顿处错误分割
- **提高噪声容忍度**：减少背景噪声对句子分割的影响
- **过滤短音频**：避免咳嗽、"嗯"等短音频被误识别为独立句子

### 2. Chunk Size 参数优化

#### 问题原因
原始的chunk_size `[10, 50, 10]` 提供的上下文窗口较小，模型缺乏足够的语义信息来判断句子边界。

#### 优化配置
```python
# 原配置：[10, 50, 10] - 总时长3.0s，左右回看各0.6s
# 优化配置：[15, 60, 15] - 总时长3.6s，左右回看各0.9s
chunk_size = [15, 60, 15]
```

#### 优化效果
- **增加上下文信息**：更大的时间窗口提供更多语义上下文
- **提高句子完整性**：模型能更好地理解完整的语义单元
- **减少错误分割**：更多的历史信息帮助模型做出更准确的分割决策

### 3. 预测频率优化

#### 问题原因
过于频繁的预测（每10个片段预测一次）导致模型在不完整的语义片段上做出分割决策。

#### 优化配置
```python
# 原配置：pre_expect = 10  (每600ms预测一次)
# 优化配置：pre_expect = 15 (每900ms预测一次)
pre_expect = 15
```

#### 优化效果
- **减少过度预测**：给模型更多时间收集完整的语义信息
- **提高预测质量**：基于更多音频数据的预测更加准确
- **减少分割频率**：降低在句子中间错误分割的概率

### 4. 智能句子边界检测

#### 问题原因
原始代码缺乏对句子完整性的语义判断，容易在标点符号或不完整语义处分割。

#### 优化配置
```python
# 预测文本过滤
if (预测 and 预测 != 旧预测 and 
    len(预测.strip()) >= 2 and          # 至少2个字符
    not 预测.endswith('，') and          # 避免在逗号处分割
    not 预测.endswith('、')):            # 避免在顿号处分割

# 智能句子边界检测
is_sentence_end = (文字.endswith(('。', '！', '？', '.', '!', '?')) or
                  len(文字) > 10)        # 较长的文字片段认为是完整的
```

#### 优化效果
- **避免标点分割**：不在逗号、顿号等非句末标点处分割
- **语义完整性判断**：基于句子长度和结束标点判断完整性
- **提高分割准确性**：只在真正的句子边界处进行分割

### 5. 音频处理优化

#### 优化配置
```python
# 增加预测条件的严格性
if (len(chunks) < chunk_size[1] and 
    pre_num == pre_expect and 
    queue_in.qsize() < 2 and          # 降低队列阈值，确保实时性
    len(chunks) >= 8):                # 确保有足够的音频数据再进行预测
```

#### 优化效果
- **确保数据充足**：只在有足够音频数据时才进行预测
- **平衡实时性**：在准确性和实时性之间找到最佳平衡点

## 配置对比

| 参数 | 原始配置 | 优化配置 | 改进效果 |
|------|----------|----------|----------|
| max_end_silence_time | 800ms | 1200ms | 减少语音提前截断 |
| chunk_size | [10,50,10] | [15,60,15] | 增加上下文信息 |
| pre_expect | 10 | 15 | 减少过度预测 |
| 句子边界检测 | 无 | 智能检测 | 避免错误分割 |
| 文本过滤 | 无 | 标点过滤 | 提高分割准确性 |

## 使用方法

1. **直接使用优化后的脚本**：
   ```bash
   python src/asr/streaming_paraformer.py
   ```

2. **自定义配置**：
   可以根据具体使用场景调整以下参数：
   - `max_end_silence_time`：根据说话习惯调整静音检测时间
   - `chunk_size`：根据实时性要求调整窗口大小
   - `pre_expect`：根据响应速度需求调整预测频率

## 预期效果

经过优化后，语音识别系统应该能够：

1. **减少句子错误拆分**：完整的句子不会被意外分割
2. **提高识别连续性**：相关的语义内容保持在同一个识别结果中
3. **改善用户体验**：前端显示的文字更加连贯和自然
4. **保持实时性**：在提高准确性的同时保持良好的响应速度

## 注意事项

1. **内存使用**：增大的chunk_size会稍微增加内存使用
2. **延迟影响**：优化后的配置可能会增加50-100ms的延迟
3. **场景适配**：不同的使用场景可能需要微调参数
4. **模型兼容性**：确保使用的FunASR版本支持这些参数

## 故障排除

如果优化后仍有问题：

1. **检查音频质量**：确保输入音频清晰，噪声较少
2. **调整VAD参数**：根据实际环境微调静音检测参数
3. **监控系统性能**：确保系统有足够的计算资源
4. **查看日志输出**：观察识别过程中的详细信息

## 参考资料

- [FunASR官方文档](https://github.com/alibaba-damo-academy/FunASR)
- [Paraformer模型说明](https://modelscope.cn/models/iic/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch)
- [VAD模型参数说明](https://huggingface.co/funasr/fsmn-vad)
