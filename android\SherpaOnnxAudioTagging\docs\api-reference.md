# API 参考文档

## 核心类 API

### AudioTagging

音频标签识别的核心引擎类，负责与底层 ONNX 模型交互。

#### 构造函数

```kotlin
class AudioTagging(
    assetManager: AssetManager? = null,
    config: AudioTaggingConfig
)
```

**参数:**
- `assetManager`: Android AssetManager，用于从 assets 目录加载模型
- `config`: 音频标签配置对象

#### 主要方法

##### createStream()

```kotlin
fun createStream(): OfflineStream
```

创建一个新的音频流处理器。

**返回值:** `OfflineStream` 实例

##### compute()

```kotlin
fun compute(stream: OfflineStream, topK: Int = -1): ArrayList<AudioEvent>
```

对音频流进行推理，返回识别的音频事件。

**参数:**
- `stream`: 包含音频数据的流对象
- `topK`: 返回前K个结果，-1表示使用配置中的默认值

**返回值:** 音频事件列表，按概率降序排列

##### release()

```kotlin
fun release()
```

释放底层资源，防止内存泄漏。

---

### OfflineStream

音频流处理器，用于接收和处理音频数据。

#### 主要方法

##### acceptWaveform()

```kotlin
fun acceptWaveform(samples: FloatArray, sampleRate: Int)
```

向流中添加音频波形数据。

**参数:**
- `samples`: 音频样本数组，范围 [-1.0, 1.0]
- `sampleRate`: 采样率，推荐 16000Hz

##### release()

```kotlin
fun release()
```

释放流资源。

##### use()

```kotlin
fun use(block: (OfflineStream) -> Unit)
```

使用 try-with-resources 模式自动管理资源。

**参数:**
- `block`: 使用流的代码块

---

### Tagger

单例管理器，提供全局访问音频标签器的接口。

#### 属性

##### tagger

```kotlin
val tagger: AudioTagging
```

获取音频标签器实例。

**注意:** 必须先调用 `initTagger()` 初始化。

#### 方法

##### initTagger()

```kotlin
fun initTagger(assetManager: AssetManager? = null, numThreads: Int = 1)
```

初始化音频标签器。

**参数:**
- `assetManager`: Android AssetManager
- `numThreads`: 推理线程数

---

## 数据类 API

### AudioEvent

表示识别出的音频事件。

```kotlin
data class AudioEvent(
    val name: String,      // 事件名称
    val index: Int,        // 事件索引
    val prob: Float        // 概率值 [0.0, 1.0]
)
```

### AudioTaggingConfig

音频标签识别配置。

```kotlin
data class AudioTaggingConfig(
    var model: AudioTaggingModelConfig = AudioTaggingModelConfig(),
    var labels: String = "",           // 标签文件路径
    var topK: Int = 5                  // 返回前K个结果
)
```

### AudioTaggingModelConfig

模型配置。

```kotlin
data class AudioTaggingModelConfig(
    var zipformer: OfflineZipformerAudioTaggingModelConfig = OfflineZipformerAudioTaggingModelConfig(),
    var ced: String = "",              // CED模型路径
    var numThreads: Int = 1,           // 线程数
    var debug: Boolean = false,        // 调试模式
    var provider: String = "cpu"       // 推理提供者
)
```

### OfflineZipformerAudioTaggingModelConfig

Zipformer 模型配置。

```kotlin
data class OfflineZipformerAudioTaggingModelConfig(
    var model: String = ""             // 模型文件路径
)
```

---

## 配置函数 API

### getAudioTaggingConfig()

```kotlin
fun getAudioTaggingConfig(type: Int, numThreads: Int = 1): AudioTaggingConfig?
```

获取预定义的音频标签配置。

**参数:**
- `type`: 模型类型 (0-5)
- `numThreads`: 线程数

**模型类型说明:**
- `0`: sherpa-onnx-zipformer-small-audio-tagging-2024-04-15 (默认)
- `1`: sherpa-onnx-zipformer-audio-tagging-2024-04-09
- `2`: sherpa-onnx-ced-tiny-audio-tagging-2024-04-19
- `3`: sherpa-onnx-ced-mini-audio-tagging-2024-04-19
- `4`: sherpa-onnx-ced-small-audio-tagging-2024-04-19
- `5`: sherpa-onnx-ced-base-audio-tagging-2024-04-19

**返回值:** 配置对象，如果类型无效则返回 null

---

## UI 组件 API

### Home

主界面 Compose 组件。

```kotlin
@Composable
fun Home()
```

### MyApp

应用主体组件。

```kotlin
@Composable
fun MyApp(padding: PaddingValues)
```

### ViewRow

音频事件显示行组件。

```kotlin
@Composable
fun ViewRow(
    modifier: Modifier = Modifier,
    event: AudioEvent
)
```

---

## 工具函数 API

### Flatten()

```kotlin
fun Flatten(sampleList: ArrayList<FloatArray>): FloatArray
```

将多个音频片段合并为单个数组。

**参数:**
- `sampleList`: 音频片段列表

**返回值:** 合并后的音频数组

---

## 使用示例

### 基本使用流程

```kotlin
// 1. 初始化标签器
Tagger.initTagger(assetManager, numThreads = 2)

// 2. 创建音频流
val stream = Tagger.tagger.createStream()

// 3. 添加音频数据
stream.acceptWaveform(audioSamples, 16000)

// 4. 进行推理
val events = Tagger.tagger.compute(stream, topK = 3)

// 5. 处理结果
for (event in events) {
    if (event.prob > 0.5) {
        println("检测到: ${event.name}, 概率: ${event.prob}")
    }
}

// 6. 释放资源
stream.release()
```

### 自动资源管理

```kotlin
// 使用 use 函数自动管理资源
Tagger.tagger.createStream().use { stream ->
    stream.acceptWaveform(audioSamples, 16000)
    val events = Tagger.tagger.compute(stream)
    // 处理结果...
} // 自动调用 release()
```

### 自定义配置

```kotlin
val config = AudioTaggingConfig(
    model = AudioTaggingModelConfig(
        zipformer = OfflineZipformerAudioTaggingModelConfig(
            model = "custom-model/model.onnx"
        ),
        numThreads = 4,
        debug = true
    ),
    labels = "custom-model/labels.csv",
    topK = 10
)

val tagger = AudioTagging(assetManager, config)
```

---

## 错误处理

### 常见异常

1. **模型加载失败**
   - 检查模型文件是否存在于 assets 目录
   - 验证模型文件格式是否正确

2. **权限不足**
   - 确保已获取 RECORD_AUDIO 权限
   - 检查权限请求代码

3. **内存不足**
   - 及时调用 `release()` 释放资源
   - 考虑减少线程数或使用更小的模型

### 最佳实践

1. **资源管理**
   ```kotlin
   // 总是在 finally 块中释放资源
   val stream = tagger.createStream()
   try {
       // 使用 stream
   } finally {
       stream.release()
   }
   ```

2. **线程安全**
   ```kotlin
   // Tagger 是线程安全的单例
   // 但 AudioTagging 实例不是线程安全的
   synchronized(tagger) {
       val events = tagger.compute(stream)
   }
   ```

3. **性能优化**
   ```kotlin
   // 重用 stream 对象而不是频繁创建
   val stream = tagger.createStream()
   // 多次使用 stream...
   stream.release()
   ```