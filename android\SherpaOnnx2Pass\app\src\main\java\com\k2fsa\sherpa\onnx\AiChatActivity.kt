package com.k2fsa.sherpa.onnx

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import kotlinx.coroutines.*

/**
 * AI聊天页面
 * 基于会议记录内容与AI进行智能问答
 */
class AiChatActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "AiChatActivity"
        const val EXTRA_MEETING_RECORD_ID = "meeting_record_id"
        const val EXTRA_MEETING_TITLE = "meeting_title"
        const val EXTRA_MEETING_CONTENT = "meeting_content"
    }

    private lateinit var btnBack: ImageButton
    private lateinit var btnClearChat: ImageButton
    private lateinit var tvMeetingContext: TextView
    private lateinit var rvChatMessages: RecyclerView
    private lateinit var etMessage: EditText
    private lateinit var btnSend: Button
    private lateinit var progressLoading: ProgressBar

    private lateinit var chatAdapter: ChatMessageAdapter
    private lateinit var chatManager: ChatManager
    private lateinit var meetingRecordManager: MeetingRecordManager

    private var meetingRecordId: String = ""
    private var meetingTitle: String = ""
    private var meetingContent: String = ""
    private var isProcessing = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_ai_chat)

        initViews()
        initManagers()
        loadIntentData()
        setupRecyclerView()
        setupInputListeners()
        loadChatHistory()
    }

    private fun initViews() {
        btnBack = findViewById(R.id.btn_back)
        btnClearChat = findViewById(R.id.btn_clear_chat)
        tvMeetingContext = findViewById(R.id.tv_meeting_context)
        rvChatMessages = findViewById(R.id.rv_chat_messages)
        etMessage = findViewById(R.id.et_message)
        btnSend = findViewById(R.id.btn_send)
        progressLoading = findViewById(R.id.progress_loading)

        // 设置点击事件
        btnBack.setOnClickListener { finish() }
        btnClearChat.setOnClickListener { showClearChatDialog() }
        btnSend.setOnClickListener { sendMessage() }
    }

    private fun initManagers() {
        chatManager = ChatManager.getInstance(this)
        meetingRecordManager = MeetingRecordManager.getInstance(this)
    }

    private fun loadIntentData() {
        meetingRecordId = intent.getStringExtra(EXTRA_MEETING_RECORD_ID) ?: ""
        meetingTitle = intent.getStringExtra(EXTRA_MEETING_TITLE) ?: "会议记录"
        meetingContent = intent.getStringExtra(EXTRA_MEETING_CONTENT) ?: ""

        if (meetingRecordId.isEmpty() || meetingContent.isEmpty()) {
            Log.e(TAG, "会议记录ID或内容为空")
            showToast("会议记录数据无效")
            finish()
            return
        }

        tvMeetingContext.text = "基于「$meetingTitle」的内容"
    }

    private fun setupRecyclerView() {
        chatAdapter = ChatMessageAdapter()
        rvChatMessages.apply {
            layoutManager = LinearLayoutManager(this@AiChatActivity)
            adapter = chatAdapter
        }
    }

    private fun setupInputListeners() {
        etMessage.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                btnSend.isEnabled = !s.isNullOrBlank() && !isProcessing
            }
        })
    }

    private fun loadChatHistory() {
        val history = chatManager.getChatHistory(meetingRecordId)
        if (history.isNotEmpty()) {
            chatAdapter.addMessages(history)
            scrollToBottom()
        } else {
            // 添加欢迎消息
            addWelcomeMessage()
        }
    }

    private fun addWelcomeMessage() {
        val welcomeMessage = ChatMessage(
            content = "您好！我是AI助手，可以基于您的会议记录内容回答问题。\n\n您可以询问：\n• 会议的主要内容\n• 重要决策和结论\n• 具体的讨论细节\n• 行动项目和任务分配\n\n请输入您的问题吧！",
            isFromUser = false,
            meetingRecordId = meetingRecordId
        )
        chatAdapter.addMessage(welcomeMessage)
        chatManager.saveChatMessage(welcomeMessage)
        scrollToBottom()
    }

    private fun sendMessage() {
        val messageText = etMessage.text.toString().trim()
        if (messageText.isEmpty() || isProcessing) return

        // 检查LLM是否可用
        if (!LLMManager.isCurrentLLMAvailable(this)) {
            showLLMConfigDialog()
            return
        }

        // 添加用户消息
        val userMessage = ChatMessage(
            content = messageText,
            isFromUser = true,
            meetingRecordId = meetingRecordId
        )
        chatAdapter.addMessage(userMessage)
        chatManager.saveChatMessage(userMessage)
        scrollToBottom()

        // 清空输入框
        etMessage.text.clear()

        // 发送给AI处理
        processAiResponse(messageText)
    }

    private fun processAiResponse(userMessage: String) {
        isProcessing = true
        btnSend.isEnabled = false
        progressLoading.visibility = View.VISIBLE

        CoroutineScope(Dispatchers.IO).launch {
            try {
                val result = LLMManager.chatWithLLM(
                    context = this@AiChatActivity,
                    userMessage = userMessage,
                    meetingContent = meetingContent,
                    chatHistory = chatAdapter.getMessages().takeLast(10) // 传递最近10条消息作为上下文
                )

                runOnUiThread {
                    progressLoading.visibility = View.GONE
                    isProcessing = false
                    btnSend.isEnabled = etMessage.text.isNotBlank()

                    if (result.success) {
                        // 添加AI回复
                        val aiMessage = ChatMessage(
                            content = result.content,
                            isFromUser = false,
                            meetingRecordId = meetingRecordId
                        )
                        chatAdapter.addMessage(aiMessage)
                        chatManager.saveChatMessage(aiMessage)
                        scrollToBottom()
                    } else {
                        Log.e(TAG, "AI回复失败: ${result.error}")
                        showToast("AI回复失败: ${result.error}")
                        
                        // 添加错误消息
                        val errorMessage = ChatMessage(
                            content = "抱歉，我暂时无法回答您的问题。请稍后再试。\n\n错误信息：${result.error}",
                            isFromUser = false,
                            meetingRecordId = meetingRecordId
                        )
                        chatAdapter.addMessage(errorMessage)
                        scrollToBottom()
                    }
                }
            } catch (e: Exception) {
                runOnUiThread {
                    progressLoading.visibility = View.GONE
                    isProcessing = false
                    btnSend.isEnabled = etMessage.text.isNotBlank()
                    
                    Log.e(TAG, "处理AI回复异常", e)
                    showToast("处理失败: ${e.message}")
                    
                    // 添加错误消息
                    val errorMessage = ChatMessage(
                        content = "抱歉，处理您的问题时出现了异常。请检查网络连接后重试。",
                        isFromUser = false,
                        meetingRecordId = meetingRecordId
                    )
                    chatAdapter.addMessage(errorMessage)
                    scrollToBottom()
                }
            }
        }
    }

    private fun scrollToBottom() {
        if (chatAdapter.itemCount > 0) {
            rvChatMessages.smoothScrollToPosition(chatAdapter.itemCount - 1)
        }
    }

    private fun showClearChatDialog() {
        AlertDialog.Builder(this)
            .setTitle("清空聊天记录")
            .setMessage("确定要清空所有聊天记录吗？\n\n此操作不可撤销。")
            .setPositiveButton("清空") { _, _ ->
                clearChatHistory()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun clearChatHistory() {
        chatAdapter.clearMessages()
        chatManager.clearChatHistory(meetingRecordId)
        addWelcomeMessage()
        showToast("聊天记录已清空")
    }

    private fun showLLMConfigDialog() {
        AlertDialog.Builder(this)
            .setTitle("LLM配置")
            .setMessage("当前没有可用的LLM配置。请先在设置中配置API密钥。")
            .setPositiveButton("去设置") { _, _ ->
                // TODO: 跳转到设置页面
                showToast("请在设置中配置LLM API密钥")
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}