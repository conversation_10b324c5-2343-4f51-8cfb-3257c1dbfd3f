package com.k2fsa.sherpa.onnx

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView

/**
 * 聊天消息适配器
 */
class ChatMessageAdapter : RecyclerView.Adapter<ChatMessageAdapter.MessageViewHolder>() {

    private val messages = mutableListOf<ChatMessage>()

    fun addMessage(message: ChatMessage) {
        messages.add(message)
        notifyItemInserted(messages.size - 1)
    }

    fun addMessages(newMessages: List<ChatMessage>) {
        val startPosition = messages.size
        messages.addAll(newMessages)
        notifyItemRangeInserted(startPosition, newMessages.size)
    }

    fun clearMessages() {
        val size = messages.size
        messages.clear()
        notifyItemRangeRemoved(0, size)
    }

    fun getMessages(): List<ChatMessage> = messages.toList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MessageViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_chat_message, parent, false)
        return MessageViewHolder(view)
    }

    override fun onBindViewHolder(holder: MessageViewHolder, position: Int) {
        holder.bind(messages[position])
    }

    override fun getItemCount(): Int = messages.size

    class MessageViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val layoutUserMessage: LinearLayout = itemView.findViewById(R.id.layout_user_message)
        private val layoutAiMessage: LinearLayout = itemView.findViewById(R.id.layout_ai_message)
        private val tvUserMessage: TextView = itemView.findViewById(R.id.tv_user_message)
        private val tvAiMessage: TextView = itemView.findViewById(R.id.tv_ai_message)
        private val tvTimestamp: TextView = itemView.findViewById(R.id.tv_timestamp)

        fun bind(message: ChatMessage) {
            if (message.isFromUser) {
                // 显示用户消息
                layoutUserMessage.visibility = View.VISIBLE
                layoutAiMessage.visibility = View.GONE
                tvUserMessage.text = message.content
            } else {
                // 显示AI消息
                layoutUserMessage.visibility = View.GONE
                layoutAiMessage.visibility = View.VISIBLE
                tvAiMessage.text = message.content
            }

            // 显示时间戳（可选）
            if (adapterPosition == 0 || shouldShowTimestamp(message, adapterPosition)) {
                tvTimestamp.visibility = View.VISIBLE
                tvTimestamp.text = message.getFormattedTimestamp()
            } else {
                tvTimestamp.visibility = View.GONE
            }
        }

        private fun shouldShowTimestamp(message: ChatMessage, position: Int): Boolean {
            // 如果是第一条消息，显示时间戳
            if (position == 0) return true
            
            // 如果与前一条消息间隔超过5分钟，显示时间戳
            val adapter = itemView.parent as? RecyclerView
            val previousMessage = (adapter?.adapter as? ChatMessageAdapter)?.messages?.getOrNull(position - 1)
            return if (previousMessage != null) {
                message.timestamp - previousMessage.timestamp > 5 * 60 * 1000 // 5分钟
            } else {
                true
            }
        }
    }
}