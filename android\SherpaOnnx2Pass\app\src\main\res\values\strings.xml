<resources>
    <!-- 原有字符串 -->
    <string name="app_name">ASR2pass </string>
    <string name="hint">Click the Start button to play speech-to-text with Next-gen <PERSON><PERSON><PERSON>.
        \n
        \n\n\n
        The source code and pre-trained models are publicly available.
        Please see https://github.com/k2-fsa/sherpa-onnx for details.
        \n\n
        Two-pass speech recognition with Next-gen <PERSON><PERSON><PERSON>.
    </string>
    <string name="start">Start</string>
    <string name="stop">Stop</string>

    <!-- Apple-style Voice Assistant Strings -->

    <string name="subtitle_local_asr">智能语音识别</string>

    <!-- Status Messages -->
    <string name="status_ready">准备就绪</string>
    <string name="status_initializing">正在初始化...</string>
    <string name="status_recording">录音中...</string>
    <string name="status_processing">AI 正在处理...</string>
    <string name="status_error">出现错误</string>

    <!-- Recording States -->
    <string name="recording_tap_to_start">点击开始录音</string>
    <string name="recording_tap_to_stop">点击停止录音</string>
    <string name="recording_listening">正在聆听...</string>

    <!-- Actions -->
    <string name="action_smart_summary">💡 智能总结</string>
    <string name="action_clear">清空</string>
    <string name="action_settings">设置</string>
    <string name="action_optimize">优化内容</string>

    <!-- Hints -->
    <string name="hint_realtime_preview">实时识别结果将在这里显示...</string>
    <string name="hint_transcription_results">转录结果将在这里显示...</string>

    <!-- Counters -->
    <string name="word_count_format">%d 字</string>
    <string name="speaker_count_format">%d 人</string>

    <!-- Accessibility -->
    <string name="content_desc_record_button">录音按钮</string>
    <string name="content_desc_settings_button">设置按钮</string>
    <string name="content_desc_waveform">音频波形</string>

    <!-- 状态相关 -->
    <string name="status_title">识别状态</string>
<!--    <string name="status_ready">准备就绪</string>-->
<!--    <string name="status_recording">正在录音...</string>-->
<!--    <string name="status_processing">正在处理...</string>-->
<!--    <string name="status_error">发生错误</string>-->
<!--    <string name="status_initializing">正在初始化...</string>-->

    <!-- 控制相关 -->
    <string name="control_title">录音控制</string>
    <string name="start_recording">开始录音</string>
    <string name="stop_recording">停止录音</string>
    <string name="clear_results">清空结果</string>
    <string name="save_results">保存结果</string>

    <!-- 结果相关 -->
    <string name="recognition_results">识别结果</string>
    <string name="results_hint">点击开始录音按钮开始语音识别...</string>
    <string name="word_count_zero">0 字</string>
<!--    <string name="word_count_format">%d 字</string>-->
    <string name="preview_hint">实时预览: </string>

    <!-- 统计信息 -->
    <string name="session_time">录音时长</string>
    <string name="recognition_count">识别次数</string>

    <!-- 消息提示 -->
    <string name="recording_started">开始录音</string>
    <string name="recording_stopped">录音已停止</string>
    <string name="results_cleared">结果已清空</string>
    <string name="results_saved">结果已保存</string>
    <string name="permission_required">需要录音权限</string>
    <string name="initialization_failed">初始化失败</string>
    <string name="microphone_error">麦克风初始化失败</string>

    <!-- 时间格式 -->
    <string name="time_format" formatted="false">%02d:%02d</string>
    <string name="timestamp_format">[%s] </string>
</resources>