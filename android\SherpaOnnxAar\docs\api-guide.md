# Sherpa-ONNX Android AAR API 使用指南

本文档提供了 Sherpa-ONNX Android AAR 各个功能模块的详细使用方法和示例代码。

## 1. 离线语音识别 (OfflineRecognizer)

### 1.1 基本使用

```kotlin
// 1. 配置模型
val modelConfig = OfflineModelConfig(
    transducer = OfflineTransducerModelConfig(
        encoder = "path/to/encoder.onnx",
        decoder = "path/to/decoder.onnx",
        joiner = "path/to/joiner.onnx"
    ),
    tokens = "path/to/tokens.txt",
    numThreads = 4,
    provider = "cpu"
)

// 2. 配置识别器
val config = OfflineRecognizerConfig(
    featConfig = FeatureConfig(sampleRate = 16000, featureDim = 80),
    modelConfig = modelConfig,
    decodingMethod = "greedy_search"
)

// 3. 创建识别器
val recognizer = OfflineRecognizer(assetManager, config)

// 4. 读取音频文件
val waveData = WaveReader.readWave(assetManager, "test.wav")

// 5. 创建流并识别
val stream = recognizer.createStream()
stream.acceptWaveform(waveData.samples, waveData.sampleRate)
val result = recognizer.decode(stream)

// 6. 获取结果
println("识别结果: ${result.text}")
println("语言: ${result.lang}")
println("情感: ${result.emotion}")

// 7. 释放资源
stream.release()
recognizer.release()
```

### 1.2 使用 Whisper 模型

```kotlin
val modelConfig = OfflineModelConfig(
    whisper = OfflineWhisperModelConfig(
        encoder = "whisper-encoder.onnx",
        decoder = "whisper-decoder.onnx",
        language = "zh", // 中文
        task = "transcribe"
    ),
    tokens = "whisper-tokens.txt"
)
```

### 1.3 热词支持

```kotlin
val config = OfflineRecognizerConfig(
    // ... 其他配置
    hotwordsFile = "hotwords.txt",
    hotwordsScore = 2.0f
)
```

## 2. 在线语音识别 (OnlineRecognizer)

### 2.1 基本使用

```kotlin
// 1. 配置模型
val modelConfig = OnlineModelConfig(
    transducer = OnlineTransducerModelConfig(
        encoder = "online-encoder.onnx",
        decoder = "online-decoder.onnx",
        joiner = "online-joiner.onnx"
    ),
    tokens = "tokens.txt",
    numThreads = 4
)

// 2. 配置端点检测
val endpointConfig = EndpointConfig(
    rule1 = EndpointRule(false, 2.4f, 0.0f),
    rule2 = EndpointRule(true, 1.4f, 0.0f),
    rule3 = EndpointRule(false, 0.0f, 20.0f)
)

// 3. 配置识别器
val config = OnlineRecognizerConfig(
    featConfig = FeatureConfig(sampleRate = 16000),
    modelConfig = modelConfig,
    endpointConfig = endpointConfig,
    enableEndpoint = true
)

// 4. 创建识别器和流
val recognizer = OnlineRecognizer(assetManager, config)
val stream = recognizer.createStream()

// 5. 流式处理音频
while (hasMoreAudio) {
    val audioChunk = getNextAudioChunk() // 获取音频片段
    stream.acceptWaveform(audioChunk, 16000)
    
    if (recognizer.isReady(stream)) {
        val result = recognizer.getResult(stream)
        println("部分结果: ${result.text}")
    }
    
    if (recognizer.isEndpoint(stream)) {
        val finalResult = recognizer.getResult(stream)
        println("最终结果: ${finalResult.text}")
        recognizer.reset(stream)
    }
}

// 6. 处理最后的音频
stream.inputFinished()
val finalResult = recognizer.getResult(stream)
println("最终结果: ${finalResult.text}")

// 7. 释放资源
stream.release()
recognizer.release()
```

### 2.2 实时录音识别示例

```kotlin
class RealtimeRecognition {
    private lateinit var recognizer: OnlineRecognizer
    private lateinit var stream: OnlineStream
    private lateinit var audioRecord: AudioRecord
    
    fun startRecognition() {
        // 初始化识别器
        val config = OnlineRecognizerConfig(/* ... */)
        recognizer = OnlineRecognizer(assetManager, config)
        stream = recognizer.createStream()
        
        // 初始化录音
        val bufferSize = AudioRecord.getMinBufferSize(
            16000, AudioFormat.CHANNEL_IN_MONO, AudioFormat.ENCODING_PCM_16BIT
        )
        audioRecord = AudioRecord(
            MediaRecorder.AudioSource.MIC, 16000,
            AudioFormat.CHANNEL_IN_MONO, AudioFormat.ENCODING_PCM_16BIT, bufferSize
        )
        
        audioRecord.startRecording()
        
        // 录音线程
        Thread {
            val buffer = ShortArray(bufferSize)
            while (isRecording) {
                val readSize = audioRecord.read(buffer, 0, buffer.size)
                if (readSize > 0) {
                    // 转换为 float 数组
                    val floatBuffer = FloatArray(readSize)
                    for (i in 0 until readSize) {
                        floatBuffer[i] = buffer[i] / 32768.0f
                    }
                    
                    stream.acceptWaveform(floatBuffer, 16000)
                    
                    if (recognizer.isReady(stream)) {
                        val result = recognizer.getResult(stream)
                        // 更新 UI 显示部分结果
                        updatePartialResult(result.text)
                    }
                    
                    if (recognizer.isEndpoint(stream)) {
                        val result = recognizer.getResult(stream)
                        // 显示最终结果
                        showFinalResult(result.text)
                        recognizer.reset(stream)
                    }
                }
            }
        }.start()
    }
    
    fun stopRecognition() {
        isRecording = false
        audioRecord.stop()
        audioRecord.release()
        
        stream.inputFinished()
        val finalResult = recognizer.getResult(stream)
        showFinalResult(finalResult.text)
        
        stream.release()
        recognizer.release()
    }
}
```

## 3. 语音合成 (OfflineTts)

### 3.1 基本使用

```kotlin
// 1. 配置 TTS 模型
val modelConfig = OfflineTtsModelConfig(
    vits = OfflineTtsVitsModelConfig(
        model = "vits-model.onnx",
        lexicon = "lexicon.txt",
        tokens = "tokens.txt",
        dataDir = "espeak-ng-data"
    ),
    numThreads = 4,
    provider = "cpu"
)

// 2. 配置 TTS
val config = OfflineTtsConfig(
    model = modelConfig,
    maxNumSentences = 1
)

// 3. 创建 TTS 实例
val tts = OfflineTts(assetManager, config)

// 4. 生成语音
val text = "你好，欢迎使用语音合成功能！"
val audio = tts.generate(text, sid = 0, speed = 1.0f)

// 5. 保存音频文件
audio.save("/sdcard/output.wav")

// 6. 获取音频信息
println("采样率: ${audio.sampleRate}")
println("音频长度: ${audio.samples.size}")
println("说话人数量: ${tts.numSpeakers()}")

// 7. 释放资源
tts.release()
```

### 3.2 多说话人合成

```kotlin
// 获取可用说话人数量
val numSpeakers = tts.numSpeakers()
println("可用说话人数量: $numSpeakers")

// 使用不同说话人
for (speakerId in 0 until numSpeakers) {
    val audio = tts.generate("测试文本", sid = speakerId, speed = 1.0f)
    audio.save("/sdcard/speaker_${speakerId}.wav")
}
```

### 3.3 带回调的语音生成

```kotlin
val callback = object : TtsCallback {
    override fun onProgress(progress: Float) {
        println("生成进度: ${(progress * 100).toInt()}%")
    }
    
    override fun onComplete(audio: GeneratedAudio) {
        println("生成完成")
        audio.save("/sdcard/callback_output.wav")
    }
}

tts.generateWithCallback("长文本内容...", sid = 0, speed = 1.0f, callback)
```

## 4. 语音活动检测 (Vad)

### 4.1 基本使用

```kotlin
// 1. 配置 VAD
val config = VadModelConfig(
    sileroVadModelConfig = SileroVadModelConfig(
        model = "silero_vad.onnx",
        threshold = 0.5f,
        minSilenceDuration = 0.25f,
        minSpeechDuration = 0.25f,
        windowSize = 512
    ),
    sampleRate = 16000,
    numThreads = 1
)

// 2. 创建 VAD 实例
val vad = Vad(assetManager, config)

// 3. 处理音频
val waveData = WaveReader.readWave(assetManager, "audio.wav")
vad.acceptWaveform(waveData.samples)

// 4. 检查是否检测到语音
if (vad.isSpeechDetected()) {
    println("检测到语音活动")
}

// 5. 获取语音段
while (!vad.empty()) {
    val segment = vad.front()
    println("语音段开始位置: ${segment.start}")
    println("语音段长度: ${segment.samples.size}")
    
    // 保存语音段
    val segmentAudio = GeneratedAudio(segment.samples, 16000)
    segmentAudio.save("/sdcard/segment_${segment.start}.wav")
    
    vad.pop()
}

// 6. 释放资源
vad.release()
```

### 4.2 实时 VAD 处理

```kotlin
class RealtimeVad {
    private lateinit var vad: Vad
    private val audioBuffer = mutableListOf<Float>()
    
    fun startVad() {
        val config = VadModelConfig(/* ... */)
        vad = Vad(assetManager, config)
        
        // 处理实时音频流
        while (isProcessing) {
            val audioChunk = getAudioChunk() // 获取音频片段
            vad.acceptWaveform(audioChunk)
            
            // 检查语音活动
            if (vad.isSpeechDetected()) {
                onSpeechDetected()
            }
            
            // 处理检测到的语音段
            while (!vad.empty()) {
                val segment = vad.front()
                processSpeechSegment(segment)
                vad.pop()
            }
        }
    }
    
    private fun onSpeechDetected() {
        println("检测到语音开始")
        // 可以在这里启动语音识别
    }
    
    private fun processSpeechSegment(segment: SpeechSegment) {
        println("处理语音段: 开始=${segment.start}, 长度=${segment.samples.size}")
        // 可以将语音段发送给语音识别器
    }
}
```

## 5. 关键词检测 (KeywordSpotter)

### 5.1 基本使用

```kotlin
// 1. 配置关键词检测器
val modelConfig = OnlineModelConfig(
    transducer = OnlineTransducerModelConfig(
        encoder = "keyword-encoder.onnx",
        decoder = "keyword-decoder.onnx",
        joiner = "keyword-joiner.onnx"
    ),
    tokens = "tokens.txt"
)

val config = KeywordSpotterConfig(
    featConfig = FeatureConfig(sampleRate = 16000),
    modelConfig = modelConfig,
    keywordsFile = "keywords.txt",
    keywordsScore = 1.5f,
    keywordsThreshold = 0.25f
)

// 2. 创建关键词检测器
val spotter = KeywordSpotter(assetManager, config)

// 3. 创建流并指定关键词
val keywords = "你好小助手\n打开音乐\n关闭灯光"
val stream = spotter.createStream(keywords)

// 4. 处理音频
val waveData = WaveReader.readWave(assetManager, "test.wav")
stream.acceptWaveform(waveData.samples, waveData.sampleRate)

// 5. 检测关键词
while (spotter.isReady(stream)) {
    spotter.decode(stream)
    val result = spotter.getResult(stream)
    
    if (result.keyword.isNotEmpty()) {
        println("检测到关键词: ${result.keyword}")
        println("置信度相关的 tokens: ${result.tokens.joinToString(", ")}")
        
        // 处理关键词
        handleKeyword(result.keyword)
        
        // 重置检测器
        spotter.reset(stream)
    }
}

// 6. 释放资源
stream.release()
spotter.release()
```

### 5.2 实时关键词检测

```kotlin
class RealtimeKeywordSpotter {
    private lateinit var spotter: KeywordSpotter
    private lateinit var stream: OnlineStream
    
    fun startSpotting() {
        val config = KeywordSpotterConfig(/* ... */)
        spotter = KeywordSpotter(assetManager, config)
        
        // 定义关键词
        val keywords = """
            小助手
            播放音乐
            停止播放
            调大音量
            调小音量
            打开灯光
            关闭灯光
        """.trimIndent()
        
        stream = spotter.createStream(keywords)
        
        // 处理实时音频
        Thread {
            while (isSpotting) {
                val audioChunk = getAudioChunk()
                stream.acceptWaveform(audioChunk, 16000)
                
                if (spotter.isReady(stream)) {
                    spotter.decode(stream)
                    val result = spotter.getResult(stream)
                    
                    if (result.keyword.isNotEmpty()) {
                        handleDetectedKeyword(result.keyword)
                        spotter.reset(stream)
                    }
                }
            }
        }.start()
    }
    
    private fun handleDetectedKeyword(keyword: String) {
        when (keyword) {
            "小助手" -> activateAssistant()
            "播放音乐" -> playMusic()
            "停止播放" -> stopMusic()
            "调大音量" -> increaseVolume()
            "调小音量" -> decreaseVolume()
            "打开灯光" -> turnOnLight()
            "关闭灯光" -> turnOffLight()
            else -> println("未知关键词: $keyword")
        }
    }
}
```

## 6. 音频文件处理

### 6.1 读取音频文件

```kotlin
// 从 Assets 读取
val waveData1 = WaveReader.readWave(assetManager, "audio.wav")

// 从文件系统读取
val waveData2 = WaveReader.readWave("/sdcard/audio.wav")

// 获取音频信息
println("采样率: ${waveData1.sampleRate}")
println("样本数: ${waveData1.samples.size}")
println("时长: ${waveData1.samples.size.toFloat() / waveData1.sampleRate} 秒")
```

### 6.2 音频格式转换

```kotlin
fun convertSampleRate(samples: FloatArray, fromRate: Int, toRate: Int): FloatArray {
    if (fromRate == toRate) return samples
    
    val ratio = toRate.toFloat() / fromRate
    val newSize = (samples.size * ratio).toInt()
    val result = FloatArray(newSize)
    
    for (i in result.indices) {
        val srcIndex = (i / ratio).toInt()
        if (srcIndex < samples.size) {
            result[i] = samples[srcIndex]
        }
    }
    
    return result
}
```

## 7. 错误处理和最佳实践

### 7.1 资源管理

```kotlin
// 使用 use 扩展函数自动管理资源
OfflineRecognizer(assetManager, config).use { recognizer ->
    recognizer.createStream().use { stream ->
        stream.acceptWaveform(samples, sampleRate)
        val result = recognizer.decode(stream)
        println(result.text)
    }
}
```

### 7.2 异常处理

```kotlin
try {
    val recognizer = OfflineRecognizer(assetManager, config)
    // 使用识别器...
} catch (e: Exception) {
    Log.e("SherpaOnnx", "创建识别器失败", e)
    // 处理错误
}
```

### 7.3 性能优化

```kotlin
// 1. 合理设置线程数
val numThreads = Runtime.getRuntime().availableProcessors().coerceAtMost(4)

// 2. 选择合适的提供者
val provider = if (hasGPU()) "gpu" else "cpu"

// 3. 批量处理音频
val batchSize = 1024
for (i in samples.indices step batchSize) {
    val end = (i + batchSize).coerceAtMost(samples.size)
    val chunk = samples.sliceArray(i until end)
    stream.acceptWaveform(chunk, sampleRate)
}
```

## 8. 完整示例应用

```kotlin
class SherpaOnnxManager {
    private var offlineRecognizer: OfflineRecognizer? = null
    private var onlineRecognizer: OnlineRecognizer? = null
    private var tts: OfflineTts? = null
    private var vad: Vad? = null
    private var keywordSpotter: KeywordSpotter? = null
    
    fun initialize(assetManager: AssetManager) {
        try {
            // 初始化各个组件
            initOfflineRecognizer(assetManager)
            initOnlineRecognizer(assetManager)
            initTts(assetManager)
            initVad(assetManager)
            initKeywordSpotter(assetManager)
        } catch (e: Exception) {
            Log.e("SherpaOnnx", "初始化失败", e)
        }
    }
    
    private fun initOfflineRecognizer(assetManager: AssetManager) {
        val config = OfflineRecognizerConfig(/* ... */)
        offlineRecognizer = OfflineRecognizer(assetManager, config)
    }
    
    // ... 其他初始化方法
    
    fun recognizeFile(audioFile: String): String? {
        return try {
            val waveData = WaveReader.readWave(audioFile)
            offlineRecognizer?.let { recognizer ->
                recognizer.createStream().use { stream ->
                    stream.acceptWaveform(waveData.samples, waveData.sampleRate)
                    recognizer.decode(stream).text
                }
            }
        } catch (e: Exception) {
            Log.e("SherpaOnnx", "识别失败", e)
            null
        }
    }
    
    fun synthesizeText(text: String, outputFile: String): Boolean {
        return try {
            tts?.let { tts ->
                val audio = tts.generate(text)
                audio.save(outputFile)
                true
            } ?: false
        } catch (e: Exception) {
            Log.e("SherpaOnnx", "合成失败", e)
            false
        }
    }
    
    fun release() {
        offlineRecognizer?.release()
        onlineRecognizer?.release()
        tts?.release()
        vad?.release()
        keywordSpotter?.release()
    }
}
```

这个 API 使用指南涵盖了 Sherpa-ONNX Android AAR 的主要功能模块，提供了详细的使用示例和最佳实践。开发者可以根据具体需求选择合适的功能模块进行集成。