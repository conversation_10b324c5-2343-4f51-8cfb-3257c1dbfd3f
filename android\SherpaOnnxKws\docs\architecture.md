# SherpaOnnxKws 架构设计文档

## 整体架构概览

### 架构层次图

```mermaid
graph TB
    subgraph "用户界面层 (UI Layer)"
        A[MainActivity]
        B[activity_main.xml]
        C[资源文件]
    end
    
    subgraph "业务逻辑层 (Business Layer)"
        D[KeywordSpotter]
        E[OnlineStream]
        F[配置管理]
    end
    
    subgraph "数据处理层 (Data Layer)"
        G[AudioRecord]
        H[音频数据转换]
        I[特征提取]
    end
    
    subgraph "本地库层 (Native Layer)"
        J[sherpa-onnx-jni]
        K[ONNX Runtime]
        L[模型文件]
    end
    
    A --> D
    A --> E
    A --> G
    D --> J
    E --> J
    G --> H
    H --> I
    I --> J
    J --> K
    K --> L
```

## 核心架构模式

### 1. MVC架构模式

```mermaid
graph LR
    subgraph "Model (数据模型)"
        M1[KeywordSpotter]
        M2[OnlineStream]
        M3[配置类]
    end
    
    subgraph "View (视图)"
        V1[MainActivity UI]
        V2[布局文件]
        V3[资源文件]
    end
    
    subgraph "Controller (控制器)"
        C1[MainActivity逻辑]
        C2[事件处理]
        C3[线程管理]
    end
    
    V1 --> C1
    C1 --> M1
    M1 --> C1
    C1 --> V1
```

### 2. 观察者模式

应用中的音频处理采用了观察者模式的变体：

```mermaid
sequenceDiagram
    participant AudioRecord as 音频录制
    participant ProcessThread as 处理线程
    participant KeywordSpotter as 关键词检测器
    participant UI as 用户界面
    
    AudioRecord->>ProcessThread: 音频数据就绪
    ProcessThread->>KeywordSpotter: 处理音频数据
    KeywordSpotter->>ProcessThread: 检测结果
    ProcessThread->>UI: 更新界面
```

## 组件设计

### 3. 核心组件关系图

```mermaid
classDiagram
    class MainActivity {
        -kws: KeywordSpotter
        -stream: OnlineStream
        -audioRecord: AudioRecord
        +onCreate()
        +onclick()
        +processSamples()
        +initModel()
        +initMicrophone()
    }
    
    class KeywordSpotter {
        -ptr: Long
        +createStream(keywords: String)
        +decode(stream: OnlineStream)
        +isReady(stream: OnlineStream)
        +getResult(stream: OnlineStream)
        +reset(stream: OnlineStream)
        +release()
    }
    
    class OnlineStream {
        -ptr: Long
        +acceptWaveform(samples: FloatArray, sampleRate: Int)
        +inputFinished()
        +release()
    }
    
    class KeywordSpotterConfig {
        +featConfig: FeatureConfig
        +modelConfig: OnlineModelConfig
        +maxActivePaths: Int
        +keywordsFile: String
        +keywordsScore: Float
        +keywordsThreshold: Float
    }
    
    class FeatureConfig {
        +sampleRate: Int
        +featureDim: Int
        +dither: Float
    }
    
    MainActivity --> KeywordSpotter
    MainActivity --> OnlineStream
    KeywordSpotter --> OnlineStream
    KeywordSpotter --> KeywordSpotterConfig
    KeywordSpotterConfig --> FeatureConfig
```

## 数据流架构

### 4. 数据流向图

```mermaid
flowchart TD
    A[麦克风输入] --> B[AudioRecord]
    B --> C[16位PCM数据]
    C --> D[音频缓冲区]
    D --> E[数据类型转换]
    E --> F[FloatArray]
    F --> G[OnlineStream]
    G --> H[JNI层处理]
    H --> I[特征提取]
    I --> J[ONNX模型推理]
    J --> K[关键词检测结果]
    K --> L[结果回调]
    L --> M[UI线程更新]
    M --> N[用户界面显示]
```

### 5. 内存管理架构

```mermaid
graph TB
    subgraph "Java/Kotlin层"
        A[MainActivity]
        B[KeywordSpotter]
        C[OnlineStream]
        D[配置对象]
    end
    
    subgraph "JNI层"
        E[JNI包装器]
        F[对象指针管理]
    end
    
    subgraph "Native层"
        G[C++对象]
        H[ONNX Runtime]
        I[模型数据]
    end
    
    A --> B
    B --> E
    C --> E
    E --> F
    F --> G
    G --> H
    H --> I
    
    style E fill:#f9f,stroke:#333,stroke-width:2px
    style F fill:#f9f,stroke:#333,stroke-width:2px
```

## 线程架构

### 6. 线程模型

```mermaid
graph TB
    subgraph "主线程 (UI Thread)"
        A[MainActivity]
        B[UI更新]
        C[用户交互]
    end
    
    subgraph "录音线程 (Recording Thread)"
        D[processSamples]
        E[音频数据读取]
        F[数据处理]
        G[检测执行]
    end
    
    subgraph "JNI线程池"
        H[模型推理]
        I[特征提取]
    end
    
    A --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    G --> B
    C --> A
```

### 7. 线程同步机制

```mermaid
sequenceDiagram
    participant UI as 主线程
    participant Record as 录音线程
    participant JNI as JNI层
    
    UI->>Record: 启动录音线程
    Note over Record: @Volatile isRecording
    
    loop 录音循环
        Record->>Record: 读取音频数据
        Record->>JNI: 处理音频
        JNI-->>Record: 返回结果
        Record->>UI: runOnUiThread更新UI
    end
    
    UI->>Record: 停止录音 (设置isRecording=false)
    Record->>UI: 线程结束
```

## 配置管理架构

### 8. 配置层次结构

```mermaid
graph TD
    A[应用配置] --> B[KeywordSpotterConfig]
    B --> C[FeatureConfig]
    B --> D[OnlineModelConfig]
    B --> E[检测参数]
    
    C --> F[采样率配置]
    C --> G[特征维度配置]
    
    D --> H[模型文件路径]
    D --> I[推理参数]
    
    E --> J[阈值设置]
    E --> K[路径数量]
```

### 9. 模型加载架构

```mermaid
flowchart TD
    A[应用启动] --> B[检查Assets目录]
    B --> C[读取模型配置]
    C --> D[创建AssetManager]
    D --> E[JNI层模型加载]
    E --> F[ONNX Runtime初始化]
    F --> G[模型验证]
    G --> H{加载成功?}
    H -->|是| I[模型就绪]
    H -->|否| J[错误处理]
    J --> K[回退策略]
```

## 错误处理架构

### 10. 异常处理层次

```mermaid
graph TB
    subgraph "应用层异常"
        A[权限异常]
        B[UI异常]
        C[配置异常]
    end
    
    subgraph "业务层异常"
        D[模型加载异常]
        E[音频处理异常]
        F[检测异常]
    end
    
    subgraph "系统层异常"
        G[JNI异常]
        H[内存异常]
        I[文件IO异常]
    end
    
    A --> D
    B --> E
    C --> F
    D --> G
    E --> H
    F --> I
```

## 性能优化架构

### 11. 性能优化策略

```mermaid
mindmap
  root((性能优化))
    内存优化
      对象池
      及时释放
      避免内存泄漏
    计算优化
      批处理
      异步处理
      缓存机制
    IO优化
      缓冲区管理
      减少系统调用
      数据预加载
    模型优化
      量化模型
      剪枝优化
      硬件加速
```

### 12. 资源生命周期管理

```mermaid
stateDiagram-v2
    [*] --> 初始化
    初始化 --> 模型加载
    模型加载 --> 就绪状态
    就绪状态 --> 录音中
    录音中 --> 处理中
    处理中 --> 录音中
    录音中 --> 停止录音
    停止录音 --> 就绪状态
    就绪状态 --> 资源释放
    资源释放 --> [*]
    
    处理中 --> 错误状态
    错误状态 --> 就绪状态
```

## 扩展性设计

### 13. 插件化架构

```mermaid
graph TB
    subgraph "核心框架"
        A[KeywordSpotter接口]
        B[配置管理]
        C[生命周期管理]
    end
    
    subgraph "模型插件"
        D[Zipformer模型]
        E[Paraformer模型]
        F[自定义模型]
    end
    
    subgraph "处理插件"
        G[音频预处理]
        H[后处理器]
        I[结果过滤器]
    end
    
    A --> D
    A --> E
    A --> F
    B --> G
    B --> H
    B --> I
```

## 安全架构

### 14. 安全机制

```mermaid
flowchart TD
    A[用户输入] --> B[输入验证]
    B --> C[权限检查]
    C --> D[数据加密]
    D --> E[安全传输]
    E --> F[访问控制]
    F --> G[审计日志]
    G --> H[安全存储]
```

## 总结

SherpaOnnxKws采用了分层架构设计，具有以下特点：

1. **清晰的职责分离**: UI层、业务层、数据层和本地库层各司其职
2. **高内聚低耦合**: 组件间通过接口交互，降低依赖性
3. **线程安全**: 采用合适的同步机制保证多线程安全
4. **资源管理**: 完善的生命周期管理和资源释放机制
5. **错误处理**: 多层次的异常处理和恢复机制
6. **性能优化**: 针对实时音频处理的性能优化策略
7. **可扩展性**: 支持不同模型和处理插件的扩展
8. **安全性**: 完善的权限管理和数据安全机制

这种架构设计确保了应用的稳定性、性能和可维护性，为后续的功能扩展和优化提供了良好的基础。