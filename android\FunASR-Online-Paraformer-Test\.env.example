# ASR Service Environment Configuration
# 复制此文件为 .env 并根据需要修改配置

# ============================================================================
# 应用基础配置
# ============================================================================
ASR_APP_NAME="ASR Service"
ASR_APP_VERSION="1.0.0"
ASR_DEBUG=false

# ============================================================================
# 服务配置
# ============================================================================
ASR_HOST=0.0.0.0
ASR_PORT=8000
ASR_WEBSOCKET_HOST=0.0.0.0
ASR_WEBSOCKET_PORT=8766

# ============================================================================
# CORS配置
# ============================================================================
ASR_CORS_ORIGINS=["*"]

# ============================================================================
# ASR模型配置
# ============================================================================
ASR_ASR_MODEL_PATH="iic/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch"
ASR_VAD_MODEL_PATH="iic/speech_fsmn_vad_zh-cn-16k-common-pytorch"
ASR_PUNC_MODEL_PATH="iic/punc_ct-transformer_zh-cn-common-vocab272727-pytorch"
ASR_SPK_MODEL_PATH="iic/speech_campplus_sv_zh-cn_16k-common"

# ============================================================================
# ASR运行配置
# ============================================================================
ASR_DEFAULT_ASR_CONFIG="balanced"
ASR_ASR_DEVICE="cuda"
ASR_ASR_NCPU=4
ASR_ASR_NGPU=1

# ============================================================================
# 音频配置
# ============================================================================
ASR_DEFAULT_SAMPLE_RATE=16000
ASR_MAX_AUDIO_DURATION=3600

# ============================================================================
# 会话配置
# ============================================================================
ASR_SESSION_TIMEOUT_MINUTES=30
ASR_MAX_SESSIONS_PER_CLIENT=5

# ============================================================================
# 文件上传配置
# ============================================================================
ASR_UPLOAD_DIR="uploads"
ASR_MAX_FILE_SIZE=100
ASR_ALLOWED_AUDIO_FORMATS=["wav", "mp3", "flac", "m4a"]

# ============================================================================
# 日志配置
# ============================================================================
ASR_LOG_LEVEL="INFO"
ASR_LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
