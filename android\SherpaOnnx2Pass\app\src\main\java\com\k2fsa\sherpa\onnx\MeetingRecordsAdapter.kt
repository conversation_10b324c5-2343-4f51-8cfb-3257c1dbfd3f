package com.k2fsa.sherpa.onnx

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView

/**
 * 会议记录列表适配器
 * 采用苹果设计风格的卡片布局
 */
class MeetingRecordsAdapter(
    private val originalRecords: MutableList<MeetingRecord>,
    private val onItemClick: (MeetingRecord) -> Unit
) : RecyclerView.Adapter<MeetingRecordsAdapter.ViewHolder>() {

    private val filteredRecords = mutableListOf<MeetingRecord>()
    
    init {
        filteredRecords.addAll(originalRecords)
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvTitle: TextView = itemView.findViewById(R.id.tv_meeting_title)
        val tvDateTime: TextView = itemView.findViewById(R.id.tv_meeting_datetime)
        val tvPreview: TextView = itemView.findViewById(R.id.tv_meeting_preview)
        val tvDuration: TextView = itemView.findViewById(R.id.tv_meeting_duration)
        val tvWordCount: TextView = itemView.findViewById(R.id.tv_meeting_word_count)
        val tvSpeakerCount: TextView = itemView.findViewById(R.id.tv_meeting_speaker_count)
        val tvContentTypes: TextView = itemView.findViewById(R.id.tv_content_types)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_meeting_record, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val record = filteredRecords[position]
        
        // 设置基本信息
        holder.tvTitle.text = record.title
        holder.tvDateTime.text = record.getFormattedDateTime()
        holder.tvPreview.text = record.getContentPreview()
        holder.tvDuration.text = record.getFormattedDuration()
        holder.tvWordCount.text = "${record.wordCount} 字"
        
        // 设置说话人数量
        if (record.speakerCount > 0) {
            holder.tvSpeakerCount.text = "${record.speakerCount} 人"
            holder.tvSpeakerCount.visibility = View.VISIBLE
        } else {
            holder.tvSpeakerCount.visibility = View.GONE
        }
        
        // 设置内容类型标识
        val contentTypes = mutableListOf<String>()
        if (record.hasAudioFile()) {
            contentTypes.add("🎙️ 录音")
        }
        contentTypes.add("📄 原始")

        // 显示优化状态
        if (record.hasOptimizedContent()) {
            contentTypes.add("✨ 已优化")
        } else {
            contentTypes.add("✨ 可优化")
        }

        // 显示总结状态
        if (record.hasSummaryContent()) {
            contentTypes.add("🤖 已总结")
        } else {
            contentTypes.add("🤖 可总结")
        }

        holder.tvContentTypes.text = contentTypes.joinToString(" • ")
        
        // 设置点击事件
        holder.itemView.setOnClickListener {
            onItemClick(record)
        }
    }

    override fun getItemCount(): Int = filteredRecords.size
    
    /**
     * 更新原始数据
     */
    fun updateData(newRecords: List<MeetingRecord>) {
        originalRecords.clear()
        originalRecords.addAll(newRecords)
        filteredRecords.clear()
        filteredRecords.addAll(newRecords)
        notifyDataSetChanged()
    }
    
    /**
     * 根据关键词过滤记录
     */
    fun filter(query: String) {
        filteredRecords.clear()
        
        if (query.isEmpty()) {
            filteredRecords.addAll(originalRecords)
        } else {
            val lowerQuery = query.lowercase()
            filteredRecords.addAll(
                originalRecords.filter { record ->
                    record.title.lowercase().contains(lowerQuery) ||
                    record.originalContent.lowercase().contains(lowerQuery) ||
                    record.optimizedContent.lowercase().contains(lowerQuery) ||
                    record.summaryContent.lowercase().contains(lowerQuery) ||
                    record.getFormattedDateTime().contains(lowerQuery)
                }
            )
        }
        
        notifyDataSetChanged()
    }
    
    /**
     * 获取当前过滤后的记录数量
     */
    fun getFilteredCount(): Int = filteredRecords.size
    
    /**
     * 获取原始记录数量
     */
    fun getOriginalCount(): Int = originalRecords.size
}
