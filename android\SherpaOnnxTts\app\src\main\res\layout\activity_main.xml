<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <TextView
        android:id="@+id/sid_label_hint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/sid_label"
        android:gravity="center"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />
    <EditText
        android:id="@+id/sid"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginTop="0dp"
        android:hint="@string/sid_hint"
        android:gravity="center"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sid_label_hint" />

    <TextView
        android:id="@+id/speed_label_hint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:text="@string/speed_label"
        android:gravity="center"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/sid"/>
    <EditText
        android:id="@+id/speed"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginTop="0dp"
        android:hint="@string/speed_hint"
        android:gravity="center"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/speed_label_hint" />

    <EditText
        android:id="@+id/text"
        android:inputType="textMultiLine"
        android:lines="8"
        android:minLines="10"
        android:gravity="top|start"
        android:maxLines="30"
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:scrollbars="vertical"
        android:hint="@string/text_hint"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/speed" />

    <Button
        android:id="@+id/generate"
        android:textAllCaps="false"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="4dp"
        android:text="@string/generate"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/text" />

    <Button
        android:id="@+id/play"
        android:textAllCaps="false"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="4dp"
        android:text="@string/play"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/generate" />

    <Button
        android:id="@+id/stop"
        android:textAllCaps="false"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="4dp"
        android:text="@string/stop"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/play" />

</androidx.constraintlayout.widget.ConstraintLayout>