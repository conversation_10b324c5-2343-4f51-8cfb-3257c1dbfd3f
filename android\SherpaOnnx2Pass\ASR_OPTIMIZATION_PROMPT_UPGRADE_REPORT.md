# 🚀 ASR内容优化提示词升级报告

## 🔍 问题分析

### 用户反馈
- **现象**: LLMManager.kt中的ASR内容优化提示词效果不够好
- **需求**: 参考SingleModelActivity.kt中更完善的ASR优化提示词进行改进

### 原有提示词问题
1. **缺少内容分析** - 没有对输入内容进行格式分析
2. **处理策略不够详细** - 缺少具体的处理指导
3. **示例说明不足** - 没有提供清晰的输入输出示例
4. **格式要求不够精确** - 对输出格式的描述不够详细

## 🔧 升级方案

### 升级1: 添加内容格式分析

**新增 `analyzeContentFormat()` 方法**:
```kotlin
private fun analyzeContentFormat(content: String): String {
    val lines = content.split('\n').filter { it.trim().isNotEmpty() }
    val hasSpeakerInfo = lines.any { it.contains(":") && (it.contains("[") || it.contains("说话人")) }
    val hasTimestamp = lines.any { it.matches(Regex(".*\\d{2}:\\d{2}:\\d{2}.*")) }
    val avgLineLength = if (lines.isNotEmpty()) lines.map { it.length }.average() else 0.0
    val fragmentedSentences = lines.count { it.trim().length < 10 }
    
    return buildString {
        append("内容特征：")
        if (hasSpeakerInfo) append("包含说话人标识，")
        if (hasTimestamp) append("包含时间戳，")
        append("共${lines.size}行，")
        append("平均行长度${avgLineLength.toInt()}字符")
        if (fragmentedSentences > lines.size / 3) {
            append("，存在较多碎片化句子")
        }
        append("。")
    }
}
```

**作用**:
- 自动分析输入内容的格式特征
- 为LLM提供精准的内容理解
- 指导后续的优化策略

### 升级2: 增强提示词结构

**新的提示词结构**:
```
1. 基本要求 (6项核心要求)
2. 输出格式要求 (详细的格式规范)
3. 内容分析 (自动生成的格式分析)
4. 处理策略 (具体的操作指导)
5. 示例说明 (清晰的输入输出示例)
6. 原始内容 (待优化的ASR内容)
```

### 升级3: 详细的处理策略

**新增处理策略**:
```
- 将**连续的**同一说话人的多行发言合并为一个自然段落
- 修正语音识别常见错误（同音字、语法错误等）
- 保持口语化特点，不要过度书面化
- 确保段落间逻辑连贯，语义完整
- 严格按照时间顺序，不要重新排列说话人的发言顺序
```

### 升级4: 清晰的示例说明

**新增示例**:
```
原始内容：
[张三] 大家好
[张三] 今天我们讨论项目
[李四] 好的
[李四] 我先汇报
[张三] 等一下
[张三] 我还有补充

正确输出：
[张三]：大家好，今天我们讨论项目。

[李四]：好的，我先汇报。

[张三]：等一下，我还有补充。
```

## 📊 升级效果对比

| 功能 | 升级前 | 升级后 |
|------|--------|--------|
| 内容分析 | ❌ 无 | ✅ 自动分析格式特征 |
| 处理策略 | ⚠️ 简单 | ✅ 详细具体的指导 |
| 示例说明 | ❌ 无 | ✅ 清晰的输入输出示例 |
| 格式要求 | ⚠️ 基础 | ✅ 精确详细的规范 |
| 说话人处理 | ⚠️ 简单 | ✅ 连续发言合并逻辑 |
| 时间顺序 | ❌ 未强调 | ✅ 严格保持时间顺序 |

## 🎯 新提示词特点

### 智能内容分析
```kotlin
**内容分析**：
内容特征：包含说话人标识，共15行，平均行长度12字符，存在较多碎片化句子。
```

### 精确处理策略
- **连续合并**: 只合并连续的相同说话人发言
- **时间顺序**: 严格按照时间顺序，不重新排列
- **口语保持**: 保持口语化特点，不过度书面化
- **错误修正**: 修正同音字、语法错误等常见问题

### 清晰输出格式
- **说话人格式**: `[说话人姓名]：完整的连续发言内容。`
- **段落分隔**: 不同说话人之间用空行分隔
- **技术信息**: 移除时间戳等技术信息
- **碎片整合**: 整合碎片化的句子为连贯段落

## 🧪 测试场景

### 场景1: 多说话人对话
**输入**:
```
乔: 我们来测试一下
乔: 声纹识别怎么样
说话人: 好的
说话人: 测试开始
```

**预期输出**:
```
[乔]：我们来测试一下，声纹识别怎么样。

[说话人]：好的，测试开始。
```

### 场景2: 碎片化句子
**输入**:
```
乔: 今天
乔: 天气
乔: 很好
```

**预期输出**:
```
[乔]：今天天气很好。
```

### 场景3: 同音字错误
**输入**:
```
乔: 这个项目很重要
乔: 我们需要仔细考虑
```

**预期输出**:
```
[乔]：这个项目很重要，我们需要仔细考虑。
```

## 🔧 代码变更总结

### 文件: LLMManager.kt

**变更1**: 升级 `buildAsrOptimizationPrompt()` 方法
```diff
private fun buildAsrOptimizationPrompt(originalContent: String): String {
+   // 分析内容格式，提供更精准的处理策略
+   val formatAnalysis = analyzeContentFormat(originalContent)
+   
    return """
        请对以下语音识别(ASR)内容进行优化和修正，要求：
        // ... 基本要求保持不变 ...
        
+       **内容分析**：
+       $formatAnalysis
+
+       **处理策略**：
+       - 将**连续的**同一说话人的多行发言合并为一个自然段落
+       - 修正语音识别常见错误（同音字、语法错误等）
+       - 保持口语化特点，不要过度书面化
+       - 确保段落间逻辑连贯，语义完整
+       - 严格按照时间顺序，不要重新排列说话人的发言顺序
+
+       **示例说明**：
+       原始内容：
+       [张三] 大家好
+       [张三] 今天我们讨论项目
+       [李四] 好的
+       [李四] 我先汇报
+       [张三] 等一下
+       [张三] 我还有补充
+
+       正确输出：
+       [张三]：大家好，今天我们讨论项目。
+
+       [李四]：好的，我先汇报。
+
+       [张三]：等一下，我还有补充。
        
        原始ASR内容：
        $originalContent
    """.trimIndent()
}
```

**变更2**: 新增 `analyzeContentFormat()` 方法
```diff
+   /**
+    * 分析内容格式，提供精准的处理策略
+    */
+   private fun analyzeContentFormat(content: String): String {
+       val lines = content.split('\n').filter { it.trim().isNotEmpty() }
+       val hasSpeakerInfo = lines.any { it.contains(":") && (it.contains("[") || it.contains("说话人")) }
+       val hasTimestamp = lines.any { it.matches(Regex(".*\\d{2}:\\d{2}:\\d{2}.*")) }
+       val avgLineLength = if (lines.isNotEmpty()) lines.map { it.length }.average() else 0.0
+       val fragmentedSentences = lines.count { it.trim().length < 10 }
+       
+       return buildString {
+           append("内容特征：")
+           if (hasSpeakerInfo) append("包含说话人标识，")
+           if (hasTimestamp) append("包含时间戳，")
+           append("共${lines.size}行，")
+           append("平均行长度${avgLineLength.toInt()}字符")
+           if (fragmentedSentences > lines.size / 3) {
+               append("，存在较多碎片化句子")
+           }
+           append("。")
+       }
+   }
```

## 🚀 编译验证

```bash
./gradlew assembleDebug
# ✅ BUILD SUCCESSFUL in 15s
```

## 📝 总结

### 升级亮点
1. **智能分析**: 自动分析内容格式特征，提供精准指导
2. **详细策略**: 具体的处理策略，避免模糊指令
3. **清晰示例**: 输入输出示例，确保LLM理解正确
4. **格式规范**: 精确的输出格式要求，保证一致性

### 预期效果
- **更准确的说话人合并**: 只合并连续发言，保持对话逻辑
- **更好的错误修正**: 针对语音识别常见错误进行优化
- **更自然的语言**: 保持口语化特点，避免过度书面化
- **更清晰的格式**: 统一的输出格式，提高可读性

**升级时间**: 约30分钟  
**升级文件**: 1个文件  
**新增代码**: 约60行  
**最终状态**: ✅ ASR优化提示词全面升级

现在ASR内容优化功能将提供更好的优化效果，生成更自然、更准确的转录结果！🚀✨
