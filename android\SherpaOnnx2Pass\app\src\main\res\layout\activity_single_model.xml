<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="#f5f5f5">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="单模型ASR引擎测试"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="#333333" />

        <Button
            android:id="@+id/btn_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="返回"
            android:textSize="14sp"
            android:background="@color/text_secondary"
            android:textColor="@android:color/white"
            android:paddingHorizontal="16dp"
            android:paddingVertical="8dp" />

    </LinearLayout>

    <!-- 状态显示 -->
    <TextView
        android:id="@+id/tv_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="初始化中..."
        android:textSize="14sp"
        android:textColor="#666666"
        android:background="#ffffff"
        android:padding="12dp"
        android:layout_marginBottom="16dp"
        android:elevation="2dp" />

    <!-- 控制按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btn_record"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="开始录音"
            android:textSize="16sp"
            android:background="@color/text_secondary"
            android:textColor="@android:color/white"
            android:padding="12dp"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_clear"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="清空结果"
            android:textSize="16sp"
            android:background="@color/text_secondary"
            android:textColor="@android:color/white"
            android:padding="12dp"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- 实时预测显示 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="实时预测"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/tv_preview"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:text=""
        android:textSize="14sp"
        android:textColor="#007acc"
        android:background="#ffffff"
        android:padding="12dp"
        android:gravity="top"
        android:scrollbars="vertical"
        android:layout_marginBottom="16dp"
        android:elevation="2dp"
        android:hint="实时预测结果将在这里显示..." />

    <!-- 最终结果显示 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="识别结果"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:layout_marginBottom="8dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#ffffff"
        android:elevation="2dp">

        <TextView
            android:id="@+id/tv_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text=""
            android:textSize="16sp"
            android:textColor="#333333"
            android:padding="12dp"
            android:gravity="top"
            android:hint="识别结果将在这里显示..."
            android:textIsSelectable="true" />

    </ScrollView>

    <!-- 说明文字 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="💡 单模型模式：使用一个Zipformer模型完成预测和最终识别\n🎯 特点：资源占用更少，响应速度快，适合实时应用"
        android:textSize="12sp"
        android:textColor="#888888"
        android:background="#f0f0f0"
        android:padding="8dp"
        android:layout_marginTop="8dp" />

</LinearLayout>
