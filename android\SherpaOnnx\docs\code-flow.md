# SherpaOnnx Android 代码执行流程分析

## 应用启动流程

### 1. 应用初始化流程图

```mermaid
flowchart TD
    A[应用启动] --> B[MainActivity.onCreate]
    B --> C[请求录音权限]
    C --> D[权限检查]
    D -->|权限被拒绝| E[应用退出]
    D -->|权限通过| F[初始化模型 initModel]
    F --> G[设置UI组件]
    G --> H[等待用户操作]
    
    style A fill:#e1f5fe
    style F fill:#fff3e0
    style H fill:#f3e5f5
```

### 2. 模型初始化详细流程

```mermaid
flowchart TD
    A[initModel 开始] --> B[选择模型类型 type=0]
    B --> C[创建 HomophoneReplacerConfig]
    C --> D[创建 OnlineRecognizerConfig]
    D --> E[设置 FeatureConfig]
    E --> F[设置 ModelConfig]
    F --> G[设置 EndpointConfig]
    G --> H[检查是否使用同音字替换]
    H -->|使用| I[复制资源文件到外部存储]
    H -->|不使用| J[创建 OnlineRecognizer 实例]
    I --> J
    J --> K[加载 sherpa-onnx-jni 库]
    K --> L[模型初始化完成]
    
    style A fill:#e8f5e8
    style L fill:#e8f5e8
    style K fill:#fff3e0
```

## 语音识别主流程

### 3. 录音和识别流程图

```mermaid
flowchart TD
    A[用户点击录音按钮] --> B{当前是否在录音?}
    B -->|否| C[初始化麦克风 initMicrophone]
    B -->|是| D[停止录音]
    
    C --> E[检查录音权限]
    E -->|权限不足| F[请求权限并返回]
    E -->|权限充足| G[创建 AudioRecord 实例]
    G --> H[开始录音 startRecording]
    H --> I[更新UI按钮文本为 停止]
    I --> J[启动录音线程 processSamples]
    
    D --> K[停止 AudioRecord]
    K --> L[释放 AudioRecord 资源]
    L --> M[更新UI按钮文本为 开始 ]
    
    style A fill:#e1f5fe
    style J fill:#fff3e0
    style M fill:#f3e5f5
```

### 4. 音频处理和识别详细流程

```mermaid
flowchart TD
    A[processSamples 开始] --> B[创建识别流 createStream]
    B --> C[设置音频缓冲区 100ms]
    C --> D[进入录音循环]
    
    D --> E[读取音频数据 audioRecord.read]
    E --> F{读取成功?}
    F -->|否| D
    F -->|是| G[转换音频格式 Short -> Float]
    
    G --> H[发送音频到流 acceptWaveform]
    H --> I{识别器准备就绪?}
    I -->|是| J[执行解码 decode]
    I -->|否| K[检查是否为端点]
    J --> K
    
    K --> L{是否为端点?}
    L -->|是| M[处理 Paraformer 尾部填充]
    L -->|否| N[获取识别结果]
    M --> O[重置识别流 reset]
    O --> P[更新结果索引]
    P --> Q[更新UI显示]
    N --> Q
    
    Q --> R{继续录音?}
    R -->|是| D
    R -->|否| S[释放识别流]
    S --> T[processSamples 结束]
    
    style A fill:#e8f5e8
    style T fill:#e8f5e8
    style J fill:#fff3e0
    style Q fill:#f3e5f5
```

## 核心组件交互流程

### 5. 组件间交互关系图

```mermaid
sequenceDiagram
    participant UI as MainActivity
    participant AR as AudioRecord
    participant OR as OnlineRecognizer
    participant OS as OnlineStream
    participant JNI as Native Library
    
    UI->>UI: onCreate()
    UI->>OR: 创建识别器实例
    OR->>JNI: 加载模型
    JNI-->>OR: 模型加载完成
    
    UI->>AR: 初始化麦克风
    UI->>AR: 开始录音
    
    loop 录音循环
        AR->>UI: 音频数据
        UI->>OS: acceptWaveform()
        OS->>JNI: 发送音频数据
        
        UI->>OR: isReady()
        OR->>JNI: 检查状态
        JNI-->>OR: 准备状态
        
        alt 准备就绪
            UI->>OR: decode()
            OR->>JNI: 执行解码
        end
        
        UI->>OR: isEndpoint()
        OR->>JNI: 检查端点
        JNI-->>OR: 端点状态
        
        UI->>OR: getResult()
        OR->>JNI: 获取结果
        JNI-->>OR: 识别文本
        OR-->>UI: 显示结果
        
        alt 检测到端点
            UI->>OR: reset()
            OR->>JNI: 重置状态
        end
    end
    
    UI->>AR: 停止录音
    UI->>OS: release()
    OS->>JNI: 释放资源
```

### 6. 数据流转换过程

```mermaid
flowchart LR
    A[麦克风音频] --> B[AudioRecord]
    B --> C[Short Array 16-bit PCM]
    C --> D[Float Array 归一化]
    D --> E[OnlineStream]
    E --> F[JNI 层处理]
    F --> G[ONNX 模型推理]
    G --> H[识别结果 tokens]
    H --> I[文本后处理]
    I --> J[UI 显示]
    
    style A fill:#e1f5fe
    style G fill:#fff3e0
    style J fill:#f3e5f5
```

## 错误处理和资源管理

### 7. 错误处理流程

```mermaid
flowchart TD
    A[操作开始] --> B{权限检查}
    B -->|失败| C[请求权限]
    C --> D{权限获取结果}
    D -->|拒绝| E[显示错误并退出]
    D -->|通过| F[继续执行]
    B -->|通过| F
    
    F --> G{麦克风初始化}
    G -->|失败| H[记录错误日志]
    H --> I[返回错误状态]
    G -->|成功| J[正常执行流程]
    
    J --> K{模型加载}
    K -->|失败| L[记录错误并退出]
    K -->|成功| M[应用正常运行]
    
    style E fill:#ffebee
    style I fill:#ffebee
    style L fill:#ffebee
    style M fill:#e8f5e8
```

### 8. 资源生命周期管理

```mermaid
stateDiagram-v2
    [*] --> Created: 应用启动
    Created --> Initialized: 模型加载
    Initialized --> Recording: 开始录音
    Recording --> Processing: 音频处理
    Processing --> Recording: 继续录音
    Processing --> Stopped: 停止录音
    Stopped --> Initialized: 准备下次录音
    Recording --> Stopped: 用户停止
    Initialized --> Destroyed: 应用退出
    Stopped --> Destroyed: 应用退出
    Destroyed --> [*]: 资源释放
    
    Recording: AudioRecord 活跃
    Processing: OnlineStream 处理
    Destroyed: 释放所有资源
```

## 性能优化要点

### 9. 性能关键路径

```mermaid
flowchart TD
    A[音频采集 100ms] --> B[格式转换 <1ms]
    B --> C[JNI 调用 <1ms]
    C --> D[ONNX 推理 10-50ms]
    D --> E[结果处理 <1ms]
    E --> F[UI 更新 <5ms]
    
    G[总延迟目标] --> H[< 100ms 实时性]
    
    style D fill:#fff3e0
    style H fill:#e8f5e8
```

## 总结

该 SherpaOnnx Android 应用的代码执行流程具有以下特点：

1. **模块化设计**: 各功能模块职责清晰，便于维护和扩展
2. **实时处理**: 采用流式处理架构，保证低延迟
3. **资源管理**: 完善的生命周期管理和错误处理机制
4. **性能优化**: JNI 集成和原生库调用保证高性能
5. **用户体验**: 简洁的UI设计和实时反馈机制

整个应用的核心是围绕 `OnlineRecognizer` 和 `OnlineStream` 构建的实时语音识别引擎，通过 JNI 调用底层的 ONNX 模型进行高效的语音识别处理。