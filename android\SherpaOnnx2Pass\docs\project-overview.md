# SherpaOnnx2Pass 项目功能模块概述

## 项目简介

SherpaOnnx2Pass 是一个基于 Sherpa-ONNX 的 Android 语音识别应用，采用单模型 ASR 引擎架构，集成了声纹识别、AI 优化和会议总结等功能。

## 主要功能模块

### 1. 核心 ASR 引擎模块

#### SingleModelASREngine
- **功能**: 单模型自动语音识别引擎
- **特点**: 参考 FunASR 的单模型实现，使用一个在线识别器完成预测和最终识别
- **核心能力**:
  - 实时语音识别
  - 预测结果和最终结果输出
  - 端点检测
  - 音频质量控制
  - 智能句子边界检测

#### OnlineRecognizer
- **功能**: 在线语音识别器
- **支持模型类型**:
  - Transducer 模型
  - Paraformer 模型
  - Zipformer2 CTC 模型
  - NeMo CTC 模型
- **配置参数**:
  - 端点检测规则
  - 解码方法
  - 热词支持

### 2. 声纹识别模块

#### SpeakerDataManager
- **功能**: 声纹数据持久化管理
- **核心能力**:
  - 声纹数据保存和加载
  - 声纹数据加密存储
  - 声纹数据管理（增删改查）
  - 应用重启后声纹数据恢复

#### Speaker 相关组件
- **SpeakerEmbeddingExtractor**: 声纹特征提取
- **SpeakerEmbeddingManager**: 声纹管理器
- **说话人识别**: 实时说话人身份识别

### 3. AI 优化模块

#### ApiKeyManager
- **功能**: API 密钥安全管理
- **特点**:
  - 多种密钥来源支持（BuildConfig、加密存储、用户输入）
  - AES 加密存储
  - 密钥优先级管理

#### GeminiConfig
- **功能**: Gemini AI API 配置
- **能力**:
  - ASR 结果自动优化
  - 会议内容总结
  - 智能文本处理

### 4. 用户界面模块

#### SingleModelActivity
- **功能**: 主界面控制器
- **UI 组件**:
  - 录音控制按钮
  - 结果显示区域
  - 状态指示器
  - 统计信息显示
- **交互功能**:
  - 录音开始/停止
  - 结果清空和保存
  - 声纹注册和管理
  - ASR 结果优化
  - 会议总结生成

### 5. 配置管理模块

#### FeatureConfig
- **功能**: 音频特征配置
- **参数**: 采样率、特征维度、抖动参数

#### HomophoneReplacerConfig
- **功能**: 同音词替换配置
- **用途**: 提高识别准确性

## 应用执行流程

### 主要执行流程图

```mermaid
flowchart TD
    A[应用启动] --> B[权限检查]
    B --> C[初始化UI组件]
    C --> D[初始化ASR引擎]
    D --> E{初始化成功?}
    E -->|否| F[显示错误信息]
    E -->|是| G[恢复声纹数据]
    G --> H[准备就绪状态]
    
    H --> I[用户点击录音]
    I --> J[开始录音]
    J --> K[音频数据采集]
    K --> L[音频数据处理]
    L --> M[ASR识别]
    M --> N[声纹识别]
    N --> O[结果输出]
    O --> P{继续录音?}
    P -->|是| K
    P -->|否| Q[停止录音]
    
    Q --> R[结果后处理]
    R --> S{自动优化?}
    S -->|是| T[AI优化]
    S -->|否| U[显示最终结果]
    T --> U
    
    U --> V[用户操作选择]
    V --> W[保存结果]
    V --> X[清空结果]
    V --> Y[声纹管理]
    V --> Z[会议总结]
    
    F --> AA[退出应用]
    W --> H
    X --> H
    Y --> H
    Z --> H
```

### ASR 引擎处理流程

```mermaid
flowchart TD
    A[音频输入] --> B[音频分段]
    B --> C[音频缓冲区]
    C --> D[预测计数器检查]
    D --> E{达到预测间隔?}
    E -->|否| F[继续缓冲]
    E -->|是| G[执行预测识别]
    G --> H[生成预测结果]
    H --> I[智能边界检测]
    I --> J{达到最终识别条件?}
    J -->|否| K[输出预测结果]
    J -->|是| L[执行最终识别]
    L --> M[生成最终结果]
    M --> N[端点检测]
    N --> O{检测到端点?}
    O -->|否| P[继续识别]
    O -->|是| Q[输出最终结果]
    
    K --> F
    P --> F
    Q --> R[重置状态]
    R --> S[等待新输入]
```

### 声纹识别流程

```mermaid
flowchart TD
    A[音频输入] --> B[特征提取]
    B --> C[生成声纹向量]
    C --> D[与已注册声纹比较]
    D --> E{相似度 > 阈值?}
    E -->|是| F[识别成功]
    E -->|否| G[未知说话人]
    F --> H[返回说话人信息]
    G --> I[返回未知标识]
    
    J[声纹注册] --> K[录制音频样本]
    K --> L[提取特征向量]
    L --> M[保存到本地存储]
    M --> N[更新声纹数据库]
    
    O[声纹管理] --> P[查看已注册声纹]
    O --> Q[删除声纹]
    O --> R[清空所有声纹]
```

### AI 优化流程

```mermaid
flowchart TD
    A[ASR识别结果] --> B{API密钥配置?}
    B -->|否| C[跳过优化]
    B -->|是| D[内容格式分析]
    D --> E[检测说话人标识]
    D --> F[检测时间戳]
    D --> G[检测多行内容]
    
    E --> H[构建优化请求]
    F --> H
    G --> H
    
    H --> I[调用Gemini API]
    I --> J{API调用成功?}
    J -->|否| K[保持原始结果]
    J -->|是| L[解析优化结果]
    L --> M[更新显示内容]
    M --> N[更新统计信息]
    
    C --> O[显示原始结果]
    K --> O
    N --> P[优化完成]
```

## 技术特点

### 1. 单模型架构
- 简化了传统双模型（预测+最终）的复杂性
- 参考 FunASR 的 AutoModel 设计
- 统一的识别流程，降低延迟

### 2. 智能缓冲管理
- 基于 chunk 的音频处理
- 动态预测间隔调整
- 音频质量评估和控制

### 3. 声纹持久化
- 本地加密存储
- 应用重启后自动恢复
- 支持多用户声纹管理

### 4. AI 增强功能
- 集成 Gemini AI 进行结果优化
- 智能会议总结生成
- 安全的 API 密钥管理

### 5. 用户体验优化
- 实时预览和最终结果分离显示
- 丰富的状态反馈
- 直观的操作界面

## 性能优化

### 1. 内存管理
- 音频缓冲区大小控制
- 及时释放不需要的资源
- 避免内存泄漏

### 2. 计算优化
- CPU 提供商配置
- 多线程处理
- 智能端点检测减少无效计算

### 3. 网络优化
- API 调用失败处理
- 网络状态检查
- 异步处理避免 UI 阻塞

## 扩展性设计

### 1. 模型支持
- 支持多种 ONNX 模型格式
- 可配置的模型参数
- 热词和规则支持

### 2. 功能扩展
- 模块化的组件设计
- 清晰的接口定义
- 易于添加新功能

### 3. 平台适配
- Android 平台优化
- 权限管理
- 生命周期管理