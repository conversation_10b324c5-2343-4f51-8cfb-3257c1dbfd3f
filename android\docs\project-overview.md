# Sherpa-ONNX Android 项目功能模块总览

本文档总结了 Sherpa-ONNX Android 项目中的主要功能模块，并提供了各模块的代码执行流程图。

## 项目概述

Sherpa-ONNX Android 是一个基于 ONNX Runtime 的语音处理工具集，提供了多种语音相关的功能模块，包括语音识别、语音合成、关键词检测、说话人识别等。

## 主要功能模块

### 1. 核心库模块 (SherpaOnnxAar)
**功能**: 提供核心的语音处理功能库
- 离线语音识别 (OfflineRecognizer)
- 在线流式语音识别 (OnlineRecognizer)
- 语音合成 (OfflineTts)
- 特征提取和音频处理

### 2. 语音识别模块

#### SherpaOnnx - 流式语音识别
**功能**: 实时流式语音识别
- 支持实时音频流处理
- 端点检测
- 多语言支持

#### SherpaOnnx2Pass - 双通道语音识别
**功能**: 两阶段语音识别，提高准确性
- 第一阶段：流式识别
- 第二阶段：非流式精确识别

#### SherpaOnnxSimulateStreamingAsr - 模拟流式识别
**功能**: 使用非流式模型进行流式语音识别
- VAD (Voice Activity Detection) 集成
- 音频分段处理

#### SherpaOnnxVadAsr - VAD + ASR
**功能**: 语音活动检测与语音识别结合
- 自动检测语音段
- 减少无效音频处理

### 3. 语音合成模块

#### SherpaOnnxTts - 独立语音合成
**功能**: 文本转语音应用
- 多语言语音合成
- 音色选择
- 语速控制

#### SherpaOnnxTtsEngine - TTS 引擎
**功能**: 系统级 TTS 引擎
- 替换系统默认 TTS
- 支持第三方应用调用
- 电子书阅读器集成

### 4. 关键词检测模块

#### SherpaOnnxKws - 关键词唤醒
**功能**: 实时关键词检测和唤醒
- 自定义关键词
- 低功耗检测
- 实时响应

### 5. 说话人相关模块

#### SherpaOnnxSpeakerIdentification - 说话人识别
**功能**: 识别和验证说话人身份
- 说话人注册
- 实时识别
- 特征向量管理

#### SherpaOnnxSpeakerDiarization - 说话人分离
**功能**: 多说话人音频分离和标记
- 自动检测说话人数量
- 时间段标记
- 聚类分析

### 6. 音频分析模块

#### SherpaOnnxAudioTagging - 音频标记
**功能**: 音频内容分类和标记
- 环境声音识别
- 音乐类型分类
- 事件检测

#### SherpaOnnxAudioTaggingWearOs - 穿戴设备音频标记
**功能**: 适配 WearOS 的音频标记
- 低功耗优化
- 简化界面
- 实时处理

### 7. 语言识别模块

#### SherpaOnnxSpokenLanguageIdentification - 语言识别
**功能**: 自动识别音频中的语言
- 多语言检测
- 置信度评分
- 实时识别

### 8. 其他模块

#### SherpaOnnxVad - 语音活动检测
**功能**: 检测音频中的语音活动
- 静音检测
- 语音段分割
- 噪声过滤

#### SherpaOnnxWebSocket - WebSocket 客户端
**功能**: 与服务器进行实时语音通信
- 流式音频传输
- 实时识别结果
- 网络通信

#### SherpaOnnxJavaDemo - Java 示例
**功能**: Java API 使用示例
- 基础功能演示
- API 调用示例
- 集成指南

## 整体架构流程图

```mermaid
graph TB
    subgraph "应用层 (Application Layer)"
        A[Android Applications]
        B[MainActivity]
        C[UI Components]
    end
    
    subgraph "业务逻辑层 (Business Logic Layer)"
        D[Audio Processing]
        E[Model Management]
        F[Configuration]
    end
    
    subgraph "核心引擎层 (Core Engine Layer)"
        G[SherpaOnnx AAR]
        H[OfflineRecognizer]
        I[OnlineRecognizer]
        J[OfflineTts]
        K[KeywordSpotter]
        L[SpeakerEmbedding]
    end
    
    subgraph "JNI 桥接层 (JNI Bridge Layer)"
        M[JNI Interface]
        N[Native C++ Code]
    end
    
    subgraph "AI 模型层 (AI Model Layer)"
        O[ONNX Runtime]
        P[ASR Models]
        Q[TTS Models]
        R[Speaker Models]
        S[Audio Tagging Models]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    G --> I
    G --> J
    G --> K
    G --> L
    H --> M
    I --> M
    J --> M
    K --> M
    L --> M
    M --> N
    N --> O
    O --> P
    O --> Q
    O --> R
    O --> S
```

## 通用代码执行流程

### 应用启动流程

```mermaid
flowchart TD
    A[应用启动] --> B[MainActivity.onCreate]
    B --> C[权限检查]
    C --> D{录音权限?}
    D -->|已授权| E[初始化模型]
    D -->|未授权| F[请求权限]
    F --> G{用户响应}
    G -->|同意| E
    G -->|拒绝| H[显示错误退出]
    E --> I[加载 ONNX 模型]
    I --> J[创建核心实例]
    J --> K[初始化 UI]
    K --> L[应用就绪]
```

### 音频处理通用流程

```mermaid
flowchart TD
    A[用户触发录音] --> B[创建 AudioRecord]
    B --> C[配置音频参数]
    C --> D[开始录音线程]
    D --> E[读取音频缓冲区]
    E --> F[PCM 数据转换]
    F --> G[数据预处理]
    G --> H[发送到处理引擎]
    H --> I[AI 模型推理]
    I --> J[后处理结果]
    J --> K[更新 UI 显示]
    K --> L{继续录音?}
    L -->|是| E
    L -->|否| M[停止录音]
    M --> N[释放资源]
```

### 模型推理通用流程

```mermaid
flowchart TD
    A[音频数据输入] --> B[特征提取]
    B --> C[数据归一化]
    C --> D[创建输入张量]
    D --> E[ONNX 模型推理]
    E --> F[输出张量解析]
    F --> G[后处理算法]
    G --> H[置信度计算]
    H --> I[结果过滤]
    I --> J[格式化输出]
    J --> K[返回最终结果]
```

## 技术特点

### 1. 跨平台兼容性
- 基于 ONNX Runtime，支持多种硬件加速
- 统一的 API 接口设计
- 模块化架构，便于集成

### 2. 性能优化
- JNI 桥接，减少数据拷贝
- 流式处理，降低延迟
- 内存管理优化

### 3. 易用性
- 丰富的示例代码
- 详细的文档说明
- 灵活的配置选项

### 4. 扩展性
- 插件化模型加载
- 自定义配置支持
- 开放的 API 接口

## 开发指南

### 集成步骤
1. 添加 AAR 依赖
2. 配置权限和资源
3. 初始化相应模块
4. 实现音频处理逻辑
5. 处理识别结果

### 最佳实践
- 合理管理模型资源
- 优化音频采集参数
- 实现错误处理机制
- 注意内存泄漏防护

## 总结

Sherpa-ONNX Android 项目提供了完整的语音处理解决方案，涵盖了从基础的语音识别到高级的说话人分析等多个领域。通过模块化的设计和统一的架构，开发者可以根据需求选择合适的功能模块，快速构建语音相关的 Android 应用。