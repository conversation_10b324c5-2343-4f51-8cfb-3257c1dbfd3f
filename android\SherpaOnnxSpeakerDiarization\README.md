# SherpaOnnx Speaker Diarization Android App

基于 Sherpa-ONNX 框架的 Android 说话人分离应用，支持离线音频处理和多说话人识别。

## 🎯 项目概述

这是一个使用先进深度学习技术的说话人分离应用，能够自动识别音频文件中的不同说话人并标记其发言时间段。应用采用完全离线处理方式，保护用户隐私的同时提供高精度的识别结果。

### 主要特性

- 🔒 **完全离线**: 无需网络连接，保护隐私安全
- 🎯 **高精度识别**: 基于 pyannote-audio 和 3D-Speaker 模型
- 📱 **现代化界面**: Material Design 3 设计语言
- ⚡ **实时进度**: 处理进度可视化显示
- 📋 **结果导出**: 支持复制结果到剪贴板
- 🔧 **参数可调**: 支持自定义说话人数量和聚类阈值

## 🏗️ 技术架构

```mermaid
graph TB
    subgraph "前端层"
        A[Jetpack Compose UI]
        B[Material Design 3]
        C[Navigation Component]
    end
    
    subgraph "业务逻辑层"
        D[SpeakerDiarizationObject]
        E[音频文件处理]
        F[参数配置管理]
    end
    
    subgraph "核心引擎层"
        G[OfflineSpeakerDiarization]
        H[JNI 接口]
    end
    
    subgraph "原生层"
        I[Sherpa-ONNX C++]
        J[ONNX Runtime]
    end
    
    subgraph "AI 模型"
        K[说话人分割模型]
        L[说话人嵌入模型]
    end
    
    A --> D
    B --> A
    C --> A
    D --> G
    E --> G
    F --> G
    G --> H
    H --> I
    I --> J
    J --> K
    J --> L
```

## 📋 系统要求

### 硬件要求
- **最低内存**: 4GB RAM
- **推荐内存**: 6GB RAM 或更高
- **存储空间**: 至少 100MB 可用空间
- **处理器**: ARM64 或 x86_64 架构

### 软件要求
- **Android 版本**: Android 5.0 (API 21) 或更高
- **目标版本**: Android 14 (API 34)
- **Kotlin 版本**: 1.9.0+
- **Gradle 版本**: 8.0+

### 音频格式要求
- **格式**: WAV 文件
- **采样率**: 16kHz
- **位深**: 16-bit
- **声道**: 单声道 (Mono)
- **编码**: PCM

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/k2-fsa/sherpa-onnx.git
cd sherpa-onnx/android/SherpaOnnxSpeakerDiarization
```

### 2. 准备模型文件

下载并放置必需的模型文件到 `app/src/main/assets/` 目录：

#### 说话人分割模型
```bash
# 下载分割模型
wget https://github.com/k2-fsa/sherpa-onnx/releases/download/speaker-segmentation-models/sherpa-onnx-pyannote-segmentation-3-0.tar.bz2

# 解压并重命名
tar -xjf sherpa-onnx-pyannote-segmentation-3-0.tar.bz2
mv sherpa-onnx-pyannote-segmentation-3-0/model.onnx app/src/main/assets/segmentation.onnx
```

#### 说话人嵌入模型
```bash
# 下载嵌入模型
wget https://github.com/k2-fsa/sherpa-onnx/releases/download/speaker-recongition-models/3dspeaker_speech_eres2net_base_sv_zh-cn_3dspeaker_16k.onnx

# 重命名并移动
mv 3dspeaker_speech_eres2net_base_sv_zh-cn_3dspeaker_16k.onnx app/src/main/assets/embedding.onnx
```

### 3. 构建项目

```bash
# 使用 Gradle 构建
./gradlew assembleDebug

# 或者在 Android Studio 中打开项目并构建
```

### 4. 安装和运行

```bash
# 安装到连接的设备
./gradlew installDebug

# 或者直接安装 APK
adb install app/build/outputs/apk/debug/app-debug.apk
```

## 📖 使用指南

### 基本使用流程

1. **选择音频文件**: 点击"选择文件"按钮，选择符合格式要求的 WAV 文件
2. **设置参数**: 
   - 说话人数量（0 表示自动检测）
   - 聚类阈值（0.0-1.0，默认 0.5）
3. **开始处理**: 点击"开始分析"按钮
4. **查看结果**: 处理完成后查看说话人分离结果
5. **导出结果**: 点击"复制结果"将结果复制到剪贴板

### 参数调优指南

| 场景 | 说话人数量 | 聚类阈值 | 说明 |
|------|-----------|----------|------|
| 已知说话人数 | 具体数字 | 0.5 | 最准确的设置 |
| 未知说话人数 | 0 或 -1 | 0.4-0.6 | 根据音频特点调整 |
| 声音相似 | 0 | 0.3-0.4 | 更敏感的检测 |
| 噪音较多 | 0 | 0.6-0.7 | 更保守的检测 |

## 📁 项目结构

```
SherpaOnnxSpeakerDiarization/
├── app/
│   ├── src/main/
│   │   ├── java/com/k2fsa/sherpa/onnx/speaker/diarization/
│   │   │   ├── MainActivity.kt                 # 主活动
│   │   │   ├── SpeakerDiarizationObject.kt     # 单例管理器
│   │   │   ├── OfflineSpeakerDiarization.kt    # 核心引擎
│   │   │   ├── ReadWaveFile.kt                 # 音频文件读取
│   │   │   ├── screens/
│   │   │   │   ├── Home.kt                     # 主屏幕
│   │   │   │   └── Help.kt                     # 帮助屏幕
│   │   │   └── ui/theme/                       # UI 主题
│   │   ├── assets/                             # 模型文件目录
│   │   │   ├── segmentation.onnx               # 分割模型
│   │   │   └── embedding.onnx                  # 嵌入模型
│   │   └── AndroidManifest.xml
│   └── build.gradle.kts
├── docs/                                       # 项目文档
│   ├── project-overview.md                     # 项目概述
│   ├── architecture.md                         # 架构设计
│   ├── api-reference.md                        # API 参考
│   └── user-guide.md                           # 用户指南
├── build.gradle.kts
├── settings.gradle.kts
└── README.md
```

## 🔧 开发指南

### 环境配置

1. **Android Studio**: Arctic Fox 或更高版本
2. **JDK**: JDK 8 或更高版本
3. **Android SDK**: API 21-34
4. **NDK**: 如需修改原生代码

### 构建配置

```kotlin
// app/build.gradle.kts
android {
    compileSdk = 34
    
    defaultConfig {
        minSdk = 21
        targetSdk = 34
    }
    
    buildFeatures {
        compose = true
    }
    
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.1"
    }
}
```

### 依赖管理

主要依赖项：
- Jetpack Compose
- Material Design 3
- Navigation Compose
- DocumentFile
- Kotlin Coroutines

### 代码规范

- 遵循 Kotlin 官方编码规范
- 使用 Material Design 3 组件
- 采用 MVVM 架构模式
- 单例模式管理全局状态

## 🧪 测试

### 运行测试

```bash
# 运行单元测试
./gradlew test

# 运行 UI 测试
./gradlew connectedAndroidTest
```

### 测试覆盖

- 单元测试：核心业务逻辑
- UI 测试：用户界面交互
- 集成测试：端到端流程

## 📊 性能优化

### 内存管理
- 及时释放大型音频数据
- 使用单例模式避免重复初始化
- 合理使用 Kotlin 协程

### 处理优化
- 后台线程执行音频处理
- 进度回调避免 UI 阻塞
- 分块处理大型音频文件

## 🤝 贡献指南

### 如何贡献

1. **Fork 项目**
2. **创建功能分支**: `git checkout -b feature/amazing-feature`
3. **提交更改**: `git commit -m 'Add amazing feature'`
4. **推送分支**: `git push origin feature/amazing-feature`
5. **创建 Pull Request**

### 贡献类型

- 🐛 Bug 修复
- ✨ 新功能开发
- 📚 文档改进
- 🎨 UI/UX 优化
- ⚡ 性能优化
- 🧪 测试覆盖

### 代码审查

所有 Pull Request 都需要经过代码审查：
- 代码质量检查
- 功能测试验证
- 性能影响评估
- 文档更新确认

## 📄 许可证

本项目采用 [Apache License 2.0](LICENSE) 许可证。

## 🙏 致谢

### 核心技术
- [Sherpa-ONNX](https://github.com/k2-fsa/sherpa-onnx): 语音处理框架
- [pyannote-audio](https://github.com/pyannote/pyannote-audio): 说话人分割模型
- [3D-Speaker](https://github.com/modelscope/3D-Speaker): 说话人嵌入模型

### 开发框架
- [Jetpack Compose](https://developer.android.com/jetpack/compose): 现代 Android UI 工具包
- [Material Design 3](https://m3.material.io/): Google 设计系统
- [ONNX Runtime](https://onnxruntime.ai/): 跨平台机器学习推理

## 📞 联系方式

- **项目主页**: [sherpa-onnx](https://github.com/k2-fsa/sherpa-onnx)
- **问题反馈**: [GitHub Issues](https://github.com/k2-fsa/sherpa-onnx/issues)
- **技术讨论**: [GitHub Discussions](https://github.com/k2-fsa/sherpa-onnx/discussions)

## 📈 路线图

### 当前版本 (v1.0)
- ✅ 基础说话人分离功能
- ✅ Material Design 3 界面
- ✅ 离线处理能力
- ✅ 参数可调节

### 计划功能
- [ ] 支持更多音频格式 (MP3, M4A, FLAC)
- [ ] 实时说话人分离
- [ ] 批量处理功能
- [ ] 结果导出多种格式 (JSON, CSV, SRT)
- [ ] 云端模型更新
- [ ] 多语言支持
- [ ] 性能监控和分析

---

**⚠️ 重要提示**: 本应用仅用于学习和研究目的。请确保您有权处理所使用的音频文件，并遵守相关的隐私和版权法律法规。