# SherpaOnnx Audio Tagging Wear OS 项目文档

## 项目概述

SherpaOnnx Audio Tagging Wear OS 是一个基于 Android Wear OS 平台的实时音频标记应用。该应用使用 Sherpa-ONNX 框架和深度学习模型来识别和分类环境音频，为用户提供实时的音频事件检测功能。

## 主要功能模块

### 1. 核心模块

#### 1.1 MainActivity
- **文件**: `MainActivity.kt`
- **功能**: 应用主入口，负责权限管理、初始化和界面设置
- **关键职责**:
  - 音频录制权限请求和验证
  - Tagger 模块初始化
  - 保持屏幕常亮
  - 启动画面管理

#### 1.2 AudioTagging 引擎
- **文件**: `AudioTagging.kt`
- **功能**: 音频标记的核心引擎，封装 ONNX 模型推理
- **关键职责**:
  - 音频标记模型配置和加载
  - 音频流处理
  - 音频事件识别和分类
  - 支持多种预训练模型（Zipformer、CED 系列）

#### 1.3 Tagger 单例管理器
- **文件**: `Tagger.kt`
- **功能**: AudioTagging 实例的单例管理器
- **关键职责**:
  - 全局 AudioTagging 实例管理
  - 线程安全的初始化
  - 模型配置管理

#### 1.4 OfflineStream 音频流处理
- **文件**: `OfflineStream.kt`
- **功能**: 音频数据流的处理和管理
- **关键职责**:
  - 音频波形数据接收
  - 内存管理和资源释放
  - JNI 接口封装

### 2. 用户界面模块

#### 2.1 HomeScreen 主界面
- **文件**: `HomeScreen.kt`
- **功能**: 应用的主要用户界面
- **关键职责**:
  - 音频录制控制（开始/停止）
  - 实时显示识别结果
  - 阈值调节滑块
  - 音频数据采集和处理

### 3. 支持模块

#### 3.1 主题系统
- **目录**: `theme/`
- **功能**: Wear OS 适配的 UI 主题

#### 3.2 JNI 本地库
- **目录**: `jniLibs/`
- **功能**: 包含不同架构的 Sherpa-ONNX 本地库
- **支持架构**: arm64-v8a, armeabi-v7a, x86, x86_64

#### 3.3 模型资源
- **目录**: `assets/`
- **功能**: 存储预训练的音频标记模型文件

## 技术特性

### 音频处理
- **采样率**: 16kHz
- **音频格式**: 16-bit PCM 单声道
- **处理间隔**: 100ms
- **缓冲区管理**: 动态音频缓冲区

### 模型支持
- **Zipformer 模型**: 小型和标准版本
- **CED 模型**: Tiny、Mini、Small、Base 多个版本
- **量化支持**: INT8 量化模型以提高性能
- **多线程**: 支持多线程推理加速

### Wear OS 适配
- **圆形屏幕支持**: 适配 Wear OS 圆形显示屏
- **触控优化**: 针对小屏幕的触控界面
- **电池优化**: 智能资源管理
- **独立运行**: 无需配对手机即可独立运行

## 权限要求

- `RECORD_AUDIO`: 音频录制权限
- `WAKE_LOCK`: 保持设备唤醒权限
- `android.hardware.type.watch`: Wear OS 设备特性

## 依赖库

- **Wear Compose**: Wear OS 的 Compose UI 框架
- **Activity Compose**: Activity 与 Compose 集成
- **Core Splashscreen**: 启动画面支持
- **Material3**: Material Design 3 组件
- **Play Services Wearable**: Wear OS 服务支持