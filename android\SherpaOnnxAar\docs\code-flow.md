# Sherpa-ONNX Android AAR 代码执行流程

本文档详细描述了 Sherpa-ONNX Android AAR 项目中各个功能模块的代码执行流程。

## 1. 离线语音识别流程

### 1.1 初始化流程

```mermaid
flowchart TD
    A[创建 OfflineRecognizer] --> B{是否使用 AssetManager?}
    B -->|是| C[newFromAsset]
    B -->|否| D[newFromFile]
    C --> E[加载模型配置]
    D --> E
    E --> F[初始化特征提取器]
    F --> G[加载模型文件]
    G --> H[创建解码器]
    H --> I[初始化热词]
    I --> J[OfflineRecognizer 就绪]
```

### 1.2 识别流程

```mermaid
flowchart TD
    A[创建 OfflineStream] --> B[acceptWaveform]
    B --> C[音频预处理]
    C --> D[特征提取]
    D --> E[模型推理]
    E --> F[解码处理]
    F --> G[后处理]
    G --> H[生成识别结果]
    H --> I[返回 OfflineRecognizerResult]
    I --> J[包含文本、tokens、时间戳等]
```

## 2. 在线语音识别流程

### 2.1 初始化流程

```mermaid
flowchart TD
    A[创建 OnlineRecognizer] --> B{是否使用 AssetManager?}
    B -->|是| C[newFromAsset]
    B -->|否| D[newFromFile]
    C --> E[加载模型配置]
    D --> E
    E --> F[初始化特征提取器]
    F --> G[加载模型文件]
    G --> H[创建流式解码器]
    H --> I[配置端点检测]
    I --> J[初始化语言模型]
    J --> K[OnlineRecognizer 就绪]
```

### 2.2 流式识别流程

```mermaid
flowchart TD
    A[创建 OnlineStream] --> B[acceptWaveform]
    B --> C[音频缓冲]
    C --> D[实时特征提取]
    D --> E[流式模型推理]
    E --> F[部分结果解码]
    F --> G{检测到端点?}
    G -->|否| H[继续接收音频]
    G -->|是| I[最终解码]
    H --> B
    I --> J[生成最终结果]
    J --> K[返回 OnlineRecognizerResult]
```

## 3. 语音合成流程

### 3.1 TTS 初始化流程

```mermaid
flowchart TD
    A[创建 OfflineTts] --> B{是否使用 AssetManager?}
    B -->|是| C[newFromAsset]
    B -->|否| D[newFromFile]
    C --> E[加载 TTS 配置]
    D --> E
    E --> F[初始化声学模型]
    F --> G[加载声码器]
    G --> H[加载词典和tokens]
    H --> I[初始化文本处理器]
    I --> J[OfflineTts 就绪]
```

### 3.2 语音生成流程

```mermaid
flowchart TD
    A[输入文本] --> B[文本预处理]
    B --> C[文本规范化]
    C --> D[分词处理]
    D --> E[音素转换]
    E --> F[声学模型推理]
    F --> G[生成梅尔频谱]
    G --> H[声码器处理]
    H --> I[生成音频波形]
    I --> J[后处理和采样率转换]
    J --> K[返回 GeneratedAudio]
```

## 4. 语音活动检测流程

### 4.1 VAD 初始化流程

```mermaid
flowchart TD
    A[创建 Vad] --> B{是否使用 AssetManager?}
    B -->|是| C[newFromAsset]
    B -->|否| D[newFromFile]
    C --> E[加载 VAD 配置]
    D --> E
    E --> F[初始化 Silero VAD 模型]
    F --> G[设置检测参数]
    G --> H[配置窗口大小]
    H --> I[Vad 就绪]
```

### 4.2 语音检测流程

```mermaid
flowchart TD
    A[acceptWaveform] --> B[音频缓冲]
    B --> C[窗口分帧]
    C --> D[VAD 模型推理]
    D --> E[概率计算]
    E --> F{概率 > 阈值?}
    F -->|是| G[标记为语音]
    F -->|否| H[标记为静音]
    G --> I[更新语音段]
    H --> J[更新静音段]
    I --> K[检查段长度]
    J --> K
    K --> L{满足分割条件?}
    L -->|是| M[生成 SpeechSegment]
    L -->|否| N[继续累积]
    M --> O[添加到队列]
    N --> A
```

## 5. 关键词检测流程

### 5.1 关键词检测初始化

```mermaid
flowchart TD
    A[创建 KeywordSpotter] --> B{是否使用 AssetManager?}
    B -->|是| C[newFromAsset]
    B -->|否| D[newFromFile]
    C --> E[加载关键词配置]
    D --> E
    E --> F[加载在线模型]
    F --> G[读取关键词文件]
    G --> H[构建关键词图]
    H --> I[设置检测参数]
    I --> J[KeywordSpotter 就绪]
```

### 5.2 关键词检测流程

```mermaid
flowchart TD
    A[创建 OnlineStream] --> B[acceptWaveform]
    B --> C[特征提取]
    C --> D[模型推理]
    D --> E[关键词匹配]
    E --> F{匹配到关键词?}
    F -->|否| G[继续检测]
    F -->|是| H[计算置信度]
    G --> B
    H --> I{置信度 > 阈值?}
    I -->|否| G
    I -->|是| J[生成检测结果]
    J --> K[返回 KeywordSpotterResult]
```

## 6. 音频处理流程

### 6.1 音频读取流程

```mermaid
flowchart TD
    A[WaveReader.readWave] --> B{从 Asset 读取?}
    B -->|是| C[readWaveFromAsset]
    B -->|否| D[readWaveFromFile]
    C --> E[解析 WAV 头]
    D --> E
    E --> F[读取音频数据]
    F --> G[格式转换]
    G --> H[采样率检查]
    H --> I[返回 WaveData]
```

### 6.2 特征提取流程

```mermaid
flowchart TD
    A[音频输入] --> B[预加重]
    B --> C[分帧]
    C --> D[加窗]
    D --> E[FFT 变换]
    E --> F[梅尔滤波器组]
    F --> G[对数变换]
    G --> H[特征归一化]
    H --> I[返回特征向量]
```

## 7. 整体系统架构流程

```mermaid
flowchart TD
    A[Android 应用] --> B[Sherpa-ONNX AAR]
    B --> C[Kotlin/Java API 层]
    C --> D[JNI 接口层]
    D --> E[C++ 核心库]
    E --> F[ONNX Runtime]
    F --> G[模型文件]
    
    C --> H[OfflineRecognizer]
    C --> I[OnlineRecognizer]
    C --> J[OfflineTts]
    C --> K[Vad]
    C --> L[KeywordSpotter]
    C --> M[其他功能模块]
    
    H --> N[离线识别模型]
    I --> O[在线识别模型]
    J --> P[TTS 模型]
    K --> Q[VAD 模型]
    L --> R[关键词模型]
```

## 8. 资源管理流程

```mermaid
flowchart TD
    A[对象创建] --> B[分配 native 指针]
    B --> C[初始化 C++ 对象]
    C --> D[使用对象]
    D --> E{手动调用 release?}
    E -->|是| F[delete native 对象]
    E -->|否| G[finalize 方法]
    G --> H{指针有效?}
    H -->|是| F
    H -->|否| I[跳过清理]
    F --> J[设置指针为 0]
    J --> K[资源释放完成]
    I --> K
```

## 9. 错误处理流程

```mermaid
flowchart TD
    A[API 调用] --> B{参数验证}
    B -->|失败| C[抛出异常]
    B -->|成功| D[JNI 调用]
    D --> E{C++ 执行}
    E -->|异常| F[捕获 C++ 异常]
    E -->|成功| G[返回结果]
    F --> H[转换为 Java 异常]
    H --> I[抛出 Java 异常]
    C --> J[异常处理]
    I --> J
    G --> K[正常返回]
```

## 总结

Sherpa-ONNX Android AAR 项目采用分层架构设计：

1. **应用层**: Android 应用通过 AAR 包调用功能
2. **API 层**: Kotlin/Java 接口，提供易用的 API
3. **JNI 层**: 连接 Java 和 C++ 的桥梁
4. **核心层**: C++ 实现的核心算法和 ONNX 模型推理
5. **模型层**: 各种预训练的 ONNX 模型文件

每个功能模块都遵循相似的初始化-使用-释放的生命周期，通过统一的配置系统和资源管理机制确保系统的稳定性和性能。