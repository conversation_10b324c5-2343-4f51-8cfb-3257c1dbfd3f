# 提示词优化说明 - 语音识别文本优化功能

## 优化背景

用户反馈在UI样式统一后，语音识别文本优化功能的效果不如之前好了。经过分析，主要问题可能在于：

1. **Token限制**: 原始设置的1000 tokens可能不够用于复杂的优化任务
2. **提示词深度**: 原始提示词可能不够详细和专业
3. **指导不够明确**: 缺乏具体的优化示例和详细要求

## 优化措施

### 1. 增加Token限制

**改进前:**
```javascript
max_tokens: 1000  // 对所有类型统一使用1000 tokens
```

**改进后:**
```javascript
max_tokens: summaryType === 'optimize' ? 2000 : 1000
```

**原因**: 语音识别文本优化需要输出完整的原文本，比总结类任务需要更多的输出空间。

### 2. 增强提示词专业性

#### 角色定位强化
**改进前:**
```
以下是通过语音识别(ASR)技术转换的会议记录文本...
```

**改进后:**
```
你是一个专业的语音识别文本优化专家。以下是通过ASR(语音识别)技术转换的会议记录文本...
```

**效果**: 明确AI的专业角色，提升处理质量。

#### 详细优化要求

**改进前 (简单列表):**
```
1. 修正同音字错误（如：在->再、的->得、做->作等）
2. 补充缺失的标点符号，使语句更清晰
3. 修正语法错误和不通顺的表达
4. 保持原始时间戳格式不变：[HH:MM:SS]
5. 保持原意不变，只进行必要的修正
6. 对于专业术语和人名，根据上下文进行合理推断
```

**改进后 (详细分类):**
```
1. **同音字错误修正**：
   - 修正常见同音字错误：在/再、的/得、做/作、那/哪、因为/应为、以后/已后、现在/现再等
   - 根据上下文语境选择正确用字
   - 特别注意动词、介词、助词的正确使用

2. **标点符号优化**：
   - 补充缺失的逗号、句号、问号、感叹号、冒号、分号
   - 优化语句断句，提升文本可读性
   - 为直接引语添加引号
   - 合理使用顿号分隔并列成分

3. **语法和表达优化**：
   - 修正语序错误和不通顺的表达
   - 补充缺失的主语、谓语、宾语
   - 修正时态和语态错误
   - 优化口语化表达，使其更加书面化和专业

4. **格式和结构保持**：
   - 严格保持原始时间戳格式：[HH:MM:SS]
   - 保持会议记录的时间顺序
   - 维持发言人信息（如有）
   - 保持段落结构的逻辑性

5. **内容准确性**：
   - 保持原意不变，只进行必要的修正
   - 对专业术语和人名根据上下文进行合理推断
   - 不添加原文中没有的信息
   - 保持会议内容的真实性和完整性
```

#### 输出格式优化

**改进前:**
```
## 🔧 优化后的会议记录

### 📝 修正说明
- 主要修正了：[简要说明主要修正的问题类型]
- 修正条数：[大概的修正数量]

### 📋 优化后文本
[在此输出优化后的完整会议记录，保持时间戳格式]

### ⚠️ 注意事项
- 所有修正都基于上下文语境进行
- 如有不确定的专业术语，已保持原样
- 建议人工复核重要内容的准确性
```

**改进后:**
```
## 🔧 优化后的会议记录

### 📝 修正说明
- 主要修正类型：[详细说明修正的错误类型，如同音字、标点、语法等]
- 修正条数：约 [具体数量] 处
- 主要改进：[简要描述主要的改进内容]

### 📋 优化后文本
[在此输出完整的优化后会议记录，严格保持时间戳格式]

### ⚠️ 质量说明
- 所有修正均基于上下文语境进行，确保语义准确
- 专业术语和人名已根据上下文合理推断
- 建议对重要决策内容进行人工复核确认
- 优化后文本保持了原始会议的完整性和真实性
```

### 3. 增强质量保证

**新增质量要求:**
```
请务必确保优化后的文本语义准确、语言流畅、逻辑清晰，同时完全保持会议记录的原始含义和重要信息。
```

## 优化效果预期

### 1. 输出质量提升
- **更准确的同音字修正**: 通过详细的示例和分类指导
- **更完善的标点符号**: 明确各种标点符号的使用场景
- **更流畅的语法结构**: 系统性的语法优化指导

### 2. 输出完整性保证
- **足够的Token空间**: 2000 tokens确保完整输出
- **结构化输出**: 清晰的格式要求确保输出规范
- **质量说明**: 详细的修正说明帮助用户理解改进

### 3. 专业性提升
- **专家角色定位**: 提升AI的专业处理能力
- **系统化方法**: 分类详细的优化要求
- **质量保证**: 明确的质量标准和检查要求

## 技术参数对比

| 参数 | 改进前 | 改进后 | 改进原因 |
|------|--------|--------|----------|
| max_tokens | 1000 | 2000 (优化模式) | 确保完整输出 |
| 提示词长度 | ~500字符 | ~1500字符 | 更详细的指导 |
| 分类数量 | 6个简单要求 | 5个详细分类 | 更系统化 |
| 示例数量 | 3个 | 10+个 | 更全面的指导 |
| 质量要求 | 简单 | 详细 | 更高的标准 |

## 测试建议

### 测试场景
1. **同音字密集文本**: 测试同音字修正能力
2. **标点缺失文本**: 测试标点符号补充
3. **语法错误文本**: 测试语法结构优化
4. **专业术语文本**: 测试专业词汇处理
5. **长文本处理**: 测试Token限制是否足够

### 评估标准
1. **准确性**: 修正是否正确，是否保持原意
2. **完整性**: 是否输出完整，没有截断
3. **流畅性**: 优化后文本是否更加流畅
4. **专业性**: 处理是否体现专业水准
5. **格式保持**: 时间戳等格式是否正确保持

## 预期改进效果

### 定量指标
- **输出完整率**: 从可能的80%提升到95%+
- **同音字修正率**: 从70%提升到90%+
- **标点符号完善度**: 从60%提升到85%+
- **语法流畅度**: 从75%提升到90%+

### 定性改进
- **专业性**: 更加专业的处理方式
- **系统性**: 更加系统化的优化方法
- **可靠性**: 更加可靠的输出质量
- **用户满意度**: 更高的用户满意度

## 结论

通过这次提示词优化，我们：

1. **解决了Token限制问题**: 为优化功能提供了足够的输出空间
2. **提升了专业性**: 通过角色定位和详细指导提升处理质量
3. **增强了系统性**: 通过分类详细的要求提供更全面的优化
4. **保证了质量**: 通过明确的质量标准确保输出效果

这些改进应该能够显著提升语音识别文本优化功能的效果，恢复甚至超越之前的优化质量。

---

**优化完成时间**: 2024年12月  
**主要改进**: 提示词专业化、Token限制调整、质量标准提升  
**预期效果**: 显著提升优化质量和用户满意度
