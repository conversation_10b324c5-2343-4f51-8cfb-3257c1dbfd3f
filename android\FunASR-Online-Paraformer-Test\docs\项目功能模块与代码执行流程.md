# ASR Service 项目功能模块与代码执行流程

## 项目概述

ASR Service 是一个基于 FastAPI 和 FunASR 的现代化实时语音识别服务，提供 WebSocket 和 REST API 双协议支持，具备多客户端接入能力和完整的会话管理功能。

## 主要功能模块

### 1. 核心服务模块

#### 1.1 FastAPI 应用核心 (`app/main.py`)
- **功能**: 应用入口点，路由注册，中间件配置
- **职责**: 
  - 创建 FastAPI 应用实例
  - 配置 CORS 中间件
  - 注册各模块路由
  - 提供静态文件服务
  - 请求日志记录

#### 1.2 配置管理 (`app/core/config.py`)
- **功能**: 基于 Pydantic 的配置系统
- **职责**:
  - 环境变量管理
  - 服务配置（端口、地址等）
  - ASR 模型路径配置
  - WebSocket 配置

### 2. API 接口模块

#### 2.1 WebSocket API (`app/api/websocket.py`)
- **功能**: 实时音频流处理
- **职责**:
  - WebSocket 连接管理
  - 实时音频数据接收
  - 音频识别结果推送
  - 连接状态管理

#### 2.2 REST API (`app/api/rest.py`)
- **功能**: HTTP 接口服务
- **职责**:
  - 会话管理接口
  - 配置管理接口
  - 音频文件上传处理
  - 健康检查接口

#### 2.3 LLM API (`app/api/llm_routes.py`)
- **功能**: 大语言模型集成
- **职责**:
  - LLM 提供商管理
  - 文本处理请求
  - 流式响应支持

#### 2.4 会议总结 API (`app/api/meeting_summary.py`)
- **功能**: 会议记录智能总结
- **职责**:
  - 会议文本总结
  - 多种总结类型支持
  - 行动项提取

### 3. 业务服务模块

#### 3.1 ASR 服务 (`app/services/asr_service.py`)
- **功能**: 语音识别服务封装
- **职责**:
  - ASR 引擎管理
  - 音频数据处理
  - 识别结果格式化
  - 多配置支持

#### 3.2 会话管理 (`app/services/session_manager.py`)
- **功能**: 会话生命周期管理
- **职责**:
  - 会话创建和销毁
  - 会话状态跟踪
  - 客户端会话映射
  - 过期会话清理

### 4. ASR 引擎模块

#### 4.1 WebSocket ASR (`src/asr/websocket_asr.py`)
- **功能**: 专门处理 WebSocket 音频流的 ASR 引擎
- **职责**:
  - FunASR 模型集成
  - 音频缓冲管理
  - 实时识别处理
  - 结果优化

#### 4.2 ASR 配置 (`src/config/asr_config.py`)
- **功能**: ASR 引擎配置管理
- **职责**:
  - VAD 参数配置
  - 不同场景配置
  - 识别精度优化

### 5. 数据模型模块

#### 5.1 Pydantic 模型 (`app/models/schemas.py`)
- **功能**: API 数据结构定义
- **职责**:
  - 请求响应模型
  - 数据验证
  - 类型安全

### 6. LLM 集成模块

#### 6.1 LLM 管理器 (`app/llm/`)
- **功能**: 大语言模型集成框架
- **职责**:
  - 多提供商支持
  - 统一接口封装
  - 错误处理

## 代码执行流程

### 1. 系统启动流程

```mermaid
flowchart TD
    A[运行 run_fastapi.py] --> B[检查依赖]
    B --> C[创建 FastAPI 应用]
    C --> D[加载配置]
    D --> E[注册中间件]
    E --> F[注册路由]
    F --> G[挂载静态文件]
    G --> H[启动 Uvicorn 服务器]
    H --> I[服务就绪]
```

### 2. WebSocket 实时识别流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant WS as WebSocket API
    participant SM as 会话管理器
    participant ASR as ASR服务
    participant Engine as ASR引擎

    Client->>WS: 建立 WebSocket 连接
    WS->>SM: 创建会话
    SM->>ASR: 创建 ASR 引擎实例
    ASR->>Engine: 初始化 FunASR 模型
    WS-->>Client: 连接成功，返回会话ID

    loop 音频流处理
        Client->>WS: 发送音频数据
        WS->>ASR: 处理音频块
        ASR->>Engine: 调用识别引擎
        Engine-->>ASR: 返回识别结果
        ASR-->>WS: 格式化结果
        WS-->>Client: 推送识别结果
    end

    Client->>WS: 断开连接
    WS->>SM: 清理会话
    SM->>ASR: 销毁 ASR 引擎
```

### 3. REST API 会话管理流程

```mermaid
flowchart TD
    A[客户端请求] --> B{请求类型}
    
    B -->|创建会话| C[POST /api/v1/sessions]
    C --> D[验证请求参数]
    D --> E[会话管理器创建会话]
    E --> F[生成会话ID]
    F --> G[返回会话信息和WebSocket URL]
    
    B -->|获取会话| H[GET /api/v1/sessions/id]
    H --> I[查询会话信息]
    I --> J[返回会话详情]
    
    B -->|上传音频| K[POST /api/v1/audio/upload]
    K --> L[保存音频文件]
    L --> M[调用ASR服务处理]
    M --> N[返回识别结果]
    
    B -->|获取配置| O[GET /api/v1/configs]
    O --> P[返回可用配置列表]
```

### 4. ASR 音频处理流程

```mermaid
flowchart TD
    A[接收音频数据] --> B[音频格式转换]
    B --> C[添加到音频缓冲区]
    C --> D{缓冲区大小检查}
    
    D -->|足够| E[提取音频块]
    D -->|不足| C
    
    E --> F[调用 FunASR 模型]
    F --> G[VAD 语音检测]
    G --> H[Paraformer 识别]
    H --> I[标点符号处理]
    I --> J[结果后处理]
    J --> K[格式化输出]
    K --> L[返回识别结果]
    
    L --> M{是否继续}
    M -->|是| C
    M -->|否| N[结束处理]
```

### 5. 会议总结处理流程

```mermaid
flowchart TD
    A[接收会议文本] --> B[验证输入参数]
    B --> C[选择总结类型]
    C --> D{总结类型}
    
    D -->|简要总结| E[构建简要总结提示词]
    D -->|详细总结| F[构建详细总结提示词]
    D -->|行动项提取| G[构建行动项提示词]
    
    E --> H[调用 LLM 服务]
    F --> H
    G --> H
    
    H --> I[LLM 处理]
    I --> J[结果后处理]
    J --> K[格式化输出]
    K --> L[返回总结结果]
```

### 6. 配置管理流程

```mermaid
flowchart TD
    A[系统启动] --> B[加载环境变量]
    B --> C[初始化 Pydantic Settings]
    C --> D[验证配置参数]
    D --> E[创建配置实例]
    
    F[运行时配置切换] --> G[接收切换请求]
    G --> H[验证新配置]
    H --> I[更新 ASR 引擎配置]
    I --> J[重新初始化相关组件]
    J --> K[返回切换结果]
```

## 关键技术特性

### 1. 异步处理
- 基于 FastAPI 的异步框架
- WebSocket 异步连接管理
- 异步音频处理流水线

### 2. 模块化设计
- 清晰的模块边界
- 依赖注入模式
- 可扩展的插件架构

### 3. 配置管理
- 环境变量驱动
- 运行时配置热切换
- 多场景配置支持

### 4. 错误处理
- 统一异常处理
- 详细错误日志
- 优雅降级机制

### 5. 性能优化
- 音频缓冲管理
- 连接池复用
- 内存使用优化

## 部署架构

```mermaid
flowchart TB
    subgraph "客户端层"
        A[Web 客户端]
        B[移动端客户端]
        C[桌面端客户端]
    end
    
    subgraph "API 网关层"
        D[负载均衡器]
    end
    
    subgraph "应用服务层"
        E[FastAPI 服务实例 1]
        F[FastAPI 服务实例 2]
        G[FastAPI 服务实例 N]
    end
    
    subgraph "业务逻辑层"
        H[会话管理服务]
        I[ASR 处理服务]
        J[LLM 集成服务]
    end
    
    subgraph "模型层"
        K[FunASR 模型]
        L[LLM 模型]
    end
    
    subgraph "存储层"
        M[会话数据存储]
        N[音频文件存储]
        O[日志存储]
    end
    
    A --> D
    B --> D
    C --> D
    
    D --> E
    D --> F
    D --> G
    
    E --> H
    E --> I
    E --> J
    
    F --> H
    F --> I
    F --> J
    
    G --> H
    G --> I
    G --> J
    
    I --> K
    J --> L
    
    H --> M
    I --> N
    E --> O
    F --> O
    G --> O
```

## 总结

本项目采用现代化的微服务架构设计，通过模块化的方式实现了完整的语音识别服务。主要特点包括：

1. **高性能**: 基于 FastAPI 的异步处理能力
2. **可扩展**: 清晰的模块划分和插件化设计
3. **易维护**: 统一的配置管理和错误处理
4. **多协议**: 同时支持 WebSocket 和 REST API
5. **智能化**: 集成 LLM 提供智能总结功能

通过合理的架构设计和代码组织，项目具备了良好的可维护性和扩展性，能够满足不同场景下的语音识别需求。