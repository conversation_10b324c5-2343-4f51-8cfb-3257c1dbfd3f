# SherpaOnnx Audio Tagging Android 应用

## 项目概述

SherpaOnnx Audio Tagging 是一个基于 Android 平台的音频标签识别应用，使用 Sherpa-ONNX 框架进行实时音频事件检测和分类。该应用能够通过麦克风录制音频并识别其中的音频事件，如音乐、语音、噪音等。

## 主要功能模块

### 1. 核心模块

#### MainActivity.kt
- **功能**: 应用主入口，负责权限管理和初始化
- **职责**:
  - 请求录音权限 (RECORD_AUDIO)
  - 初始化音频标签器 (Tagger)
  - 设置 Compose UI 主题

#### AudioTagging.kt
- **功能**: 音频标签识别的核心引擎
- **职责**:
  - 定义配置数据类 (AudioTaggingConfig, AudioTaggingModelConfig)
  - 封装 JNI 调用，与 C++ 底层库交互
  - 提供音频流创建和事件计算接口
  - 支持多种预训练模型 (Zipformer, CED)

#### Tagger.kt
- **功能**: 单例模式的标签器管理器
- **职责**:
  - 管理 AudioTagging 实例的生命周期
  - 提供全局访问点
  - 处理模型初始化

#### OfflineStream.kt
- **功能**: 音频流处理器
- **职责**:
  - 接收音频波形数据
  - 管理 JNI 资源
  - 提供资源自动释放机制

### 2. UI 模块

#### Home.kt
- **功能**: 主界面 Compose UI 组件
- **职责**:
  - 音频录制控制 (开始/停止)
  - 阈值调节滑块
  - 实时显示识别结果
  - 音频数据采集和处理

### 3. 配置模块

#### 模型配置
- 支持 6 种预训练模型:
  - Zipformer Small (默认)
  - Zipformer Standard
  - CED Tiny/Mini/Small/Base
- 可配置线程数、调试模式等参数

## 技术架构

### 技术栈
- **UI框架**: Jetpack Compose
- **语言**: Kotlin
- **音频处理**: Android AudioRecord API
- **AI推理**: Sherpa-ONNX (ONNX Runtime)
- **JNI**: C++ 底层库集成

### 依赖库
- androidx.compose (UI)
- androidx.lifecycle (生命周期管理)
- androidx.activity (Activity 组件)

## 应用流程

### 启动流程
1. MainActivity 启动
2. 请求录音权限
3. 初始化 Tagger 单例
4. 加载预训练模型
5. 显示主界面

### 音频识别流程
1. 用户点击开始按钮
2. 创建 AudioRecord 实例
3. 开始录音线程
4. 实时采集音频数据 (16kHz, 16-bit PCM)
5. 用户点击停止按钮
6. 创建 OfflineStream
7. 将音频数据传入流处理器
8. 调用 AI 模型进行推理
9. 过滤低于阈值的结果
10. 在 UI 中显示识别结果

## 文件结构

```
app/src/main/java/com/k2fsa/sherpa/onnx/audio/tagging/
├── MainActivity.kt          # 主Activity
├── AudioTagging.kt          # 核心AI引擎
├── Tagger.kt               # 单例管理器
├── OfflineStream.kt        # 音频流处理
├── Home.kt                 # 主界面UI
└── ui/theme/               # UI主题配置
    ├── Color.kt
    ├── Theme.kt
    └── Type.kt
```

## 模型支持

应用支持多种音频标签模型，可通过修改 `getAudioTaggingConfig` 函数中的 type 参数切换:

- **Type 0**: sherpa-onnx-zipformer-small-audio-tagging-2024-04-15 (默认)
- **Type 1**: sherpa-onnx-zipformer-audio-tagging-2024-04-09
- **Type 2-5**: sherpa-onnx-ced-{tiny,mini,small,base}-audio-tagging-2024-04-19

## 权限要求

- `android.permission.RECORD_AUDIO`: 录音权限，用于音频采集

## 最低系统要求

- Android API Level 21 (Android 5.0)
- 目标 API Level 34 (Android 14)