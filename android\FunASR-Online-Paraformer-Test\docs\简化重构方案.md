# ASR服务简化重构方案

## 🎯 重构目标

1. **引入FastAPI**：提供现代化的API框架支持
2. **优化模块划分**：清晰的代码组织结构
3. **多客户端支持**：为Web、移动端、桌面端等提供统一接口
4. **保持兼容性**：现有WebSocket协议和功能保持不变

## 📁 新目录结构

```
asr-service/
├── app/                          # FastAPI应用
│   ├── __init__.py
│   ├── main.py                   # FastAPI应用入口
│   ├── api/                      # API路由
│   │   ├── __init__.py
│   │   ├── websocket.py          # WebSocket路由
│   │   └── rest.py               # REST API路由
│   ├── core/                     # 核心配置
│   │   ├── __init__.py
│   │   ├── config.py             # 配置管理
│   │   └── deps.py               # 依赖注入
│   └── models/                   # 数据模型
│       ├── __init__.py
│       └── schemas.py            # Pydantic模型
├── services/                     # 业务服务层
│   ├── __init__.py
│   ├── asr_service.py            # ASR服务封装
│   └── session_manager.py       # 会话管理
├── asr/                          # ASR引擎（保持现有）
│   ├── __init__.py
│   ├── websocket_asr.py          # 现有ASR引擎
│   └── streaming_paraformer.py   # 现有流式引擎
├── config/                       # 配置文件（保持现有）
│   ├── __init__.py
│   ├── asr_config.py
│   └── config_switcher.py
├── frontend/                     # 前端（保持现有）
│   ├── index.html
│   └── style.css
├── utils/                        # 工具函数
│   ├── __init__.py
│   └── audio_utils.py
├── tests/                        # 测试文件
│   ├── __init__.py
│   └── test_api.py
├── requirements.txt              # 依赖管理
├── run_fastapi.py               # 新的启动脚本
├── run_system.py                # 保持现有启动脚本
└── README.md
```

## 🛠️ 技术栈

### 新增依赖
```txt
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6
websockets>=12.0
pydantic>=2.0.0
```

### 保持现有
- FunASR相关依赖
- 音频处理依赖
- 现有配置系统

## 🚀 实施步骤

### 第一步：环境准备（1天）

1. **更新依赖**
```bash
pip install fastapi uvicorn[standard] python-multipart
```

2. **创建目录结构**
```bash
mkdir -p app/{api,core,models}
mkdir -p services utils tests
```

### 第二步：FastAPI应用基础（1天）

创建FastAPI主应用，配置基础中间件和路由。

### 第三步：WebSocket迁移（2天）

将现有WebSocket逻辑迁移到FastAPI，保持协议兼容性。

### 第四步：REST API开发（2天）

添加REST API接口，支持不同类型的客户端。

### 第五步：服务层封装（2天）

封装现有ASR引擎，实现清晰的服务层。

### 第六步：测试验证（2天）

确保所有功能正常，编写基础测试。

## 📋 详细实施计划

### Day 1: 环境准备和FastAPI基础

**任务**：
- [ ] 更新项目依赖
- [ ] 创建FastAPI应用结构
- [ ] 实现基础配置管理
- [ ] 配置CORS和基础中间件

**交付成果**：
- FastAPI应用可以启动
- 基础健康检查接口可用
- 配置系统正常工作

### Day 2: WebSocket路由实现

**任务**：
- [ ] 创建FastAPI WebSocket路由
- [ ] 实现连接管理器
- [ ] 保持现有WebSocket协议兼容

**交付成果**：
- WebSocket连接正常
- 消息收发功能正常
- 与现有前端兼容

### Day 3-4: ASR服务集成

**任务**：
- [ ] 封装现有ASR引擎
- [ ] 实现音频处理逻辑
- [ ] 集成到WebSocket处理中

**交付成果**：
- 音频识别功能正常
- 实时识别结果返回
- 性能与原系统相当

### Day 5-6: REST API开发

**任务**：
- [ ] 实现会话管理API
- [ ] 实现配置管理API
- [ ] 添加文件上传支持
- [ ] 生成API文档

**交付成果**：
- REST API接口可用
- 自动生成的API文档
- 支持多种客户端接入

### Day 7-8: 服务层优化

**任务**：
- [ ] 重构服务层代码
- [ ] 实现会话管理
- [ ] 优化错误处理
- [ ] 添加日志记录

**交付成果**：
- 清晰的服务层架构
- 完善的错误处理
- 结构化日志输出

### Day 9-10: 测试和文档

**任务**：
- [ ] 编写基础测试用例
- [ ] 更新使用文档
- [ ] 性能测试和优化
- [ ] 部署验证

**交付成果**：
- 基础测试覆盖
- 完整的使用文档
- 部署指南

## 🔄 迁移策略

### 渐进式迁移
1. **并行运行**：新旧系统同时运行
2. **逐步切换**：先测试新功能，再切换生产流量
3. **回滚准备**：保持旧系统可用，必要时快速回滚

### 兼容性保证
- 保持现有WebSocket协议不变
- 前端代码无需修改
- 配置文件格式保持兼容
- ASR引擎功能不变

## 📊 预期收益

### 技术收益
- **API标准化**：自动生成的OpenAPI文档
- **类型安全**：Pydantic模型验证
- **性能提升**：FastAPI的高性能特性
- **开发效率**：更好的开发体验和调试工具

### 业务收益
- **多客户端支持**：Web、移动端、桌面端统一接口
- **扩展性**：易于添加新功能和接口
- **维护性**：清晰的代码结构和模块划分
- **文档化**：自动生成的API文档

## 🎯 成功标准

### 功能标准
- [ ] 现有WebSocket功能完全正常
- [ ] 音频识别性能不下降
- [ ] 前端无需修改即可使用
- [ ] 配置切换功能正常

### 技术标准
- [ ] FastAPI应用正常启动
- [ ] API文档自动生成
- [ ] REST接口响应正常
- [ ] 错误处理完善

### 扩展标准
- [ ] 支持多种客户端接入
- [ ] 易于添加新的API接口
- [ ] 代码结构清晰易维护
- [ ] 为未来功能扩展做好准备

## 📚 后续扩展方向

### 短期扩展（1-2个月）
- 移动端SDK开发
- 批量音频处理API
- 用户认证和权限管理
- 音频文件上传和处理

### 中期扩展（3-6个月）
- 多语言支持
- 实时转写API
- 音频质量分析
- 性能监控和告警

### 长期扩展（6个月以上）
- 微服务架构
- 容器化部署
- 云原生支持
- 大规模集群部署

这个简化重构方案保持了渐进式的特点，确保现有功能不受影响的同时，为未来的扩展打下良好基础。
