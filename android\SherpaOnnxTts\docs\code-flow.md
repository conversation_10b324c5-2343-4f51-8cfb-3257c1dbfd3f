# SherpaOnnxTts 代码执行流程分析

## 应用启动流程

### 1. 应用初始化序列

```mermaid
sequenceDiagram
    participant User
    participant MainActivity
    participant OfflineTts
    participant AudioTrack
    participant AssetManager
    
    User->>MainActivity: 启动应用
    MainActivity->>MainActivity: onCreate()
    MainActivity->>MainActivity: initTts()
    MainActivity->>AssetManager: 加载模型配置
    MainActivity->>OfflineTts: 创建TTS实例
    OfflineTts->>OfflineTts: 加载ONNX模型
    MainActivity->>MainActivity: initAudioTrack()
    MainActivity->>AudioTrack: 初始化音频轨道
    MainActivity->>MainActivity: 设置UI组件
    MainActivity->>User: 显示界面
```

## 核心功能流程

### 2. TTS 文本转语音流程

```mermaid
flowchart TD
    A[用户输入文本] --> B{验证输入参数}
    B -->|参数无效| C[显示错误提示]
    B -->|参数有效| D[禁用UI按钮]
    D --> E[重置AudioTrack]
    E --> F[创建后台线程]
    F --> G[调用TTS生成音频]
    G --> H[实时音频回调]
    H --> I[AudioTrack播放]
    I --> J{生成完成?}
    J -->|否| H
    J -->|是| K[保存音频文件]
    K --> L[启用播放按钮]
    L --> M[停止AudioTrack]
    C --> N[等待用户重新输入]
    M --> N
```

### 3. 音频播放流程

```mermaid
flowchart TD
    A[用户点击播放] --> B[停止当前MediaPlayer]
    B --> C[创建新MediaPlayer]
    C --> D[加载音频文件]
    D --> E{文件存在?}
    E -->|否| F[显示错误]
    E -->|是| G[开始播放]
    G --> H[音频播放中]
    H --> I[播放完成]
    F --> J[等待用户操作]
    I --> J
```

## 详细模块交互流程

### 4. TTS 引擎初始化流程

```mermaid
flowchart TD
    A[MainActivity.initTts()] --> B[确定模型类型]
    B --> C{模型类型}
    C -->|VITS| D[配置VITS模型]
    C -->|Matcha| E[配置Matcha模型]
    C -->|Kokoro| F[配置Kokoro模型]
    D --> G[设置模型路径]
    E --> G
    F --> G
    G --> H[复制Assets资源]
    H --> I[创建OfflineTtsConfig]
    I --> J[初始化OfflineTts实例]
    J --> K[加载JNI库]
    K --> L[TTS引擎就绪]
```

### 5. 资源文件管理流程

```mermaid
flowchart TD
    A[copyDataDir()] --> B[获取Assets列表]
    B --> C{是否为文件?}
    C -->|是| D[copyFile()]
    C -->|否| E[创建目录]
    E --> F[递归复制子项]
    F --> G{还有子项?}
    G -->|是| B
    G -->|否| H[复制完成]
    D --> I[打开输入流]
    I --> J[创建输出流]
    J --> K[字节流复制]
    K --> L[关闭流]
    L --> H
```

### 6. 音频生成与回调流程

```mermaid
sequenceDiagram
    participant UI as MainActivity
    participant TTS as OfflineTts
    participant JNI as Native Library
    participant AT as AudioTrack
    
    UI->>TTS: generateWithCallback()
    TTS->>JNI: 调用本地方法
    JNI->>JNI: 文本预处理
    JNI->>JNI: ONNX模型推理
    loop 音频块生成
        JNI->>UI: callback(samples)
        UI->>AT: write(samples)
        AT->>AT: 播放音频块
        UI->>JNI: 返回继续标志
    end
    JNI->>TTS: 返回完整音频
    TTS->>UI: GeneratedAudio对象
    UI->>UI: 保存音频文件
```

## 错误处理流程

### 7. 异常处理机制

```mermaid
flowchart TD
    A[用户操作] --> B{输入验证}
    B -->|验证失败| C[Toast提示错误]
    B -->|验证通过| D[执行操作]
    D --> E{操作异常?}
    E -->|是| F[Log记录错误]
    E -->|否| G[操作成功]
    F --> H[恢复UI状态]
    H --> I[等待用户重试]
    C --> I
    G --> J[更新UI状态]
```

### 8. 内存管理流程

```mermaid
flowchart TD
    A[OfflineTts创建] --> B[分配本地内存]
    B --> C[使用TTS功能]
    C --> D{应用退出?}
    D -->|否| C
    D -->|是| E[调用finalize()]
    E --> F[释放本地内存]
    F --> G[清理JNI资源]
    G --> H[内存释放完成]
```

## 线程管理

### 9. 多线程执行模型

```mermaid
flowchart TD
    A[主线程 - UI] --> B[创建后台线程]
    B --> C[后台线程 - TTS生成]
    C --> D[音频回调线程]
    D --> E[AudioTrack播放线程]
    C --> F{生成完成?}
    F -->|否| D
    F -->|是| G[runOnUiThread()]
    G --> H[更新UI状态]
    H --> A
```

## 配置管理流程

### 10. 模型配置选择流程

```mermaid
flowchart TD
    A[getOfflineTtsConfig()] --> B{modelName为空?}
    B -->|是| C{acousticModelName为空?}
    C -->|是| D[抛出异常]
    C -->|否| E[配置Matcha模型]
    B -->|否| F{voices为空?}
    F -->|是| G[配置VITS模型]
    F -->|否| H[配置Kokoro模型]
    E --> I[设置vocoder]
    G --> J[设置词典和tokens]
    H --> J
    I --> J
    J --> K[创建OfflineTtsConfig]
    K --> L[返回配置对象]
```

## 总结

该应用的核心执行流程包括：

1. **初始化阶段**: 加载模型、配置音频系统
2. **交互阶段**: 用户输入处理、参数验证
3. **生成阶段**: 后台TTS处理、实时音频输出
4. **播放阶段**: 音频文件播放管理
5. **清理阶段**: 资源释放、内存管理

整个流程采用异步处理模式，确保UI响应性，同时通过回调机制实现实时音频播放，提供良好的用户体验。