# API 参考文档

## 核心 API

### OfflineSpeakerDiarization

主要的说话人分离引擎类，提供离线说话人分离功能。

#### 构造函数

```kotlin
class OfflineSpeakerDiarization(
    assetManager: AssetManager? = null,
    val config: OfflineSpeakerDiarizationConfig
)
```

**参数:**
- `assetManager`: Android AssetManager，用于从 assets 目录加载模型文件
- `config`: 说话人分离配置对象

#### 主要方法

##### process()

```kotlin
fun process(samples: FloatArray): Array<OfflineSpeakerDiarizationSegment>
```

处理音频数据并返回说话人分离结果。

**参数:**
- `samples`: 音频样本数据（FloatArray 格式）

**返回值:**
- `Array<OfflineSpeakerDiarizationSegment>`: 分离结果数组

**示例:**
```kotlin
val segments = speakerDiarization.process(audioSamples)
segments.forEach { segment ->
    println("Speaker ${segment.speaker}: ${segment.start}s - ${segment.end}s")
}
```

##### processWithCallback()

```kotlin
fun processWithCallback(
    samples: FloatArray,
    callback: (numProcessedChunks: Int, numTotalChunks: Int, arg: Long) -> Int,
    arg: Long = 0
): Array<OfflineSpeakerDiarizationSegment>
```

带进度回调的处理方法。

**参数:**
- `samples`: 音频样本数据
- `callback`: 进度回调函数
- `arg`: 传递给回调函数的参数

**回调函数参数:**
- `numProcessedChunks`: 已处理的音频块数量
- `numTotalChunks`: 总音频块数量
- `arg`: 用户自定义参数

**示例:**
```kotlin
val callback = { processed: Int, total: Int, _: Long ->
    val progress = 100.0 * processed / total
    println("Progress: %.2f%%".format(progress))
    0 // 返回 0 继续处理，非 0 停止处理
}

val segments = speakerDiarization.processWithCallback(audioSamples, callback)
```

##### sampleRate()

```kotlin
fun sampleRate(): Int
```

获取模型期望的采样率。

**返回值:**
- `Int`: 采样率（通常为 16000 Hz）

##### setConfig()

```kotlin
fun setConfig(config: OfflineSpeakerDiarizationConfig)
```

更新配置（仅聚类配置会生效）。

**参数:**
- `config`: 新的配置对象

##### release()

```kotlin
fun release()
```

释放原生资源。

---

### SpeakerDiarizationObject

单例对象，管理全局的说话人分离实例。

#### 属性

```kotlin
object SpeakerDiarizationObject {
    val sd: OfflineSpeakerDiarization // 获取说话人分离实例
}
```

#### 方法

##### initSpeakerDiarization()

```kotlin
fun initSpeakerDiarization(assetManager: AssetManager? = null)
```

初始化说话人分离实例。

**参数:**
- `assetManager`: Android AssetManager

**示例:**
```kotlin
// 在 MainActivity.onCreate() 中调用
SpeakerDiarizationObject.initSpeakerDiarization(this.assets)

// 使用实例
val sampleRate = SpeakerDiarizationObject.sd.sampleRate()
```

---

### 配置类

#### OfflineSpeakerDiarizationConfig

主配置类，包含所有说话人分离相关的配置。

```kotlin
data class OfflineSpeakerDiarizationConfig(
    var segmentation: OfflineSpeakerSegmentationModelConfig = OfflineSpeakerSegmentationModelConfig(),
    var embedding: SpeakerEmbeddingExtractorConfig = SpeakerEmbeddingExtractorConfig(),
    var clustering: FastClusteringConfig = FastClusteringConfig(),
    var minDurationOn: Float = 0.2f,
    var minDurationOff: Float = 0.5f
)
```

**字段说明:**
- `segmentation`: 说话人分割模型配置
- `embedding`: 说话人嵌入提取器配置
- `clustering`: 聚类算法配置
- `minDurationOn`: 最小语音持续时间（秒）
- `minDurationOff`: 最小静音持续时间（秒）

#### OfflineSpeakerSegmentationModelConfig

说话人分割模型配置。

```kotlin
data class OfflineSpeakerSegmentationModelConfig(
    var pyannote: OfflineSpeakerSegmentationPyannoteModelConfig = OfflineSpeakerSegmentationPyannoteModelConfig(),
    var numThreads: Int = 1,
    var debug: Boolean = false,
    var provider: String = "cpu"
)
```

#### OfflineSpeakerSegmentationPyannoteModelConfig

```kotlin
data class OfflineSpeakerSegmentationPyannoteModelConfig(
    var model: String = ""
)
```

#### SpeakerEmbeddingExtractorConfig

说话人嵌入提取器配置。

```kotlin
data class SpeakerEmbeddingExtractorConfig(
    val model: String = "",
    var numThreads: Int = 1,
    var debug: Boolean = false,
    var provider: String = "cpu"
)
```

#### FastClusteringConfig

快速聚类算法配置。

```kotlin
data class FastClusteringConfig(
    var numClusters: Int = -1,
    var threshold: Float = 0.5f
)
```

**字段说明:**
- `numClusters`: 说话人数量（-1 表示自动检测）
- `threshold`: 聚类阈值（0.0-1.0，值越大说话人越少）

---

### 结果类

#### OfflineSpeakerDiarizationSegment

说话人分离结果片段。

```kotlin
data class OfflineSpeakerDiarizationSegment(
    val start: Float,  // 开始时间（秒）
    val end: Float,    // 结束时间（秒）
    val speaker: Int   // 说话人 ID（从 0 开始）
)
```

**示例:**
```kotlin
val segment = OfflineSpeakerDiarizationSegment(
    start = 1.5f,
    end = 3.2f,
    speaker = 0
)
println("说话人 ${segment.speaker} 在 ${segment.start}-${segment.end} 秒说话")
```

---

### 音频处理 API

#### readUri()

```kotlin
fun readUri(context: Context, uri: Uri): WaveData
```

从 URI 读取音频文件。

**参数:**
- `context`: Android Context
- `uri`: 音频文件的 URI

**返回值:**
- `WaveData`: 包含音频数据的对象

#### WaveData

音频数据容器。

```kotlin
data class WaveData(
    val sampleRate: Int? = null,    // 采样率
    val samples: FloatArray? = null, // 音频样本
    val msg: String? = null          // 错误信息
)
```

**示例:**
```kotlin
val waveData = readUri(context, audioUri)
if (waveData.msg != null) {
    // 处理错误
    println("错误: ${waveData.msg}")
} else {
    // 使用音频数据
    val samples = waveData.samples!!
    val sampleRate = waveData.sampleRate!!
}
```

---

## 使用示例

### 完整的说话人分离流程

```kotlin
class SpeakerDiarizationExample {
    
    fun performSpeakerDiarization(context: Context, audioUri: Uri) {
        // 1. 初始化说话人分离（通常在 Application 或 MainActivity 中）
        SpeakerDiarizationObject.initSpeakerDiarization(context.assets)
        
        // 2. 读取音频文件
        val waveData = readUri(context, audioUri)
        if (waveData.msg != null) {
            println("音频读取失败: ${waveData.msg}")
            return
        }
        
        // 3. 验证采样率
        val expectedSampleRate = SpeakerDiarizationObject.sd.sampleRate()
        if (waveData.sampleRate != expectedSampleRate) {
            println("采样率不匹配: 期望 $expectedSampleRate, 实际 ${waveData.sampleRate}")
            return
        }
        
        // 4. 设置聚类参数（可选）
        val config = OfflineSpeakerDiarizationConfig(
            clustering = FastClusteringConfig(
                numClusters = 2,  // 已知有 2 个说话人
                threshold = 0.5f
            )
        )
        SpeakerDiarizationObject.sd.setConfig(config)
        
        // 5. 执行说话人分离
        val progressCallback = { processed: Int, total: Int, _: Long ->
            val percent = 100.0 * processed / total
            println("处理进度: %.1f%%".format(percent))
            0 // 继续处理
        }
        
        val segments = SpeakerDiarizationObject.sd.processWithCallback(
            waveData.samples!!, 
            progressCallback
        )
        
        // 6. 处理结果
        println("检测到 ${segments.map { it.speaker }.distinct().size} 个说话人")
        segments.forEach { segment ->
            println("说话人 ${segment.speaker}: %.2fs - %.2fs".format(segment.start, segment.end))
        }
    }
}
```

### 自定义配置示例

```kotlin
fun createCustomConfig(): OfflineSpeakerDiarizationConfig {
    return OfflineSpeakerDiarizationConfig(
        segmentation = OfflineSpeakerSegmentationModelConfig(
            pyannote = OfflineSpeakerSegmentationPyannoteModelConfig(
                model = "segmentation.onnx"
            ),
            numThreads = 2,
            debug = true,
            provider = "cpu"
        ),
        embedding = SpeakerEmbeddingExtractorConfig(
            model = "embedding.onnx",
            numThreads = 2,
            debug = true,
            provider = "cpu"
        ),
        clustering = FastClusteringConfig(
            numClusters = -1,  // 自动检测
            threshold = 0.6f   // 较高阈值，倾向于较少说话人
        ),
        minDurationOn = 0.3f,   // 最小语音段 300ms
        minDurationOff = 0.7f   // 最小静音段 700ms
    )
}
```

### 错误处理示例

```kotlin
fun safeProcessAudio(audioSamples: FloatArray): List<OfflineSpeakerDiarizationSegment> {
    return try {
        val segments = SpeakerDiarizationObject.sd.process(audioSamples)
        segments.toList()
    } catch (e: Exception) {
        when {
            e.message?.contains("sample rate") == true -> {
                println("采样率错误: ${e.message}")
            }
            e.message?.contains("model") == true -> {
                println("模型加载错误: ${e.message}")
            }
            else -> {
                println("处理错误: ${e.message}")
            }
        }
        emptyList()
    }
}
```

---

## 性能优化建议

### 1. 内存管理

```kotlin
// 及时释放大型音频数据
fun processLargeAudio(audioUri: Uri) {
    val waveData = readUri(context, audioUri)
    try {
        val segments = SpeakerDiarizationObject.sd.process(waveData.samples!!)
        // 处理结果...
    } finally {
        // 音频数据使用完毕后，让 GC 回收
        System.gc()
    }
}
```

### 2. 线程管理

```kotlin
// 在后台线程执行处理
fun processInBackground(audioSamples: FloatArray, callback: (List<OfflineSpeakerDiarizationSegment>) -> Unit) {
    Thread {
        val segments = SpeakerDiarizationObject.sd.process(audioSamples)
        // 切换到主线程更新 UI
        Handler(Looper.getMainLooper()).post {
            callback(segments.toList())
        }
    }.start()
}
```

### 3. 配置缓存

```kotlin
object ConfigCache {
    private var cachedConfig: OfflineSpeakerDiarizationConfig? = null
    
    fun getOptimizedConfig(): OfflineSpeakerDiarizationConfig {
        if (cachedConfig == null) {
            cachedConfig = OfflineSpeakerDiarizationConfig(
                // 优化的配置参数...
            )
        }
        return cachedConfig!!
    }
}
```

---

## 常见问题

### Q: 如何处理不同采样率的音频？

A: 目前只支持 16kHz 采样率，需要预先转换：

```kotlin
fun checkSampleRate(waveData: WaveData): Boolean {
    val expectedRate = SpeakerDiarizationObject.sd.sampleRate()
    if (waveData.sampleRate != expectedRate) {
        println("需要将音频从 ${waveData.sampleRate}Hz 转换为 ${expectedRate}Hz")
        return false
    }
    return true
}
```

### Q: 如何选择合适的聚类阈值？

A: 根据音频特点调整：

```kotlin
fun getRecommendedThreshold(audioLengthSeconds: Float, estimatedSpeakers: Int): Float {
    return when {
        estimatedSpeakers <= 2 -> 0.4f  // 较低阈值，更敏感
        estimatedSpeakers <= 4 -> 0.5f  // 默认阈值
        else -> 0.6f                    // 较高阈值，避免过度分割
    }
}
```

### Q: 如何处理长音频文件？

A: 考虑分段处理：

```kotlin
fun processLongAudio(samples: FloatArray, chunkSizeSeconds: Float = 60f): List<OfflineSpeakerDiarizationSegment> {
    val sampleRate = SpeakerDiarizationObject.sd.sampleRate()
    val chunkSize = (chunkSizeSeconds * sampleRate).toInt()
    val allSegments = mutableListOf<OfflineSpeakerDiarizationSegment>()
    
    for (i in samples.indices step chunkSize) {
        val end = minOf(i + chunkSize, samples.size)
        val chunk = samples.sliceArray(i until end)
        val segments = SpeakerDiarizationObject.sd.process(chunk)
        
        // 调整时间偏移
        val timeOffset = i.toFloat() / sampleRate
        segments.forEach { segment ->
            allSegments.add(
                OfflineSpeakerDiarizationSegment(
                    start = segment.start + timeOffset,
                    end = segment.end + timeOffset,
                    speaker = segment.speaker
                )
            )
        }
    }
    
    return allSegments
}
```