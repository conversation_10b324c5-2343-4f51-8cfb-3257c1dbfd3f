# 系统架构设计文档

## 整体架构概览

### 系统架构图

```mermaid
graph TB
    subgraph "Presentation Layer (表示层)"
        A[MainActivity]
        B[HomeScreen]
        C[Theme System]
    end
    
    subgraph "Business Logic Layer (业务逻辑层)"
        D[Tagger Manager]
        E[Audio Processing]
        F[Result Filtering]
    end
    
    subgraph "Data Layer (数据层)"
        G[AudioTagging Engine]
        H[OfflineStream]
        I[Model Configuration]
    end
    
    subgraph "Native Layer (本地层)"
        J[Sherpa-ONNX JNI]
        K[ONNX Runtime]
        L[Audio Models]
    end
    
    subgraph "System Layer (系统层)"
        M[Android AudioRecord]
        N[Wear OS Services]
        O[File System]
    end
    
    A --> D
    B --> E
    D --> G
    E --> H
    G --> J
    H --> J
    J --> K
    K --> L
    E --> M
    A --> N
    I --> O
```

## 分层架构详解

### 1. 表示层 (Presentation Layer)

#### 组件职责
- **MainActivity**: 应用入口点，负责生命周期管理
- **HomeScreen**: 主要用户界面，处理用户交互
- **Theme System**: Wear OS 适配的 UI 主题

#### 设计模式
- **MVVM 模式**: 使用 Compose 状态管理
- **单一职责原则**: 每个 Composable 专注特定功能

```mermaid
classDiagram
    class MainActivity {
        +onCreate()
        +onRequestPermissionsResult()
        -requestPermissions()
        -initializeTagger()
    }
    
    class HomeScreen {
        +threshold: Float
        +isStarted: Boolean
        +result: String
        +onButtonClick()
        -startRecording()
        -stopRecording()
    }
    
    class WearApp {
        +Composable()
    }
    
    MainActivity --> WearApp
    WearApp --> HomeScreen
```

### 2. 业务逻辑层 (Business Logic Layer)

#### 核心组件

```mermaid
classDiagram
    class Tagger {
        -_tagger: AudioTagging?
        +tagger: AudioTagging
        +initTagger(AssetManager, Int)
    }
    
    class AudioProcessing {
        +sampleRateInHz: Int
        +bufferSize: Int
        +processAudio()
        +flattenSamples()
    }
    
    class ResultFilter {
        +threshold: Float
        +filterResults(List~AudioEvent~)
        +formatDisplay(List~AudioEvent~)
    }
    
    Tagger --> AudioProcessing
    AudioProcessing --> ResultFilter
```

#### 设计原则
- **单例模式**: Tagger 使用单例确保全局唯一实例
- **工厂模式**: 模型配置通过工厂方法创建
- **策略模式**: 支持多种音频处理策略

### 3. 数据层 (Data Layer)

#### 数据流架构

```mermaid
flowchart LR
    A[Audio Input] --> B[AudioRecord]
    B --> C[ShortArray Buffer]
    C --> D[FloatArray Conversion]
    D --> E[OfflineStream]
    E --> F[AudioTagging Engine]
    F --> G[ONNX Model]
    G --> H[Prediction Results]
    H --> I[AudioEvent List]
```

#### 核心数据结构

```mermaid
classDiagram
    class AudioTaggingConfig {
        +model: AudioTaggingModelConfig
        +labels: String
        +topK: Int
    }
    
    class AudioTaggingModelConfig {
        +zipformer: OfflineZipformerAudioTaggingModelConfig
        +ced: String
        +numThreads: Int
        +debug: Boolean
        +provider: String
    }
    
    class AudioEvent {
        +name: String
        +index: Int
        +prob: Float
    }
    
    class OfflineStream {
        +ptr: Long
        +acceptWaveform(FloatArray, Int)
        +release()
    }
    
    AudioTaggingConfig --> AudioTaggingModelConfig
    AudioTagging --> AudioEvent
    AudioTagging --> OfflineStream
```

### 4. 本地层 (Native Layer)

#### JNI 接口设计

```mermaid
sequenceDiagram
    participant Java as Java Layer
    participant JNI as JNI Bridge
    participant Native as Native Library
    participant ONNX as ONNX Runtime
    
    Java->>JNI: newFromAsset()
    JNI->>Native: Create AudioTagging
    Native->>ONNX: Load Model
    ONNX-->>Native: Model Ready
    Native-->>JNI: Instance Pointer
    JNI-->>Java: Long Pointer
    
    Java->>JNI: createStream()
    JNI->>Native: Create Stream
    Native-->>JNI: Stream Pointer
    JNI-->>Java: OfflineStream
    
    Java->>JNI: acceptWaveform()
    JNI->>Native: Process Audio
    
    Java->>JNI: compute()
    JNI->>Native: Run Inference
    Native->>ONNX: Forward Pass
    ONNX-->>Native: Predictions
    Native-->>JNI: Results Array
    JNI-->>Java: AudioEvent[]
```

## 设计模式应用

### 1. 单例模式 (Singleton Pattern)

```kotlin
object Tagger {
    private var _tagger: AudioTagging? = null
    val tagger: AudioTagging get() = _tagger!!
    
    fun initTagger(assetManager: AssetManager?, numThreads: Int) {
        synchronized(this) {
            if (_tagger != null) return
            // 初始化逻辑
        }
    }
}
```

**优势**:
- 确保全局唯一的 AudioTagging 实例
- 线程安全的初始化
- 资源共享和管理

### 2. 工厂模式 (Factory Pattern)

```kotlin
fun getAudioTaggingConfig(type: Int, numThreads: Int): AudioTaggingConfig? {
    return when (type) {
        0 -> createZipformerSmallConfig(numThreads)
        1 -> createZipformerConfig(numThreads)
        2 -> createCEDTinyConfig(numThreads)
        // ... 其他配置
        else -> null
    }
}
```

**优势**:
- 封装对象创建逻辑
- 支持多种模型配置
- 易于扩展新模型类型

### 3. 观察者模式 (Observer Pattern)

```kotlin
// Compose 状态管理
var result by remember { mutableStateOf("") }
var isStarted by remember { mutableStateOf(false) }
var threshold by remember { mutableStateOf(0.6F) }
```

**优势**:
- 响应式 UI 更新
- 状态变化自动通知
- 解耦数据和视图

### 4. 策略模式 (Strategy Pattern)

```kotlin
// 不同的音频处理策略
interface AudioProcessingStrategy {
    fun processAudio(samples: FloatArray): List<AudioEvent>
}

class ZipformerStrategy : AudioProcessingStrategy
class CEDStrategy : AudioProcessingStrategy
```

## 性能优化设计

### 1. 内存管理策略

```mermaid
flowchart TD
    A[内存分配] --> B[对象池管理]
    B --> C[及时释放]
    C --> D[垃圾回收优化]
    
    A --> E[缓冲区复用]
    E --> F[批量处理]
    F --> G[异步处理]
    
    D --> H[性能监控]
    G --> H
    H --> I[内存泄漏检测]
```

### 2. 线程管理架构

```mermaid
sequenceDiagram
    participant UI as UI Thread
    participant Audio as Audio Thread
    participant Compute as Compute Thread
    participant JNI as JNI Thread
    
    UI->>Audio: Start Recording
    Audio->>Audio: Collect Samples
    Audio->>Compute: Process Audio
    Compute->>JNI: Model Inference
    JNI-->>Compute: Results
    Compute-->>UI: Update Display
```

### 3. 资源优化策略

- **模型量化**: 使用 INT8 量化模型减少内存占用
- **延迟加载**: 按需加载模型资源
- **缓存机制**: 缓存频繁使用的配置
- **批处理**: 批量处理音频数据

## 扩展性设计

### 1. 模块化架构

```mermaid
graph LR
    A[Core Module] --> B[Audio Module]
    A --> C[Model Module]
    A --> D[UI Module]
    
    B --> E[Recording]
    B --> F[Processing]
    
    C --> G[Zipformer]
    C --> H[CED]
    C --> I[Custom Models]
    
    D --> J[Wear UI]
    D --> K[Phone UI]
```

### 2. 插件化支持

- **模型插件**: 支持动态加载新模型
- **处理插件**: 支持自定义音频处理算法
- **UI 插件**: 支持不同设备的 UI 适配

### 3. 配置化管理

```kotlin
data class AppConfig(
    val modelType: Int = 0,
    val numThreads: Int = 2,
    val threshold: Float = 0.6f,
    val sampleRate: Int = 16000,
    val bufferSize: Int = 1600
)
```

## 安全性设计

### 1. 权限管理

```mermaid
flowchart TD
    A[权限检查] --> B{RECORD_AUDIO}
    B -->|已授权| C[继续执行]
    B -->|未授权| D[请求权限]
    D --> E{用户响应}
    E -->|同意| C
    E -->|拒绝| F[显示错误并退出]
```

### 2. 数据安全

- **本地处理**: 音频数据不上传到服务器
- **内存清理**: 及时清理敏感数据
- **权限最小化**: 只请求必要权限

### 3. 错误处理

```kotlin
try {
    // 音频处理逻辑
} catch (e: SecurityException) {
    Log.e(TAG, "Permission denied", e)
    // 权限错误处理
} catch (e: IllegalStateException) {
    Log.e(TAG, "Invalid state", e)
    // 状态错误处理
} catch (e: Exception) {
    Log.e(TAG, "Unexpected error", e)
    // 通用错误处理
}
```

## 总结

该架构设计具有以下特点：

1. **分层清晰**: 表示层、业务层、数据层、本地层职责明确
2. **模块化**: 高内聚、低耦合的模块设计
3. **可扩展**: 支持新模型、新功能的快速集成
4. **高性能**: 多线程、内存优化、模型量化
5. **安全可靠**: 完善的权限管理和错误处理
6. **易维护**: 清晰的代码结构和设计模式应用

这种架构设计确保了应用在 Wear OS 平台上的稳定运行，同时为未来的功能扩展和性能优化提供了良好的基础。