<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时语音转文字显示</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🎤 实时语音转文字</h1>
            <div class="status-indicator">
                <span class="status-dot" id="statusDot"></span>
                <span class="status-text" id="statusText">连接中...</span>
            </div>
        </header>

        <main class="main-content">
            <div class="text-display" id="textDisplay">
                <div class="welcome-message">
                    <p>🔊 等待语音识别数据...</p>
                    <p class="hint">请确保ASR程序正在运行</p>
                </div>
            </div>
        </main>

        <footer class="footer">
            <div class="controls">
                <button class="btn btn-record" id="recordBtn">🎤 开始录音</button>
                <button class="btn btn-clear" id="clearBtn">清空文本</button>
                <button class="btn btn-save" id="saveBtn">保存文本</button>
                <button class="btn btn-summary" id="summaryBtn">📝 总结会议</button>
                <button class="btn btn-reconnect" id="reconnectBtn">重新连接</button>
            </div>
            <div class="info">
                <span>连接状态: <span id="connectionInfo">未连接</span></span>
                <span>|</span>
                <span>最后更新: <span id="lastUpdate">--</span></span>
            </div>
        </footer>

        <!-- 会议总结弹窗 -->
        <div class="modal" id="summaryModal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>📝 会议记录总结</h2>
                    <button class="modal-close" id="modalClose">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="summary-options">
                        <label>
                            <input type="radio" name="summaryType" value="brief" checked>
                            简要总结 (重点摘要)
                        </label>
                        <label>
                            <input type="radio" name="summaryType" value="detailed">
                            详细总结 (完整分析)
                        </label>
                        <label>
                            <input type="radio" name="summaryType" value="action">
                            行动项总结 (待办事项)
                        </label>
                    </div>
                    <div class="summary-result" id="summaryResult">
                        <div class="loading" id="summaryLoading" style="display: none;">
                            <div class="spinner"></div>
                            <p>正在生成会议总结，请稍候...</p>
                        </div>
                        <div class="summary-content" id="summaryContent" style="display: none;"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" id="generateSummaryBtn">生成总结</button>
                    <button class="btn btn-secondary" id="saveSummaryBtn" style="display: none;">保存总结</button>
                    <button class="btn btn-secondary" id="copySummaryBtn" style="display: none;">复制总结</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class ASRWebSocketClient {
            constructor() {
                this.ws = null;
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 5;
                this.reconnectDelay = 2000;
                this.isManualDisconnect = false;
                
                // 音频录制相关
                this.mediaRecorder = null;
                this.audioStream = null;
                this.isRecording = false;
                this.audioChunks = [];
                this.audioContext = null;
                this.processor = null;
                
                this.initElements();
                this.bindEvents();
                this.connect();
            }

            initElements() {
                this.statusDot = document.getElementById('statusDot');
                this.statusText = document.getElementById('statusText');
                this.textDisplay = document.getElementById('textDisplay');
                this.recordBtn = document.getElementById('recordBtn');
                this.clearBtn = document.getElementById('clearBtn');
                this.saveBtn = document.getElementById('saveBtn');
                this.summaryBtn = document.getElementById('summaryBtn');
                this.reconnectBtn = document.getElementById('reconnectBtn');
                this.connectionInfo = document.getElementById('connectionInfo');
                this.lastUpdate = document.getElementById('lastUpdate');

                // 总结弹窗相关元素
                this.summaryModal = document.getElementById('summaryModal');
                this.modalClose = document.getElementById('modalClose');
                this.generateSummaryBtn = document.getElementById('generateSummaryBtn');
                this.saveSummaryBtn = document.getElementById('saveSummaryBtn');
                this.copySummaryBtn = document.getElementById('copySummaryBtn');
                this.summaryLoading = document.getElementById('summaryLoading');
                this.summaryContent = document.getElementById('summaryContent');
                this.summaryResult = document.getElementById('summaryResult');
            }

            bindEvents() {
                this.recordBtn.addEventListener('click', () => this.toggleRecording());
                this.clearBtn.addEventListener('click', () => this.clearText());
                this.saveBtn.addEventListener('click', () => this.saveText());
                this.summaryBtn.addEventListener('click', () => this.showSummaryModal());
                this.reconnectBtn.addEventListener('click', () => this.reconnect());

                // 总结弹窗事件
                this.modalClose.addEventListener('click', () => this.hideSummaryModal());
                this.generateSummaryBtn.addEventListener('click', () => this.generateSummary());
                this.saveSummaryBtn.addEventListener('click', () => this.saveSummary());
                this.copySummaryBtn.addEventListener('click', () => this.copySummary());

                // 点击弹窗外部关闭
                this.summaryModal.addEventListener('click', (e) => {
                    if (e.target === this.summaryModal) {
                        this.hideSummaryModal();
                    }
                });

                // 键盘快捷键
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey) {
                        switch(e.key) {
                            case 'l':
                                e.preventDefault();
                                this.clearText();
                                break;
                            case 's':
                                e.preventDefault();
                                this.saveText();
                                break;
                            case 'r':
                                e.preventDefault();
                                this.reconnect();
                                break;
                            case 'm':
                                e.preventDefault();
                                this.showSummaryModal();
                                break;
                        }
                    }
                    // ESC键关闭弹窗
                    if (e.key === 'Escape') {
                        this.hideSummaryModal();
                    }
                });
            }

            connect() {
                try {
                    // 动态获取 WebSocket 连接地址
                    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                    const host = window.location.hostname === '0.0.0.0' ? 'localhost' : window.location.hostname;
                    const port = window.location.port;
                    const wsUrl = `${protocol}//${host}:${port}/ws/audio`;

                    console.log('连接到 WebSocket:', wsUrl);
                    this.ws = new WebSocket(wsUrl);

                    this.ws.onopen = () => {
                        console.log('WebSocket连接已建立');
                        this.updateStatus('connected', '已连接');
                        this.reconnectAttempts = 0;
                        this.isManualDisconnect = false;
                        this.updateConnectionInfo('已连接');
                    };

                    this.ws.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            this.handleMessage(data);
                        } catch (e) {
                            console.error('解析消息失败:', e);
                            // 尝试直接显示文本消息
                            this.displayText(event.data, true);
                        }
                    };

                    this.ws.onclose = () => {
                        console.log('WebSocket连接已关闭');
                        this.updateStatus('disconnected', '连接断开');
                        this.updateConnectionInfo('连接断开');
                        
                        if (!this.isManualDisconnect) {
                            this.attemptReconnect();
                        }
                    };

                    this.ws.onerror = (error) => {
                        console.error('WebSocket错误:', error);
                        this.updateStatus('error', '连接错误');
                        this.updateConnectionInfo('连接错误');
                    };

                } catch (error) {
                    console.error('连接失败:', error);
                    this.updateStatus('error', '连接失败');
                    this.updateConnectionInfo('连接失败');
                    this.attemptReconnect();
                }
            }

            handleMessage(data) {
                switch (data.type) {
                    case 'welcome':
                        console.log('收到欢迎消息:', data.message);
                        if (data.session_info && data.session_info.current_text) {
                            this.displayText(data.session_info.current_text, true);
                        }
                        break;

                    case 'asr_result':
                    case 'recognition_result':
                        // 处理 FastAPI 格式的识别结果
                        if (data.result) {
                            this.displayText(data.result.text, data.result.is_final);
                        } else {
                            this.displayText(data.text, data.is_final);
                        }
                        this.updateLastUpdate();
                        break;

                    case 'pong':
                        // 心跳响应
                        break;

                    case 'error':
                        console.error('服务器错误:', data.error_message);
                        this.updateStatus('error', '服务器错误');
                        break;

                    default:
                        console.log('未知消息类型:', data);
                }
            }

            displayText(text, isFinal = true) {
                // 清除欢迎消息
                const welcomeMsg = this.textDisplay.querySelector('.welcome-message');
                if (welcomeMsg) {
                    welcomeMsg.remove();
                }

                if (isFinal) {
                    // 最终结果：创建新的文本元素
                    const textElement = document.createElement('div');
                    textElement.className = 'text-line final';
                    textElement.textContent = text;

                    // 添加时间戳
                    const timestamp = document.createElement('span');
                    timestamp.className = 'timestamp';
                    timestamp.textContent = new Date().toLocaleTimeString();
                    textElement.appendChild(timestamp);

                    this.textDisplay.appendChild(textElement);
                } else {
                    // 预览文本：更新或创建预览元素
                    let previewElement = this.textDisplay.querySelector('.text-line.preview');
                    if (!previewElement) {
                        previewElement = document.createElement('div');
                        previewElement.className = 'text-line preview';
                        this.textDisplay.appendChild(previewElement);
                    }
                    previewElement.textContent = text;
                }

                // 自动滚动到底部
                this.textDisplay.scrollTop = this.textDisplay.scrollHeight;
            }

            updateStatus(status, text) {
                this.statusDot.className = `status-dot ${status}`;
                this.statusText.textContent = text;
            }

            updateConnectionInfo(info) {
                this.connectionInfo.textContent = info;
            }

            updateLastUpdate() {
                const now = new Date();
                this.lastUpdate.textContent = now.toLocaleTimeString();
            }

            attemptReconnect() {
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++;
                    this.updateStatus('reconnecting', `重连中 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                    
                    setTimeout(() => {
                        console.log(`尝试重连 ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
                        this.connect();
                    }, this.reconnectDelay);
                } else {
                    this.updateStatus('error', '连接失败');
                    this.updateConnectionInfo('连接失败，请手动重连');
                }
            }

            clearText() {
                this.textDisplay.innerHTML = '<div class="welcome-message"><p>文本已清空</p></div>';
            }

            saveText() {
                const textElements = this.textDisplay.querySelectorAll('.text-line.final');
                if (textElements.length > 0) {
                    let allText = '';
                    textElements.forEach((element, index) => {
                        // 提取文本内容（排除时间戳）
                        const textContent = element.childNodes[0].textContent || element.textContent;
                        const timestamp = element.querySelector('.timestamp')?.textContent || '';
                        allText += `[${timestamp}] ${textContent}\n`;
                    });

                    const blob = new Blob([allText], { type: 'text/plain;charset=utf-8' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `语音转文字_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                } else {
                    alert('没有文本可保存');
                }
            }

            reconnect() {
                this.isManualDisconnect = true;
                if (this.ws) {
                    this.ws.close();
                }
                this.reconnectAttempts = 0;
                setTimeout(() => this.connect(), 500);
            }

            // 音频录制相关方法
            async toggleRecording() {
                if (!this.isRecording) {
                    await this.startRecording();
                } else {
                    this.stopRecording();
                }
            }

            async startRecording() {
                try {
                    // 请求麦克风权限
                    this.audioStream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            sampleRate: 16000,
                            channelCount: 1,
                            echoCancellation: true,
                            noiseSuppression: true
                        }
                    });

                    // 创建音频上下文
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
                        sampleRate: 16000
                    });

                    const source = this.audioContext.createMediaStreamSource(this.audioStream);
                    
                    // 创建音频处理器 - 使用更小的缓冲区提高实时性
                    this.processor = this.audioContext.createScriptProcessor(1024, 1, 1);
                    
                    this.processor.onaudioprocess = (event) => {
                        if (this.isRecording && this.ws && this.ws.readyState === WebSocket.OPEN) {
                            const inputBuffer = event.inputBuffer.getChannelData(0);
                            
                            // 转换为16位PCM
                            const pcmData = this.float32ToPCM16(inputBuffer);
                            
                            // 发送音频数据到服务器（二进制格式）
                            this.ws.send(pcmData.buffer);
                        }
                    };

                    source.connect(this.processor);
                    this.processor.connect(this.audioContext.destination);

                    this.isRecording = true;
                    this.recordBtn.textContent = '🛑 停止录音';
                    this.recordBtn.classList.add('recording');
                    
                    console.log('开始录音');
                    
                } catch (error) {
                    console.error('启动录音失败:', error);
                    alert('无法访问麦克风，请检查权限设置');
                }
            }

            stopRecording() {
                this.isRecording = false;
                
                if (this.processor) {
                    this.processor.disconnect();
                    this.processor = null;
                }
                
                if (this.audioContext) {
                    this.audioContext.close();
                    this.audioContext = null;
                }
                
                if (this.audioStream) {
                    this.audioStream.getTracks().forEach(track => track.stop());
                    this.audioStream = null;
                }
                
                this.recordBtn.textContent = '🎤 开始录音';
                this.recordBtn.classList.remove('recording');
                
                console.log('停止录音');
            }

            // 将Float32Array转换为16位PCM
            float32ToPCM16(float32Array) {
                const pcm16 = new Int16Array(float32Array.length);
                for (let i = 0; i < float32Array.length; i++) {
                    const sample = Math.max(-1, Math.min(1, float32Array[i]));
                    pcm16[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
                }
                return pcm16;
            }

            // 发送心跳
            sendHeartbeat() {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(JSON.stringify({ type: 'ping' }));
                }
            }

            // 会议总结相关方法
            showSummaryModal() {
                const textElements = this.textDisplay.querySelectorAll('.text-line.final');
                if (textElements.length === 0) {
                    alert('没有会议记录可以总结，请先进行语音转文字');
                    return;
                }

                this.summaryModal.style.display = 'flex';
                this.resetSummaryModal();
            }

            hideSummaryModal() {
                this.summaryModal.style.display = 'none';
            }

            resetSummaryModal() {
                this.summaryLoading.style.display = 'none';
                this.summaryContent.style.display = 'none';
                this.saveSummaryBtn.style.display = 'none';
                this.copySummaryBtn.style.display = 'none';
                this.generateSummaryBtn.style.display = 'inline-block';
                this.summaryContent.innerHTML = '';
            }

            async generateSummary() {
                // 获取所有会议文本
                const meetingText = this.getMeetingText();
                if (!meetingText.trim()) {
                    alert('没有会议内容可以总结');
                    return;
                }

                // 获取总结类型
                const summaryType = document.querySelector('input[name="summaryType"]:checked').value;

                // 显示加载提示
                const loadingText = this.summaryLoading.querySelector('p');
                const loadingMessages = {
                    'brief': '正在生成简要总结，请稍候...',
                    'detailed': '正在生成详细总结，请稍候...',
                    'action': '正在提取行动项，请稍候...'
                };
                loadingText.textContent = loadingMessages[summaryType] || '正在生成会议总结，请稍候...';

                // 显示加载状态
                this.summaryLoading.style.display = 'block';
                this.summaryContent.style.display = 'none';
                this.generateSummaryBtn.style.display = 'none';

                try {
                    // 调用新的会议总结API
                    const summary = await this.callMeetingSummaryAPI(meetingText, summaryType);
                    this.displaySummary(summary, summaryType);
                } catch (error) {
                    console.error('生成总结失败:', error);
                    this.summaryLoading.style.display = 'none';
                    this.generateSummaryBtn.style.display = 'inline-block';
                    alert('生成总结失败: ' + error.message);
                }
            }

            getMeetingText() {
                const textElements = this.textDisplay.querySelectorAll('.text-line.final');
                let meetingText = '';

                textElements.forEach((element, index) => {
                    const textContent = element.childNodes[0].textContent || element.textContent;
                    const timestamp = element.querySelector('.timestamp')?.textContent || '';
                    meetingText += `[${timestamp}] ${textContent}\n`;
                });

                return meetingText;
            }

            async callMeetingSummaryAPI(meetingText, summaryType) {
                /**
                 * 调用新的会议总结API
                 * 该API会自动进行两步处理：1. 优化ASR文本 2. 生成对应总结
                 */
                const requestData = {
                    meeting_text: meetingText,
                    summary_type: summaryType,
                    model: 'gemini-1.5-flash',
                    temperature: 0.3
                };

                const response = await fetch('/api/meeting/summary', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '请求失败');
                }

                const data = await response.json();

                // 如果开启调试模式，可以在控制台查看优化后的文本
                if (data.optimized_text) {
                    console.log('优化后的文本:', data.optimized_text);
                }

                return data.summary;
            }

            async callLLMSummary(meetingText, summaryType) {
                /**
                 * 原有的LLM调用方法（保留作为备用）
                 * 注意：这个方法现在只用于向后兼容，推荐使用callMeetingSummaryAPI
                 */
                const prompts = {
                    brief: `你是一个专业的会议记录分析专家。以下是会议记录文本，请提取关键要点并进行简要总结。

**会议记录：**
${meetingText}

**分析要求：**
1. **关键信息提取**: 重点关注决策、行动项、重要讨论点
2. **逻辑梳理**: 整理会议的主要脉络和核心内容
3. **准确表达**: 用准确、专业的语言表达总结内容

**输出格式：**
## 📋 会议简要总结

### 🎯 主要议题
- [列出主要讨论的议题]

### ✅ 关键决定
- [列出重要决定和结论]

### 💡 重要信息
- [列出其他重要信息]

`,

                    detailed: `你是一个资深的会议分析师，擅长提取和整理完整的会议信息。以下是会议记录文本，请进行详细分析和总结。

**会议记录：**
${meetingText}

**分析要求：**
1. **全面分析**: 深入分析会议的各个方面和层次
2. **逻辑梳理**: 组织和梳理会议内容的逻辑结构
3. **专业表达**: 使用准确、专业的商务语言进行表述
4. **细节保留**: 保留重要的讨论细节和关键信息

**输出格式：**
## 📊 会议详细总结

### 🎯 会议概述
[描述会议的整体情况、背景和目标]

### 💬 讨论内容
[详细描述各个议题的讨论过程，保持逻辑清晰]

### 📋 决策事项
[列出所有决定和决策]

### 🔍 关键观点
[记录重要的观点和建议]

### 📅 后续安排
[整理后续计划或安排]

`,

                    action: `你是一个专业的项目管理专家，擅长从会议记录中提取可执行的行动项。以下是会议记录文本，请提取准确的行动项和待办事项。

**会议记录：**
${meetingText}

**提取要求：**
1. **准确识别**: 准确识别会议中的行动项
2. **责任明确**: 识别和明确任务的负责人
3. **时间明确**: 准确提取时间节点和截止日期
4. **任务具体**: 将模糊的表述转化为具体可执行的任务
5. **优先级判断**: 根据讨论内容判断任务的重要性和紧急性

**输出格式：**
## 📋 行动项总结

### ✅ 待办事项
- **任务**: [具体的待办任务]
- **负责人**: [负责人姓名或部门，如有提及]
- **截止时间**: [时间节点]
- **优先级**: [高/中/低，基于讨论内容判断]

### 🔄 跟进事项
- [需要持续跟进的事项]

### 📅 下次会议议题
- [下次会议需要讨论的内容]

### ⚠️ 注意事项
- [需要特别注意的事项和风险点]

`,

                    optimize: `你是一个专业的语音识别文本优化专家。以下是通过ASR(语音识别)技术转换的会议记录文本，由于语音识别技术的局限性，文本中存在各种识别错误。请帮助我优化和修正这些文本，确保优化后的文本语义准确、语言流畅、逻辑清晰，同时完全保持会议记录的原始含义和重要信息。

**原始ASR识别文本：**
${meetingText}

**详细优化要求：**

1. **同音字错误修正**：
   - 修正常见同音字错误：在/再、的/得、做/作、那/哪、因为/应为、以后/已后、现在/现再等
   - 根据上下文语境选择正确用字
   - 特别注意动词、介词、助词的正确使用

2. **标点符号优化**：
   - 补充缺失的逗号、句号、问号、感叹号、冒号、分号
   - 优化语句断句，提升文本可读性
   - 为直接引语添加引号
   - 合理使用顿号分隔并列成分

3. **语法和表达优化**：
   - 修正语序错误和不通顺的表达
   - 补充缺失的主语、谓语、宾语
   - 修正时态和语态错误
   - 优化口语化表达，使其更加书面化和专业

4. **格式和结构保持**：
   - 严格保持原始时间戳格式：[HH:MM:SS]
   - 保持会议记录的时间顺序
   - 维持发言人信息（如有）
   - 保持段落结构的逻辑性

5. **内容准确性**：
   - 保持原意不变，只进行必要的修正
   - 对专业术语和人名根据上下文进行合理推断
   - 不添加原文中没有的信息
   - 保持会议内容的真实性和完整性

**输出格式要求：**

[在此输出完整的优化后会议记录，严格保持时间戳格式]

`
                };

                // 根据功能类型设置合适的Token限制
                const tokenLimits = {
                    'brief': 1200,      // 简要总结需要适中的空间
                    'detailed': 1800,   // 详细总结需要更多空间
                    'action': 1500,     // 行动项需要较多空间列举任务
                    'optimize': 2000    // 优化功能需要最多空间输出完整文本
                };

                const requestData = {
                    model: 'gemini-1.5-flash',
                    messages: [
                        { role: 'user', content: prompts[summaryType] }
                    ],
                    temperature: 0.3,
                    max_tokens: tokenLimits[summaryType] || 1000
                };

                const response = await fetch('/llm/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '请求失败');
                }

                const data = await response.json();
                return data.content;
            }

            displaySummary(summary, summaryType) {
                this.summaryLoading.style.display = 'none';
                this.summaryContent.style.display = 'block';
                this.saveSummaryBtn.style.display = 'inline-block';
                this.copySummaryBtn.style.display = 'inline-block';

                // 根据类型更新保存按钮文本
                const buttonTexts = {
                    'brief': '保存简要总结',
                    'detailed': '保存详细总结',
                    'action': '保存行动项'
                };
                this.saveSummaryBtn.textContent = buttonTexts[summaryType] || '保存总结';

                // 将Markdown格式转换为HTML显示
                this.summaryContent.innerHTML = this.markdownToHtml(summary);
            }

            markdownToHtml(markdown) {
                let html = markdown
                    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                    .replace(/^- (.*$)/gm, '<li>$1</li>')
                    .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
                    .replace(/\n\n/g, '<br><br>')
                    .replace(/\n/g, '<br>');

                // 为时间戳添加特殊样式（格式：[HH:MM:SS]）
                html = html.replace(/\[(\d{2}:\d{2}:\d{2})\]/g, '<span class="timestamp">[$1]</span>');

                return html;
            }

            saveSummary() {
                const summaryText = this.summaryContent.textContent || this.summaryContent.innerText;
                const meetingDate = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                const summaryType = document.querySelector('input[name="summaryType"]:checked').value;

                // 根据类型设置不同的文件名
                let fileName;
                switch(summaryType) {
                    case 'brief':
                        fileName = `会议简要总结_${meetingDate}.txt`;
                        break;
                    case 'detailed':
                        fileName = `会议详细总结_${meetingDate}.txt`;
                        break;
                    case 'action':
                        fileName = `会议行动项_${meetingDate}.txt`;
                        break;
                    default:
                        fileName = `会议总结_${meetingDate}.txt`;
                }

                const blob = new Blob([summaryText], { type: 'text/plain;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }

            async copySummary() {
                const summaryText = this.summaryContent.textContent || this.summaryContent.innerText;

                try {
                    await navigator.clipboard.writeText(summaryText);
                    // 临时改变按钮文本以显示成功
                    const originalText = this.copySummaryBtn.textContent;
                    this.copySummaryBtn.textContent = '✅ 已复制';
                    setTimeout(() => {
                        this.copySummaryBtn.textContent = originalText;
                    }, 2000);
                } catch (error) {
                    console.error('复制失败:', error);
                    alert('复制失败，请手动选择文本复制');
                }
            }
        }

        // 初始化客户端
        const client = new ASRWebSocketClient();

        // 定期发送心跳
        setInterval(() => client.sendHeartbeat(), 30000);
    </script>
</body>
</html>
