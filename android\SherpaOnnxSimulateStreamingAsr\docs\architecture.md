# 系统架构设计文档

## 整体架构概览

### 1. 系统架构图

```mermaid
graph TB
    subgraph "Presentation Layer (表示层)"
        A[MainActivity]
        B[HomeScreen]
        C[HelpScreen]
        D[Navigation System]
        E[UI Theme]
    end
    
    subgraph "Business Logic Layer (业务逻辑层)"
        F[SimulateStreamingAsr]
        G[Audio Processing]
        H[VAD Controller]
        I[ASR Controller]
    end
    
    subgraph "Data Layer (数据层)"
        J[OfflineRecognizer]
        K[Vad]
        L[AudioRecord]
        M[Model Configs]
    end
    
    subgraph "Native Layer (原生层)"
        N[Sherpa-ONNX C++]
        O[ONNX Runtime]
        P[Audio Drivers]
    end
    
    A --> F
    B --> G
    G --> H
    G --> I
    H --> K
    I --> J
    J --> N
    K --> N
    L --> P
    F --> M
```

### 2. 模块依赖关系

```mermaid
graph LR
    subgraph "UI Module"
        A[MainActivity]
        B[Screens]
        C[Navigation]
        D[Theme]
    end
    
    subgraph "Core Module"
        E[SimulateStreamingAsr]
        F[Audio Processing]
    end
    
    subgraph "Recognition Module"
        G[OfflineRecognizer]
        H[Vad]
        I[Model Configs]
    end
    
    subgraph "System Module"
        J[AudioRecord]
        K[Permissions]
        L[Assets]
    end
    
    A --> E
    B --> F
    E --> G
    E --> H
    F --> J
    G --> I
    H --> I
    E --> L
    A --> K
```

## 详细架构分析

### 3. 表示层 (Presentation Layer)

```mermaid
classDiagram
    class MainActivity {
        -permissions: Array<String>
        +onCreate(Bundle)
        +onRequestPermissionsResult()
        -initializeComponents()
    }
    
    class MainScreen {
        +navController: NavHostController
        +Composable()
    }
    
    class HomeScreen {
        -isStarted: Boolean
        -resultList: MutableList<String>
        +onRecordingButtonClick()
        +Composable()
    }
    
    class HelpScreen {
        +Composable()
    }
    
    class NavRoutes {
        +Home: NavRoutes
        +Help: NavRoutes
    }
    
    class BarItem {
        +title: String
        +image: ImageVector
        +route: String
    }
    
    MainActivity --> MainScreen
    MainScreen --> HomeScreen
    MainScreen --> HelpScreen
    MainScreen --> NavRoutes
    NavRoutes --> BarItem
```

### 4. 业务逻辑层 (Business Logic Layer)

```mermaid
classDiagram
    class SimulateStreamingAsr {
        -_recognizer: OfflineRecognizer?
        -_vad: Vad?
        +recognizer: OfflineRecognizer
        +vad: Vad
        +initOfflineRecognizer(AssetManager, Application)
        +initVad(AssetManager)
    }
    
    class AudioProcessor {
        -audioRecord: AudioRecord?
        -samplesChannel: Channel<FloatArray>
        -sampleRateInHz: Int
        +startRecording()
        +stopRecording()
        +processAudioStream()
    }
    
    class VadController {
        -buffer: ArrayList<Float>
        -offset: Int
        -windowSize: Int
        -isSpeechStarted: Boolean
        +processVadWindow(FloatArray)
        +detectSpeechActivity(): Boolean
    }
    
    class AsrController {
        -startTime: Long
        -lastText: String
        +performRecognition(FloatArray): String
        +createStream(): OfflineStream
    }
    
    SimulateStreamingAsr --> AudioProcessor
    AudioProcessor --> VadController
    AudioProcessor --> AsrController
```

### 5. 数据层 (Data Layer)

```mermaid
classDiagram
    class OfflineRecognizer {
        -ptr: Long
        +createStream(): OfflineStream
        +decode(OfflineStream)
        +getResult(OfflineStream): OfflineRecognizerResult
    }
    
    class OfflineStream {
        -ptr: Long
        +acceptWaveform(FloatArray, Int)
        +inputFinished()
    }
    
    class Vad {
        -ptr: Long
        -config: VadModelConfig
        +acceptWaveform(FloatArray)
        +isSpeechDetected(): Boolean
        +reset()
    }
    
    class OfflineRecognizerConfig {
        +modelConfig: OfflineModelConfig
        +ruleFsts: String
        +hr: HomophoneReplacerConfig
    }
    
    class VadModelConfig {
        +sileroVadModelConfig: SileroVadModelConfig
        +sampleRate: Int
        +numThreads: Int
        +provider: String
    }
    
    OfflineRecognizer --> OfflineStream
    OfflineRecognizer --> OfflineRecognizerConfig
    Vad --> VadModelConfig
```

## 设计模式应用

### 6. 设计模式实现

```mermaid
flowchart TD
    subgraph "Singleton Pattern"
        A[SimulateStreamingAsr Object]
        A1[_recognizer: OfflineRecognizer?]
        A2[_vad: Vad?]
        A --> A1
        A --> A2
    end
    
    subgraph "Factory Pattern"
        B[Model Config Factory]
        B1[getOfflineModelConfig()]
        B2[getVadModelConfig()]
        B --> B1
        B --> B2
    end
    
    subgraph "Observer Pattern"
        C[State Management]
        C1[remember { mutableStateOf() }]
        C2[LazyColumn State]
        C3[Navigation State]
        C --> C1
        C --> C2
        C --> C3
    end
    
    subgraph "Strategy Pattern"
        D[Model Selection]
        D1[ASR Model Types]
        D2[VAD Model Types]
        D3[Audio Processing Strategies]
        D --> D1
        D --> D2
        D --> D3
    end
```

### 7. 并发处理架构

```mermaid
sequenceDiagram
    participant UI as UI Thread
    participant IO as IO Dispatcher
    participant Default as Default Dispatcher
    participant Native as Native Layer
    
    UI->>UI: User clicks record button
    UI->>IO: Launch audio recording coroutine
    UI->>Default: Launch audio processing coroutine
    
    loop Audio Recording
        IO->>Native: AudioRecord.read()
        Native-->>IO: Audio samples
        IO->>Default: Send samples via Channel
    end
    
    loop Audio Processing
        Default->>Default: Receive from Channel
        Default->>Native: VAD.acceptWaveform()
        Native-->>Default: Speech detection result
        
        alt Speech detected
            Default->>Native: Recognizer.decode()
            Native-->>Default: Recognition result
            Default->>UI: Update UI state
        end
    end
```

## 数据流架构

### 8. 音频数据流

```mermaid
flowchart LR
    A[Microphone] --> B[AudioRecord]
    B --> C[Raw PCM Data]
    C --> D[Float Array Conversion]
    D --> E[Samples Channel]
    E --> F[Audio Buffer]
    F --> G[VAD Processing]
    G --> H{Speech Detected?}
    H -->|Yes| I[ASR Processing]
    H -->|No| J[Continue Monitoring]
    I --> K[Recognition Result]
    K --> L[UI Update]
    J --> F
```

### 9. 状态管理流

```mermaid
stateDiagram-v2
    [*] --> Idle
    Idle --> PermissionCheck: App Start
    PermissionCheck --> Ready: Permission Granted
    PermissionCheck --> Error: Permission Denied
    Ready --> Recording: Start Button Clicked
    Recording --> Processing: Audio Data Available
    Processing --> Recognition: Speech Detected
    Recognition --> ResultUpdate: Text Generated
    ResultUpdate --> Processing: Continue Recording
    Recording --> Ready: Stop Button Clicked
    Processing --> Ready: Stop Button Clicked
    Recognition --> Ready: Stop Button Clicked
    Error --> [*]: App Exit
```

## 性能优化架构

### 10. 内存管理策略

```mermaid
flowchart TD
    A[Memory Management] --> B[Object Pooling]
    A --> C[Resource Cleanup]
    A --> D[Buffer Management]
    
    B --> B1[AudioRecord Reuse]
    B --> B2[Stream Object Pool]
    
    C --> C1[Automatic Resource Release]
    C --> C2[Lifecycle-aware Cleanup]
    C --> C3[Native Memory Management]
    
    D --> D1[Fixed Buffer Size]
    D --> D2[Circular Buffer]
    D --> D3[Memory Pressure Monitoring]
```

### 11. 线程安全架构

```mermaid
flowchart TD
    A[Thread Safety] --> B[Synchronization]
    A --> C[Immutable Data]
    A --> D[Channel Communication]
    
    B --> B1[synchronized blocks]
    B --> B2[Atomic operations]
    
    C --> C1[Data Classes]
    C --> C2[Read-only Properties]
    
    D --> D1[Producer-Consumer Pattern]
    D --> D2[Coroutine Channels]
    D --> D3[Flow-based Communication]
```

## 扩展性设计

### 12. 模块化扩展架构

```mermaid
flowchart TD
    A[Core Framework] --> B[Plugin Interface]
    B --> C[ASR Plugins]
    B --> D[VAD Plugins]
    B --> E[Audio Plugins]
    B --> F[UI Plugins]
    
    C --> C1[Whisper Plugin]
    C --> C2[Wav2Vec Plugin]
    C --> C3[Custom Model Plugin]
    
    D --> D1[Silero VAD]
    D --> D2[WebRTC VAD]
    D --> D3[Custom VAD]
    
    E --> E1[File Input Plugin]
    E --> E2[Network Stream Plugin]
    E --> E3[Bluetooth Audio Plugin]
    
    F --> F1[Custom Theme Plugin]
    F --> F2[Visualization Plugin]
    F --> F3[Export Plugin]
```

### 13. 配置管理架构

```mermaid
classDiagram
    class ConfigManager {
        +loadModelConfig(): ModelConfig
        +loadAudioConfig(): AudioConfig
        +loadUIConfig(): UIConfig
        +saveUserPreferences()
    }
    
    class ModelConfig {
        +asrModelType: Int
        +vadModelType: Int
        +numThreads: Int
        +provider: String
    }
    
    class AudioConfig {
        +sampleRate: Int
        +bufferSize: Int
        +audioFormat: Int
        +channelConfig: Int
    }
    
    class UIConfig {
        +theme: Theme
        +language: String
        +autoScroll: Boolean
    }
    
    ConfigManager --> ModelConfig
    ConfigManager --> AudioConfig
    ConfigManager --> UIConfig
```

## 安全性架构

### 14. 安全性考虑

```mermaid
flowchart TD
    A[Security Architecture] --> B[Permission Management]
    A --> C[Data Protection]
    A --> D[Resource Access Control]
    
    B --> B1[Runtime Permission Checks]
    B --> B2[Permission State Monitoring]
    B --> B3[Graceful Degradation]
    
    C --> C1[Audio Data Encryption]
    C --> C2[Temporary Data Cleanup]
    C --> C3[Secure Storage]
    
    D --> D1[Asset Access Control]
    D --> D2[Model File Validation]
    D --> D3[Network Security]
```

这个架构文档详细描述了 SherpaOnnx 应用的系统架构，包括各层之间的关系、设计模式的应用、并发处理机制、性能优化策略以及扩展性设计。通过这些架构图和说明，开发者可以更好地理解系统的整体设计思路和实现方式。