# SherpaOnnx WebSocket Android 项目文档

## 项目概述

SherpaOnnx WebSocket 是一个基于 Android 的实时语音识别应用，通过 WebSocket 连接将音频数据发送到服务器进行语音转文字处理。该项目使用了 Next-gen <PERSON><PERSON><PERSON> 技术栈，提供高质量的语音识别服务。

## 主要功能模块

### 1. 音频录制模块 (AudioRecord)
- **功能**: 实时录制麦克风音频
- **技术**: Android AudioRecord API
- **配置**: 
  - 采样率: 16kHz
  - 声道: 单声道 (MONO)
  - 编码格式: PCM 16-bit
  - 音频源: 麦克风

### 2. WebSocket 通信模块 (MyWebsocketClient)
- **功能**: 与服务器建立 WebSocket 连接，发送音频数据并接收识别结果
- **技术**: Java-WebSocket 库
- **协议**: WebSocket (ws:// 或 wss://)
- **数据格式**: 二进制音频数据 (Float32 数组)

### 3. 语音识别结果处理模块 (SpeechContent)
- **功能**: 解析服务器返回的识别结果
- **数据结构**: 
  - `text`: 识别的文本内容
  - `segment`: 语音片段标识符
- **显示**: 实时更新 UI 显示识别结果

### 4. 用户界面模块 (MainActivity)
- **功能**: 提供用户交互界面
- **组件**:
  - WebSocket 服务器地址输入框
  - 连接/断开按钮
  - 开始/停止录音按钮
  - 识别结果显示区域

### 5. 权限管理模块
- **功能**: 管理音频录制权限
- **权限**: `RECORD_AUDIO`, `INTERNET`
- **处理**: 动态权限请求和验证

### 6. 音频处理模块 (WaveReader)
- **功能**: 音频文件读取工具类
- **技术**: JNI 调用 Native 库
- **支持**: 从 Assets 或文件系统读取 WAV 文件

## 代码执行流程

### 应用启动流程

```mermaid
flowchart TD
    A[应用启动] --> B[MainActivity.onCreate]
    B --> C[请求音频录制权限]
    C --> D{权限是否授予?}
    D -->|是| E[初始化UI组件]
    D -->|否| F[应用退出]
    E --> G[等待用户操作]
```

### WebSocket 连接流程

```mermaid
flowchart TD
    A[用户点击连接按钮] --> B[获取输入的服务器地址]
    B --> C[创建 MyWebsocketClient 实例]
    C --> D[设置回调接口]
    D --> E[发起 WebSocket 连接]
    E --> F{连接是否成功?}
    F -->|是| G[onOpen 回调]
    F -->|否| H[onError 回调]
    G --> I[更新UI状态]
    I --> J[启用录音按钮]
    H --> K[显示错误信息]
```

### 音频录制和识别流程

```mermaid
flowchart TD
    A[用户点击开始录音] --> B[初始化 AudioRecord]
    B --> C[开始录音]
    C --> D[创建录音线程]
    D --> E[循环读取音频数据]
    E --> F[音频格式转换]
    F --> G[Short[] -> Float[]]
    G --> H[Float[] -> ByteBuffer]
    H --> I{WebSocket 是否连接?}
    I -->|是| J[发送音频数据到服务器]
    I -->|否| K[丢弃音频数据]
    J --> L[等待服务器响应]
    L --> M[onMessage 回调]
    M --> N[解析 JSON 响应]
    N --> O[更新识别结果显示]
    O --> P{是否继续录音?}
    P -->|是| E
    P -->|否| Q[停止录音]
    Q --> R[释放 AudioRecord 资源]
```

### 数据处理流程

```mermaid
flowchart TD
    A[AudioRecord 读取原始数据] --> B[Short[] 16-bit PCM]
    B --> C[转换为 Float[] 归一化]
    C --> D[Float 值 / 32768.0f]
    D --> E[创建 ByteBuffer]
    E --> F[设置字节序为 LITTLE_ENDIAN]
    F --> G[写入 Float 数据]
    G --> H[获取字节数组]
    H --> I[通过 WebSocket 发送]
```

### 识别结果处理流程

```mermaid
flowchart TD
    A[接收服务器消息] --> B[JSON 字符串]
    B --> C[Gson 解析为 SpeechContent]
    C --> D[提取 text 和 segment]
    D --> E[存储到 recognitionText Map]
    E --> F[按 segment 排序]
    F --> G[生成显示文本]
    G --> H[更新 UI TextView]
```

### 应用生命周期管理

```mermaid
flowchart TD
    A[应用运行] --> B{用户操作}
    B -->|连接| C[WebSocket 连接流程]
    B -->|录音| D[音频录制流程]
    B -->|断开| E[关闭 WebSocket]
    B -->|停止| F[停止录音]
    C --> G[等待下一个操作]
    D --> G
    E --> H[重置 UI 状态]
    F --> I[释放音频资源]
    H --> G
    I --> G
    G --> B
```

## 技术架构

### 依赖库
- **Android SDK**: 目标 API 34，最低 API 21
- **Kotlin**: 1.7.20
- **Java-WebSocket**: 1.4.0 - WebSocket 客户端实现
- **Gson**: 2.10.1 - JSON 序列化/反序列化
- **AndroidX**: 核心 Android 支持库

### 核心组件交互

```mermaid
classDiagram
    class MainActivity {
        -audioRecord: AudioRecord
        -websocketClient: MyWebsocketClient
        -isRecording: Boolean
        -isConnected: Boolean
        +onCreate()
        +onclickConnect()
        +onclick()
        +processSamples()
    }
    
    class MyWebsocketClient {
        -clientCallback: WebsocketClientCallback
        +onOpen()
        +onMessage()
        +onClose()
        +onError()
        +setClientCallback()
    }
    
    class SpeechContent {
        +text: String
        +segment: Long
    }
    
    class WaveReader {
        +readWaveFromAsset()
        +readWaveFromFile()
    }
    
    MainActivity --|> MyWebsocketClient.WebsocketClientCallback
    MainActivity --> MyWebsocketClient
    MainActivity --> SpeechContent
    MainActivity --> WaveReader
```

## 配置说明

### 音频配置
- **采样率**: 16000 Hz
- **声道配置**: CHANNEL_IN_MONO
- **音频格式**: ENCODING_PCM_16BIT
- **缓冲区间隔**: 100ms (0.1秒)

### WebSocket 配置
- **默认服务器**: ws://172.28.13.167:6006
- **支持协议**: ws:// 和 wss://
- **数据格式**: 二进制音频流

### 权限要求
- **RECORD_AUDIO**: 录制音频权限
- **INTERNET**: 网络访问权限

## 使用说明

1. **启动应用**: 自动请求音频录制权限
2. **输入服务器地址**: 在输入框中填写 WebSocket 服务器地址
3. **建立连接**: 点击"连接"按钮连接到服务器
4. **开始识别**: 点击"开始"按钮开始录音和语音识别
5. **查看结果**: 识别结果实时显示在文本区域
6. **停止识别**: 点击"停止"按钮结束录音
7. **断开连接**: 点击"断开"按钮关闭 WebSocket 连接

## 注意事项

- 确保设备有麦克风权限
- 需要网络连接访问 WebSocket 服务器
- 音频数据以 100ms 间隔发送，确保实时性
- 识别结果按语音片段分段显示
- 应用支持 Android API 21 及以上版本