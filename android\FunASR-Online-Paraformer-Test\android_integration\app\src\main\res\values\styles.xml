<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Custom Button Styles -->
    <style name="PrimaryButton" parent="Widget.MaterialComponents.Button">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:padding">12dp</item>
        <item name="backgroundTint">@color/primary_blue</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="SecondaryButton" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">16sp</item>
        <item name="android:padding">12dp</item>
        <item name="strokeColor">@color/primary_blue</item>
        <item name="android:textColor">@color/primary_blue</item>
    </style>

    <style name="RecordButton" parent="Widget.MaterialComponents.Button">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:padding">12dp</item>
        <item name="backgroundTint">@color/accent_green</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <!-- Text Styles -->
    <style name="TitleText">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="SectionTitle">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="StatusText">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:padding">8dp</item>
    </style>

    <style name="ResultText">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:lineSpacingExtra">2dp</item>
    </style>

    <!-- Input Styles -->
    <style name="InputField" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
    </style>

    <!-- Card Styles -->
    <style name="StatusCard" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">2dp</item>
        <item name="android:layout_margin">4dp</item>
        <item name="cardBackgroundColor">@color/background_light</item>
    </style>

    <style name="ResultCard" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">4dp</item>
        <item name="cardBackgroundColor">@color/white</item>
    </style>
</resources>
