package com.k2fsa.sherpa.onnx

import android.content.Context

/**
 * LLM提供商枚举
 */
enum class LLMProvider(val displayName: String, val id: String) {
    GEMINI("Gemini 2.0", "gemini"),
    DEEPSEEK("DeepSeek", "deepseek");
    
    companion object {
        fun fromId(id: String): LLMProvider {
            return values().find { it.id == id } ?: GEMINI
        }
    }
}

/**
 * LLM配置接口
 * 定义所有LLM提供商需要实现的通用方法
 */
interface LLMConfig {
    /**
     * 获取提供商类型
     */
    fun getProvider(): LLMProvider
    
    /**
     * 检查API密钥是否已配置
     */
    fun isApiKeyConfigured(context: Context): Boolean
    
    /**
     * 获取API端点URL
     */
    fun getApiUrl(context: Context): String
    
    /**
     * 获取API密钥
     */
    fun getApiKey(context: Context): String
    
    /**
     * 获取模型名称
     */
    fun getModelName(): String
    
    /**
     * 构建请求体
     */
    fun buildRequestBody(prompt: String): String
    
    /**
     * 解析响应内容
     */
    fun parseResponse(response: String): String
    
    /**
     * 获取请求头
     */
    fun getHeaders(context: Context): Map<String, String>
}

/**
 * LLM请求结果
 */
data class LLMResult(
    val success: Boolean,
    val content: String,
    val error: String? = null
)
