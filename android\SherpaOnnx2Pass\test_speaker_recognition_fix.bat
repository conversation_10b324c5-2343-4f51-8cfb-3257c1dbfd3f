@echo off
chcp 65001 >nul

echo === 声纹识别修复验证脚本 ===
echo 此脚本将帮助您验证声纹识别问题是否已修复
echo.

echo 1. 检查设备连接...
adb devices
if %errorlevel% neq 0 (
    echo ❌ ADB未找到或设备未连接
    pause
    exit /b 1
)

echo.
echo 2. 清除应用日志...
adb logcat -c

echo.
echo 3. 开始监控关键日志...
echo 请按照以下步骤操作，脚本将实时显示相关日志：
echo.
echo 📋 测试步骤：
echo    1. 启动 SherpaOnnx2Pass 应用
echo    2. 等待初始化完成
echo    3. 进入设置 -^> 声纹管理
echo    4. 注册新声纹（输入姓名，录制音频）
echo    5. 返回主界面，立即测试录音识别
echo    6. 删除刚注册的声纹
echo    7. 再次测试录音识别
echo.
echo 🔍 关键日志监控中...
echo 按 Ctrl+C 停止监控
echo.

REM 监控关键日志
adb logcat | findstr /i "SettingsActivity SingleModelASREngine 声纹 speaker 添加 删除 注册 识别 Speaker"

echo.
echo === 验证完成 ===
pause
