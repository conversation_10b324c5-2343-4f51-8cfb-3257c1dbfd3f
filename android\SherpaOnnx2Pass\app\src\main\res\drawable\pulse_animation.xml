<?xml version="1.0" encoding="utf-8"?>
<!-- Simple pulse animation using traditional approach -->
<animation-list xmlns:android="http://schemas.android.com/apk/res/android"
    android:oneshot="false">

    <item android:duration="200">
        <shape android:shape="oval">
            <solid android:color="@color/apple_red_alpha_10" />
            <size android:width="120dp" android:height="120dp" />
        </shape>
    </item>

    <item android:duration="200">
        <shape android:shape="oval">
            <solid android:color="@color/apple_red_alpha_20" />
            <size android:width="140dp" android:height="140dp" />
        </shape>
    </item>

    <item android:duration="200">
        <shape android:shape="oval">
            <solid android:color="@color/apple_red_alpha_10" />
            <size android:width="160dp" android:height="160dp" />
        </shape>
    </item>

    <item android:duration="200">
        <shape android:shape="oval">
            <solid android:color="@color/apple_red_alpha_20" />
            <size android:width="140dp" android:height="140dp" />
        </shape>
    </item>

</animation-list>
