<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/app_name"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- 服务器配置 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/server_config"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_address"
            android:layout_gravity="center_vertical" />

        <EditText
            android:id="@+id/et_server_host"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="*************"
            android:inputType="text" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/server_port"
            android:layout_gravity="center_vertical" />

        <EditText
            android:id="@+id/et_server_port"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:hint="8000"
            android:inputType="number" />

    </LinearLayout>

    <!-- 连接按钮 -->
    <Button
        android:id="@+id/btn_connect"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/connect_server"
        android:layout_marginBottom="16dp" />

    <!-- 状态显示 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/status"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/tv_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/not_connected"
        android:textSize="16sp"
        android:padding="8dp"
        android:background="@drawable/status_background"
        android:layout_marginBottom="16dp" />

    <!-- 录音控制 -->
    <Button
        android:id="@+id/btn_record"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/start_recording"
        android:enabled="false"
        android:layout_marginBottom="16dp" />

    <!-- 识别结果 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/recognition_results"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <ScrollView
        android:id="@+id/scroll_results"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/results_background">

        <TextView
            android:id="@+id/tv_results"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/waiting_results"
            android:textSize="14sp"
            android:padding="12dp"
            android:textIsSelectable="true" />

    </ScrollView>

    <!-- 结果操作按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="8dp">

        <Button
            android:id="@+id/btn_clear_results"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/clear_results"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/btn_save_results"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/save_results"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/btn_meeting_summary"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/meeting_summary"
            android:layout_marginStart="4dp" />

    </LinearLayout>

</LinearLayout>