# SherpaOnnx Audio Tagging 架构设计

## 系统架构图

```mermaid
graph TB
    subgraph "Android Application Layer"
        MA[MainActivity]
        HOME[Home.kt - UI Layer]
        THEME[UI Theme]
    end
    
    subgraph "Business Logic Layer"
        TAGGER[Tagger - Singleton Manager]
        AT[AudioTagging - Core Engine]
        OS[OfflineStream - Audio Processor]
    end
    
    subgraph "System Layer"
        AR[AudioRecord - Android API]
        JNI[JNI Bridge]
        NATIVE["Native C++ Library\n(sherpa-onnx-jni)"]
    end
    
    subgraph "AI Model Layer"
        ONNX["ONNX Runtime"]
        MODEL1["Zipformer Models"]
        MODEL2["CED Models"]
        LABELS["Class Labels"]
    end
    
    MA --> HOME
    MA --> TAGGER
    HOME --> TAGGER
    HOME --> AR
    TAGGER --> AT
    AT --> OS
    AT --> JNI
    OS --> JNI
    JNI --> NATIVE
    NATIVE --> ONNX
    ONNX --> MODEL1
    ONNX --> MODEL2
    ONNX --> LABELS
```

## 应用启动流程

```mermaid
sequenceDiagram
    participant User
    participant MainActivity
    participant Tagger
    participant AudioTagging
    participant AssetManager
    participant Home
    
    User->>MainActivity: 启动应用
    MainActivity->>MainActivity: onCreate()
    MainActivity->>MainActivity: 请求录音权限
    MainActivity->>Tagger: initTagger(assetManager)
    Tagger->>AudioTagging: 创建实例
    AudioTagging->>AssetManager: 加载模型文件
    AudioTagging->>AudioTagging: 初始化JNI
    MainActivity->>Home: 设置UI内容
    Home->>User: 显示主界面
```

## 音频识别完整流程

```mermaid
flowchart TD
    START([用户点击开始]) --> PERM{检查录音权限}
    PERM -->|无权限| DENY[显示权限错误]
    PERM -->|有权限| SETUP[设置AudioRecord]
    
    SETUP --> RECORD[开始录音线程]
    RECORD --> COLLECT[采集音频数据\n16kHz, 16-bit PCM]
    COLLECT --> BUFFER[存储到缓冲区]
    BUFFER --> CHECK{用户是否停止?}
    CHECK -->|否| COLLECT
    CHECK -->|是| STOP[停止录音]
    
    STOP --> FLATTEN[合并音频片段]
    FLATTEN --> STREAM[创建OfflineStream]
    STREAM --> FEED[将音频数据传入流]
    FEED --> COMPUTE[调用AI模型推理]
    
    COMPUTE --> RESULTS[获取识别结果]
    RESULTS --> FILTER[按阈值过滤]
    FILTER --> DISPLAY[在UI中显示结果]
    DISPLAY --> END([结束])
    
    DENY --> END
```

## 数据流图

```mermaid
graph LR
    subgraph "Input"
        MIC[麦克风]
        USER[用户交互]
    end
    
    subgraph "Processing"
        AR[AudioRecord]
        BUFFER[音频缓冲区]
        STREAM[OfflineStream]
        AI[AI推理引擎]
    end
    
    subgraph "Output"
        EVENTS[音频事件列表]
        UI[用户界面]
    end
    
    MIC --> AR
    USER --> AR
    AR --> BUFFER
    BUFFER --> STREAM
    STREAM --> AI
    AI --> EVENTS
    EVENTS --> UI
    UI --> USER
```

## 类关系图

```mermaid
classDiagram
    class MainActivity {
        +onCreate()
        +onRequestPermissionsResult()
        -permissions: Array~String~
    }
    
    class Home {
        +MyApp()
        +ViewRow()
        +Flatten()
        -audioRecord: AudioRecord
        -sampleRateInHz: Int
    }
    
    class Tagger {
        -_tagger: AudioTagging?
        +tagger: AudioTagging
        +initTagger()
    }
    
    class AudioTagging {
        -ptr: Long
        +createStream(): OfflineStream
        +compute(): ArrayList~AudioEvent~
        +release()
    }
    
    class OfflineStream {
        +ptr: Long
        +acceptWaveform()
        +release()
    }
    
    class AudioEvent {
        +name: String
        +index: Int
        +prob: Float
    }
    
    class AudioTaggingConfig {
        +model: AudioTaggingModelConfig
        +labels: String
        +topK: Int
    }
    
    MainActivity --> Home
    MainActivity --> Tagger
    Home --> Tagger
    Tagger --> AudioTagging
    AudioTagging --> OfflineStream
    AudioTagging --> AudioEvent
    AudioTagging --> AudioTaggingConfig
```

## 模型配置架构

```mermaid
graph TD
    CONFIG[AudioTaggingConfig] --> MODEL[AudioTaggingModelConfig]
    CONFIG --> LABELS[Labels File]
    CONFIG --> TOPK[TopK Setting]
    
    MODEL --> ZIPFORMER[ZipformerConfig]
    MODEL --> CED[CED Config]
    MODEL --> THREADS[Thread Count]
    MODEL --> DEBUG[Debug Mode]
    MODEL --> PROVIDER[Provider CPU/GPU]
    
    ZIPFORMER --> ZMODEL["model.int8.onnx"]
    CED --> CMODEL["model.int8.onnx"]
    
    LABELS --> CSV["class_labels_indices.csv"]
```

## 线程模型

```mermaid
graph TB
    subgraph "Main Thread (UI)"
        UI[UI Components]
        COMPOSE[Compose Recomposition]
    end
    
    subgraph "Recording Thread"
        RECORD[Audio Recording]
        BUFFER[Buffer Management]
    end
    
    subgraph "Processing Thread"
        PROCESS[Audio Processing]
        AI[AI Inference]
    end
    
    UI -->|Start/Stop| RECORD
    RECORD -->|Audio Data| BUFFER
    BUFFER -->|Complete Audio| PROCESS
    PROCESS -->|Results| UI
    AI -->|Events| COMPOSE
```

## 内存管理

```mermaid
graph LR
    subgraph "Java/Kotlin Layer"
        JAVA[Java Objects]
        GC[Garbage Collector]
    end
    
    subgraph "JNI Bridge"
        JNI_REF[JNI References]
        PTR[Native Pointers]
    end
    
    subgraph "Native Layer"
        NATIVE[C++ Objects]
        MANUAL[Manual Memory Management]
    end
    
    JAVA --> JNI_REF
    JNI_REF --> PTR
    PTR --> NATIVE
    
    GC -.->|Cannot manage| NATIVE
    MANUAL -->|finalize()| PTR
    PTR -->|release()| JNI_REF
```

## 错误处理流程

```mermaid
flowchart TD
    START([操作开始]) --> TRY{尝试执行}
    TRY -->|成功| SUCCESS[正常流程]
    TRY -->|权限错误| PERM_ERR[显示权限提示]
    TRY -->|模型加载错误| MODEL_ERR[显示模型错误]
    TRY -->|录音错误| AUDIO_ERR[显示音频错误]
    TRY -->|JNI错误| JNI_ERR[显示系统错误]
    
    PERM_ERR --> LOG[记录日志]
    MODEL_ERR --> LOG
    AUDIO_ERR --> LOG
    JNI_ERR --> LOG
    
    LOG --> CLEANUP[清理资源]
    CLEANUP --> END([结束])
    SUCCESS --> END
```