# SherpaOnnx 语音语言识别 Android 应用

## 项目概述

这是一个基于 SherpaOnnx 的 Android 语音语言识别应用，能够实时识别用户说话的语言类型。应用使用了 Whisper 模型进行语音语言识别，支持多种语言的检测。

## 主要功能模块

### 1. 权限管理模块
- **文件**: `MainActivity.kt`
- **功能**: 处理音频录制权限的申请和管理
- **关键权限**: `RECORD_AUDIO`

### 2. 用户界面模块
- **文件**: `Home.kt`
- **功能**: 提供用户交互界面，包括开始/停止录音按钮和识别结果显示
- **技术**: Jetpack Compose

### 3. 音频录制模块
- **文件**: `Home.kt`
- **功能**: 实时录制音频数据
- **参数**: 
  - 采样率: 16kHz
  - 格式: 16-bit PCM
  - 声道: 单声道

### 4. 语音识别引擎模块
- **文件**: `SpokenLanguageIdentification.kt`, `slid.kt`
- **功能**: 基于 Whisper 模型的语音语言识别
- **模型**: Whisper Tiny 模型 (int8 量化版本)

### 5. 音频流处理模块
- **文件**: `OfflineStream.kt`
- **功能**: 处理音频数据流，为识别引擎提供标准化的音频输入

### 6. 语言映射模块
- **文件**: `slid.kt`
- **功能**: 将识别结果的语言代码映射为可读的语言名称

## 技术架构

- **开发语言**: Kotlin
- **UI框架**: Jetpack Compose
- **音频处理**: Android AudioRecord API
- **AI模型**: Whisper (ONNX格式)
- **JNI**: 通过 sherpa-onnx-jni 调用底层C++库

## 代码执行流程

```mermaid
flowchart TD
    A[应用启动] --> B[MainActivity.onCreate]
    B --> C[权限检查]
    C --> D{音频权限已授予?}
    D -->|否| E[请求权限]
    D -->|是| F[初始化Slid]
    E --> G[用户授权]
    G --> H{权限被授予?}
    H -->|否| I[显示错误并退出]
    H -->|是| F
    F --> J[加载Whisper模型]
    J --> K[显示主界面]
    K --> L[等待用户操作]
    
    L --> M[用户点击开始按钮]
    M --> N[创建AudioRecord]
    N --> O[开始录音线程]
    O --> P[循环读取音频数据]
    P --> Q[将音频数据转换为FloatArray]
    Q --> R{用户是否停止?}
    R -->|否| P
    R -->|是| S[停止录音]
    
    S --> T[合并所有音频片段]
    T --> U[创建OfflineStream]
    U --> V[将音频数据传入Stream]
    V --> W[调用Slid.compute进行识别]
    W --> X[获取语言代码]
    X --> Y[映射为可读语言名称]
    Y --> Z[显示识别结果]
    Z --> L
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style W fill:#fff3e0
    style Z fill:#e8f5e8
```

## 详细流程说明

### 1. 应用初始化流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant MA as MainActivity
    participant PM as PermissionManager
    participant Slid as Slid引擎
    
    User->>MA: 启动应用
    MA->>PM: 检查音频权限
    alt 权限未授予
        PM->>User: 请求权限
        User->>PM: 授予/拒绝权限
        alt 权限被拒绝
            PM->>MA: 权限拒绝
            MA->>User: 显示错误并退出
        end
    end
    MA->>Slid: 初始化识别引擎
    Slid->>Slid: 加载Whisper模型
    Slid->>Slid: 初始化语言映射表
    MA->>User: 显示主界面
```

### 2. 语音识别流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 用户界面
    participant AR as AudioRecord
    participant Stream as OfflineStream
    participant Engine as 识别引擎
    
    User->>UI: 点击开始按钮
    UI->>AR: 创建AudioRecord实例
    AR->>AR: 开始录音
    
    loop 录音循环
        AR->>AR: 读取音频缓冲区
        AR->>UI: 返回音频数据
        UI->>UI: 转换为FloatArray
        UI->>UI: 添加到样本列表
    end
    
    User->>UI: 点击停止按钮
    UI->>AR: 停止录音
    UI->>UI: 合并所有音频片段
    UI->>Stream: 创建音频流
    UI->>Stream: 传入音频数据
    UI->>Engine: 调用compute方法
    Engine->>Engine: Whisper模型推理
    Engine->>UI: 返回语言代码
    UI->>UI: 映射为语言名称
    UI->>User: 显示识别结果
```

### 3. 音频数据处理流程

```mermaid
flowchart LR
    A[原始音频] --> B[AudioRecord读取]
    B --> C[ShortArray缓冲区]
    C --> D[转换为FloatArray]
    D --> E[归一化 ÷32768.0f]
    E --> F[添加到样本列表]
    F --> G[合并所有片段]
    G --> H[OfflineStream处理]
    H --> I[Whisper模型输入]
    
    style A fill:#ffebee
    style I fill:#e8f5e8
```

## 关键技术点

### 1. 音频参数配置
- **采样率**: 16kHz (符合语音识别标准)
- **位深**: 16-bit PCM
- **声道**: 单声道 (MONO)
- **缓冲区**: 100ms间隔读取

### 2. 模型配置
- **模型类型**: Whisper Tiny
- **量化**: int8 (减少模型大小)
- **文件**: 
  - encoder: `tiny-encoder.int8.onnx`
  - decoder: `tiny-decoder.int8.onnx`

### 3. 内存管理
- 使用 `finalize()` 和 `release()` 方法管理JNI资源
- 音频流使用 `use` 模式确保资源释放

### 4. 线程管理
- 音频录制在独立线程中进行
- UI更新在主线程中执行
- 识别计算在后台线程完成

## 项目结构

```
app/src/main/java/com/k2fsa/sherpa/onnx/slid/
├── MainActivity.kt              # 主Activity，权限管理
├── Home.kt                     # 主界面，音频录制和识别
├── slid.kt                     # Slid引擎单例，模型初始化
├── SpokenLanguageIdentification.kt  # 识别引擎封装
├── OfflineStream.kt            # 音频流处理
└── ui/theme/                   # UI主题配置
```

## 依赖项

- **Jetpack Compose**: 现代化UI框架
- **sherpa-onnx-jni**: 底层语音识别库
- **Android AudioRecord**: 音频录制API
- **Whisper模型**: 存储在assets目录中

## 使用说明

1. 启动应用后，系统会请求音频录制权限
2. 授予权限后，应用会自动加载Whisper模型
3. 点击"Start"按钮开始录音
4. 说话后点击"Stop"按钮停止录音
5. 应用会显示识别出的语言名称

## 性能优化

- 使用int8量化模型减少内存占用
- 音频数据分块处理避免内存峰值
- JNI资源及时释放防止内存泄漏
- 单例模式避免重复加载模型