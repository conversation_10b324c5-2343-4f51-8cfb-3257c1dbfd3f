# 代码执行流程分析

## 应用启动流程

### 1. 应用初始化流程

```mermaid
flowchart TD
    A[应用启动] --> B[MainActivity.onCreate]
    B --> C[安装启动画面]
    C --> D[设置屏幕常亮]
    D --> E[设置主题]
    E --> F[设置 Compose UI]
    F --> G[请求音频权限]
    G --> H[初始化 Tagger]
    H --> I[显示 HomeScreen]
    
    G --> J[权限检查]
    J -->|权限被拒绝| K[显示错误提示]
    K --> L[退出应用]
    J -->|权限通过| M[记录权限获取成功]
    M --> I
```

### 2. Tagger 初始化流程

```mermaid
flowchart TD
    A[Tagger.initTagger] --> B{检查是否已初始化}
    B -->|已初始化| C[直接返回]
    B -->|未初始化| D[同步锁保护]
    D --> E[获取音频标记配置]
    E --> F[创建 AudioTagging 实例]
    F --> G[加载 ONNX 模型]
    G --> H[加载标签文件]
    H --> I[初始化完成]
    
    E --> J[getAudioTaggingConfig]
    J --> K[选择模型类型]
    K -->|type=0| L[Zipformer Small 模型]
    K -->|type=1| M[Zipformer 标准模型]
    K -->|type=2| N[CED Tiny 模型]
    K -->|type=3| O[CED Mini 模型]
    K -->|type=4| P[CED Small 模型]
    K -->|type=5| Q[CED Base 模型]
```

## 音频处理流程

### 3. 音频录制和处理主流程

```mermaid
flowchart TD
    A[用户点击开始按钮] --> B[检查音频权限]
    B -->|权限不足| C[显示权限错误]
    B -->|权限正常| D[创建 AudioRecord]
    D --> E[配置音频参数]
    E --> F[启动录制线程]
    F --> G[开始音频录制]
    
    G --> H[循环读取音频数据]
    H --> I[读取 100ms 音频缓冲区]
    I --> J[转换为 Float 数组]
    J --> K[添加到样本列表]
    K --> L{检查录制状态}
    L -->|继续录制| H
    L -->|停止录制| M[停止 AudioRecord]
    
    M --> N[合并所有音频样本]
    N --> O[创建 OfflineStream]
    O --> P[将音频数据传入流]
    P --> Q[执行音频标记推理]
    Q --> R[获取识别结果]
    R --> S[过滤低置信度结果]
    S --> T[更新 UI 显示]
    T --> U[释放资源]
```

### 4. 音频数据处理详细流程

```mermaid
flowchart TD
    A[音频录制线程] --> B[设置录制参数]
    B --> C[采样率: 16kHz]
    C --> D[格式: 16-bit PCM]
    D --> E[声道: 单声道]
    E --> F[缓冲区间隔: 100ms]
    
    F --> G[开始录制循环]
    G --> H[AudioRecord.read]
    H --> I[读取 ShortArray 数据]
    I --> J[转换为 FloatArray]
    J --> K[归一化: /32768.0f]
    K --> L[存储到样本列表]
    L --> M{录制状态检查}
    M -->|isStarted=true| G
    M -->|isStarted=false| N[退出录制循环]
    
    N --> O[Flatten 函数处理]
    O --> P[计算总样本数]
    P --> Q[创建合并数组]
    Q --> R[逐个复制样本]
    R --> S[返回完整音频数据]
```

## 模型推理流程

### 5. 音频标记推理流程

```mermaid
flowchart TD
    A[获取音频数据] --> B[创建 OfflineStream]
    B --> C[stream.acceptWaveform]
    C --> D[JNI 调用本地方法]
    D --> E[ONNX 模型推理]
    E --> F[获取原始预测结果]
    F --> G[tagger.compute]
    G --> H[解析预测结果]
    H --> I[创建 AudioEvent 列表]
    
    I --> J[遍历预测结果]
    J --> K[提取事件名称]
    K --> L[提取置信度分数]
    L --> M[提取类别索引]
    M --> N[创建 AudioEvent 对象]
    N --> O[添加到结果列表]
    O --> P{是否还有结果}
    P -->|是| J
    P -->|否| Q[返回完整结果列表]
    
    Q --> R[释放 OfflineStream]
```

### 6. 结果过滤和显示流程

```mermaid
flowchart TD
    A[获取 AudioEvent 列表] --> B[遍历所有事件]
    B --> C[检查置信度]
    C --> D{prob > threshold}
    D -->|是| E[格式化显示文本]
    D -->|否| F[跳过该事件]
    
    E --> G[添加到显示字符串]
    G --> H[格式: 事件名 (置信度)]
    H --> I{还有更多事件?}
    I -->|是| B
    I -->|否| J[更新 UI 状态]
    
    F --> I
    J --> K[在 HomeScreen 显示结果]
    K --> L[用户可调节阈值]
    L --> M[实时更新显示内容]
```

## UI 交互流程

### 7. 用户界面交互流程

```mermaid
flowchart TD
    A[HomeScreen 加载] --> B[显示欢迎信息]
    B --> C[显示阈值滑块]
    C --> D[显示开始/停止按钮]
    D --> E[等待用户交互]
    
    E --> F{用户操作类型}
    F -->|调节阈值| G[更新 threshold 值]
    F -->|点击按钮| H{当前状态}
    
    G --> I[实时更新显示文本]
    I --> E
    
    H -->|未开始| J[开始录制流程]
    H -->|正在录制| K[停止录制流程]
    
    J --> L[按钮文本: Stop]
    L --> M[清空结果显示]
    M --> N[启动音频处理]
    N --> E
    
    K --> O[按钮文本: Start]
    O --> P[停止音频录制]
    P --> Q[处理音频数据]
    Q --> R[显示识别结果]
    R --> E
```

## 资源管理流程

### 8. 内存和资源管理流程

```mermaid
flowchart TD
    A[资源创建] --> B[AudioRecord 实例]
    B --> C[OfflineStream 实例]
    C --> D[音频缓冲区]
    D --> E[样本数据列表]
    
    E --> F[使用资源]
    F --> G[音频录制和处理]
    G --> H[模型推理]
    H --> I[结果生成]
    
    I --> J[资源释放]
    J --> K[AudioRecord.stop]
    K --> L[OfflineStream.release]
    L --> M[清空样本列表]
    M --> N[JNI 资源清理]
    N --> O[垃圾回收]
    
    O --> P{应用退出?}
    P -->|否| A
    P -->|是| Q[Tagger 实例清理]
    Q --> R[AudioTagging.release]
    R --> S[本地库资源释放]
```

## 错误处理流程

### 9. 异常和错误处理流程

```mermaid
flowchart TD
    A[应用运行] --> B{检测到异常}
    B -->|权限异常| C[显示权限错误提示]
    B -->|音频录制异常| D[记录错误日志]
    B -->|模型加载异常| E[初始化失败处理]
    B -->|内存不足| F[资源清理]
    B -->|正常运行| G[继续执行]
    
    C --> H[Toast 提示用户]
    H --> I[退出应用]
    
    D --> J[停止录制]
    J --> K[重置 UI 状态]
    K --> L[允许重新开始]
    
    E --> M[使用默认配置]
    M --> N[重试初始化]
    N --> O{初始化成功?}
    O -->|是| G
    O -->|否| I
    
    F --> P[释放所有资源]
    P --> Q[强制垃圾回收]
    Q --> R[重新分配资源]
    R --> G
```

## 总结

该应用的核心执行流程可以概括为：

1. **初始化阶段**: 权限获取 → Tagger 初始化 → UI 准备
2. **录制阶段**: 音频采集 → 实时缓冲 → 数据累积
3. **处理阶段**: 数据合并 → 模型推理 → 结果解析
4. **显示阶段**: 结果过滤 → UI 更新 → 用户交互
5. **清理阶段**: 资源释放 → 状态重置 → 准备下次使用

整个流程采用了异步处理、资源池管理和错误恢复机制，确保应用在 Wear OS 设备上的稳定运行。