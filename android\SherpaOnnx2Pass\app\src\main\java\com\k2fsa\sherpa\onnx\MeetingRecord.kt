package com.k2fsa.sherpa.onnx

import java.text.SimpleDateFormat
import java.util.*

/**
 * 会议记录数据模型
 * 符合苹果设计哲学的简洁数据结构
 */
data class MeetingRecord(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val timestamp: Long = System.currentTimeMillis(),
    val originalContent: String,           // 原始ASR识别内容
    val optimizedContent: String = "",     // AI优化后的内容
    val summaryContent: String = "",       // 智能总结内容
    val wordCount: Int = 0,                // 字数统计
    val duration: Long = 0,                // 录音时长（秒）
    val speakerCount: Int = 0,             // 说话人数量
    val audioFilePath: String? = ""        // 录音文件路径
) {
    
    /**
     * 获取格式化的日期时间
     */
    fun getFormattedDateTime(): String {
        val sdf = SimpleDateFormat("yyyy年MM月dd日 HH:mm", Locale.getDefault())
        return sdf.format(Date(timestamp))
    }
    
    /**
     * 获取简短的日期
     */
    fun getShortDate(): String {
        val sdf = SimpleDateFormat("MM/dd HH:mm", Locale.getDefault())
        return sdf.format(Date(timestamp))
    }
    
    /**
     * 获取格式化的时长
     */
    fun getFormattedDuration(): String {
        val minutes = duration / 60
        val seconds = duration % 60
        return String.format("%02d:%02d", minutes, seconds)
    }
    
    /**
     * 获取内容预览（前50个字符）
     */
    fun getContentPreview(): String {
        val content = if (optimizedContent.isNotEmpty()) optimizedContent else originalContent
        return if (content.length > 50) {
            content.take(50) + "..."
        } else {
            content
        }
    }
    
    /**
     * 检查是否有优化内容
     */
    fun hasOptimizedContent(): Boolean = optimizedContent.isNotEmpty()
    
    /**
     * 检查是否有总结内容
     */
    fun hasSummaryContent(): Boolean = summaryContent.isNotEmpty()

    /**
     * 检查是否有录音文件
     */
    fun hasAudioFile(): Boolean = !audioFilePath.isNullOrEmpty() && java.io.File(audioFilePath!!).exists()
    
    /**
     * 获取完整的会议记录文本（用于导出）
     */
    fun getFullContent(): String {
        return buildString {
            append("📝 会议记录\n")
            append("标题: $title\n")
            append("时间: ${getFormattedDateTime()}\n")
            append("时长: ${getFormattedDuration()}\n")
            append("字数: $wordCount\n")
            if (speakerCount > 0) {
                append("说话人: $speakerCount 人\n")
            }
            append("==============================" + "\n\n")
            
            if (summaryContent.isNotEmpty()) {
                append("🤖 智能总结\n")
                append("------------------------------" + "\n")
                append("$summaryContent\n\n")
            }
            
            if (optimizedContent.isNotEmpty()) {
                append("✨ 优化内容\n")
                append("------------------------------" + "\n")
                append("$optimizedContent\n\n")
            }
            
            append("📄 原始记录\n")
            append("------------------------------" + "\n")
            append(originalContent)
        }
    }
}

/**
 * 会议记录类型枚举
 */
enum class MeetingContentType(val displayName: String, val icon: String) {
    ORIGINAL("原始记录", "📄"),
    OPTIMIZED("优化内容", "✨"),
    SUMMARY("智能总结", "🤖")
}
