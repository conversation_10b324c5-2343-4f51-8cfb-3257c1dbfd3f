# SherpaOnnx2Pass 应用架构设计

## 整体架构

### 系统架构图

```mermaid
graph TB
    subgraph "UI Layer"
        A[SingleModelActivity]
        B[UI Components]
        C[Event Handlers]
    end
    
    subgraph "Business Logic Layer"
        D[SingleModelASREngine]
        E[SpeakerDataManager]
        F[ApiKeyManager]
        G[GeminiConfig]
    end
    
    subgraph "Core Engine Layer"
        H[OnlineRecognizer]
        I[OnlineStream]
        J[SpeakerEmbeddingExtractor]
        K[SpeakerEmbeddingManager]
    end
    
    subgraph "Data Layer"
        L[SharedPreferences]
        M[File System]
        N[Encrypted Storage]
    end
    
    subgraph "External Services"
        O[Gemini AI API]
        P[Audio System]
    end
    
    A --> D
    A --> E
    A --> F
    A --> G
    B --> A
    C --> A
    
    D --> H
    D --> I
    D --> J
    D --> K
    
    E --> L
    E --> M
    F --> N
    
    G --> O
    D --> P
    
    style A fill:#e1f5fe
    style D fill:#f3e5f5
    style H fill:#fff3e0
    style O fill:#e8f5e8
```

## 模块详细设计

### 1. UI 层 (Presentation Layer)

#### SingleModelActivity
- **职责**: 主界面控制器，处理用户交互
- **关键方法**:
  - `onCreate()`: 初始化界面和引擎
  - `toggleRecording()`: 控制录音状态
  - `handleASRResult()`: 处理识别结果
  - `optimizeAsrContent()`: 触发AI优化

```mermaid
classDiagram
    class SingleModelActivity {
        -asrEngine: SingleModelASREngine
        -audioRecord: AudioRecord
        -isRecording: AtomicBoolean
        -recognitionResults: StringBuilder
        +onCreate()
        +toggleRecording()
        +handleASRResult()
        +optimizeAsrContent()
        +showSpeakerRegistrationDialog()
    }
    
    class ASRListener {
        <<interface>>
        +onResult(result: ASRResult)
        +onError(error: String)
        +onStatusChanged(status: String)
        +onSpeakerIdentified(speakerInfo: SpeakerInfo)
    }
    
    SingleModelActivity ..|> ASRListener
```

### 2. 业务逻辑层 (Business Logic Layer)

#### SingleModelASREngine
- **职责**: ASR引擎核心逻辑，协调各个组件
- **设计模式**: 观察者模式（ASRListener）
- **核心流程**:

```mermaid
sequenceDiagram
    participant UI as SingleModelActivity
    participant Engine as SingleModelASREngine
    participant Recognizer as OnlineRecognizer
    participant Speaker as SpeakerManager
    
    UI->>Engine: initialize()
    Engine->>Recognizer: 创建识别器
    Engine->>Speaker: 初始化声纹识别
    Engine-->>UI: 初始化完成
    
    UI->>Engine: processAudio(audioData)
    Engine->>Engine: 音频分段处理
    Engine->>Recognizer: decode(stream)
    Engine->>Speaker: 声纹识别
    Engine->>Engine: 结果处理
    Engine-->>UI: onResult(ASRResult)
```

#### SpeakerDataManager
- **职责**: 声纹数据持久化管理
- **存储策略**: 文件系统 + SharedPreferences
- **数据结构**:

```mermaid
erDiagram
    SPEAKER_DATA {
        string speaker_name PK
        int sample_count
        timestamp created_time
        timestamp updated_time
    }
    
    AUDIO_SAMPLE {
        string speaker_name FK
        int sample_index
        blob audio_data
        int sample_rate
        int duration
    }
    
    SPEAKER_DATA ||--o{ AUDIO_SAMPLE : contains
```

### 3. 核心引擎层 (Core Engine Layer)

#### OnlineRecognizer
- **职责**: 底层语音识别引擎
- **支持的模型类型**:
  - Transducer (编码器-解码器-连接器)
  - Paraformer (编码器-解码器)
  - Zipformer2 CTC
  - NeMo CTC

#### 识别流程状态机

```mermaid
stateDiagram-v2
    [*] --> Idle: 初始化完成
    Idle --> Buffering: 开始录音
    Buffering --> Predicting: 达到预测条件
    Predicting --> Buffering: 继续缓冲
    Predicting --> Final: 达到最终条件
    Final --> Endpoint: 检测到端点
    Endpoint --> Idle: 重置状态
    Final --> Buffering: 继续识别
    
    Buffering: 音频缓冲
    Predicting: 预测识别
    Final: 最终识别
    Endpoint: 端点检测
```

### 4. 数据层 (Data Layer)

#### 数据存储架构

```mermaid
graph LR
    subgraph "应用数据"
        A[SharedPreferences]
        B[内部文件存储]
        C[加密存储]
    end
    
    subgraph "存储内容"
        D[声纹列表]
        E[音频样本文件]
        F[API密钥]
        G[应用配置]
    end
    
    A --> D
    A --> G
    B --> E
    C --> F
    
    style A fill:#ffecb3
    style B fill:#c8e6c9
    style C fill:#ffcdd2
```

## 数据流分析

### 1. 音频处理数据流

```mermaid
flowchart LR
    A[麦克风] --> B[AudioRecord]
    B --> C[音频缓冲区]
    C --> D[分段处理]
    D --> E[OnlineStream]
    E --> F[OnlineRecognizer]
    F --> G[识别结果]
    G --> H[结果处理]
    H --> I[UI显示]
    
    C --> J[声纹提取]
    J --> K[声纹比对]
    K --> L[说话人识别]
    L --> H
```

### 2. 声纹管理数据流

```mermaid
flowchart TD
    A[用户注册声纹] --> B[录制音频样本]
    B --> C[特征提取]
    C --> D[生成声纹向量]
    D --> E[保存到文件系统]
    E --> F[更新SharedPreferences]
    
    G[声纹识别] --> H[提取当前音频特征]
    H --> I[加载已保存声纹]
    I --> J[相似度计算]
    J --> K{相似度>阈值?}
    K -->|是| L[返回说话人信息]
    K -->|否| M[返回未知说话人]
```

### 3. AI优化数据流

```mermaid
flowchart TD
    A[ASR识别结果] --> B[内容格式分析]
    B --> C[构建API请求]
    C --> D[加密获取API密钥]
    D --> E[调用Gemini API]
    E --> F{API调用成功?}
    F -->|是| G[解析响应结果]
    F -->|否| H[使用原始结果]
    G --> I[更新UI显示]
    H --> I
```

## 性能优化策略

### 1. 内存优化

```mermaid
graph TD
    A[内存优化策略] --> B[音频缓冲区管理]
    A --> C[对象池化]
    A --> D[及时释放资源]
    A --> E[避免内存泄漏]
    
    B --> B1[固定大小缓冲区]
    B --> B2[循环使用]
    
    C --> C1[FloatArray复用]
    C --> C2[StringBuilder复用]
    
    D --> D1[AudioRecord释放]
    D --> D2[Stream释放]
    
    E --> E1[Handler清理]
    E --> E2[Listener解绑]
```

### 2. 计算优化

```mermaid
graph TD
    A[计算优化] --> B[多线程处理]
    A --> C[智能端点检测]
    A --> D[批量处理]
    
    B --> B1[UI线程]
    B --> B2[录音线程]
    B --> B3[识别线程]
    B --> B4[网络线程]
    
    C --> C1[减少无效计算]
    C --> C2[动态阈值调整]
    
    D --> D1[音频分段批处理]
    D --> D2[结果批量更新]
```

## 错误处理机制

### 错误处理流程

```mermaid
flowchart TD
    A[异常发生] --> B{异常类型}
    B -->|初始化异常| C[显示错误信息]
    B -->|录音异常| D[重新初始化麦克风]
    B -->|识别异常| E[重置识别器状态]
    B -->|网络异常| F[降级到本地处理]
    B -->|存储异常| G[使用内存缓存]
    
    C --> H[用户重试或退出]
    D --> I[恢复录音功能]
    E --> J[继续识别流程]
    F --> K[保持基本功能]
    G --> L[数据临时保存]
    
    H --> M[记录错误日志]
    I --> M
    J --> M
    K --> M
    L --> M
```

## 扩展性设计

### 1. 插件化架构

```mermaid
graph TD
    A[核心引擎] --> B[识别插件接口]
    A --> C[声纹插件接口]
    A --> D[优化插件接口]
    
    B --> B1[Sherpa-ONNX插件]
    B --> B2[其他ASR插件]
    
    C --> C1[本地声纹插件]
    C --> C2[云端声纹插件]
    
    D --> D1[Gemini插件]
    D --> D2[其他AI插件]
```

### 2. 配置管理

```mermaid
graph LR
    A[配置中心] --> B[模型配置]
    A --> C[算法参数]
    A --> D[UI配置]
    A --> E[网络配置]
    
    B --> B1[模型路径]
    B --> B2[模型参数]
    
    C --> C1[识别阈值]
    C --> C2[端点参数]
    
    D --> D1[界面主题]
    D --> D2[显示选项]
    
    E --> E1[API端点]
    E --> E2[超时设置]
```

## 安全性设计

### 1. 数据安全

```mermaid
flowchart TD
    A[数据安全] --> B[API密钥加密]
    A --> C[声纹数据保护]
    A --> D[网络传输安全]
    
    B --> B1[AES加密存储]
    B --> B2[密钥轮换机制]
    
    C --> C1[本地文件加密]
    C --> C2[访问权限控制]
    
    D --> D1[HTTPS传输]
    D --> D2[请求签名验证]
```

### 2. 权限管理

```mermaid
stateDiagram-v2
    [*] --> RequestPermission: 应用启动
    RequestPermission --> PermissionGranted: 用户授权
    RequestPermission --> PermissionDenied: 用户拒绝
    PermissionGranted --> FullFunctionality: 完整功能
    PermissionDenied --> LimitedFunctionality: 受限功能
    LimitedFunctionality --> RequestPermission: 重新请求
```

## 测试策略

### 测试金字塔

```mermaid
graph TD
    A[测试策略] --> B[单元测试]
    A --> C[集成测试]
    A --> D[UI测试]
    A --> E[性能测试]
    
    B --> B1[SpeakerDataManagerTest]
    B --> B2[ApiKeyManagerTest]
    B --> B3[工具类测试]
    
    C --> C1[ASR引擎集成测试]
    C --> C2[声纹识别集成测试]
    
    D --> D1[录音功能测试]
    D --> D2[界面交互测试]
    
    E --> E1[内存使用测试]
    E --> E2[CPU使用测试]
    E --> E3[识别延迟测试]
```

这个架构设计确保了系统的可维护性、可扩展性和高性能，同时提供了良好的用户体验和数据安全保障。