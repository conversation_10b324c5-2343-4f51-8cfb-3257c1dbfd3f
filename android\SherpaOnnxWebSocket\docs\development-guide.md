# SherpaOnnx WebSocket Android 开发指南

## 开发环境搭建

### 系统要求

- **操作系统**: Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **Android Studio**: 4.2 或更高版本
- **JDK**: Java 8 或更高版本
- **Android SDK**: API Level 21 (Android 5.0) 或更高
- **Gradle**: 7.3.1 或更高版本

### 环境配置

```bash
# 1. 克隆项目
git clone <repository-url>
cd sherpa-onnx/android/SherpaOnnxWebSocket

# 2. 检查 Android SDK
android list targets

# 3. 同步项目依赖
./gradlew build
```

### IDE 配置

```kotlin
// Android Studio 配置建议
// File -> Settings -> Editor -> Code Style -> Kotlin
// 设置缩进为 4 空格
// 启用自动导入优化
```

## 项目结构详解

### 目录结构

```
SherpaOnnxWebSocket/
├── app/
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/com/k2fsa/sherpa/onnx/
│   │   │   │   ├── MainActivity.kt          # 主活动
│   │   │   │   ├── MyWebsocketClient.kt     # WebSocket客户端
│   │   │   │   ├── SpeechContent.kt         # 数据模型
│   │   │   │   └── WaveReader.kt            # 音频读取工具
│   │   │   ├── res/
│   │   │   │   ├── layout/
│   │   │   │   │   └── activity_main.xml    # 主界面布局
│   │   │   │   ├── values/
│   │   │   │   │   ├── strings.xml          # 字符串资源
│   │   │   │   │   ├── colors.xml           # 颜色资源
│   │   │   │   │   └── themes.xml           # 主题样式
│   │   │   │   └── ...
│   │   │   ├── assets/                      # 资源文件
│   │   │   ├── jniLibs/                     # Native库
│   │   │   └── AndroidManifest.xml         # 应用清单
│   │   ├── androidTest/                     # Android测试
│   │   └── test/                            # 单元测试
│   ├── build.gradle                         # 应用级构建配置
│   └── proguard-rules.pro                   # 混淆规则
├── docs/                                    # 项目文档
├── build.gradle                             # 项目级构建配置
├── settings.gradle                          # 项目设置
└── gradle.properties                        # Gradle属性
```

### 核心模块说明

#### 1. MainActivity.kt

```kotlin
/**
 * 主活动类 - 应用的入口点
 * 职责:
 * - 管理UI交互
 * - 协调音频录制和WebSocket通信
 * - 处理权限请求
 * - 显示识别结果
 */
class MainActivity : AppCompatActivity(), MyWebsocketClient.WebsocketClientCallback {
    // 核心组件
    private var audioRecord: AudioRecord? = null
    private var websocketClient: MyWebsocketClient? = null
    
    // UI组件
    private lateinit var recordButton: Button
    private lateinit var connectButton: Button
    private lateinit var textView: TextView
    private lateinit var etUrl: EditText
    
    // 状态管理
    @Volatile private var isRecording: Boolean = false
    @Volatile private var isConnected: Boolean = false
}
```

#### 2. MyWebsocketClient.kt

```kotlin
/**
 * WebSocket客户端封装
 * 职责:
 * - 管理WebSocket连接
 * - 处理连接事件
 * - 发送音频数据
 * - 接收识别结果
 */
class MyWebsocketClient(serverUri: URI?) : WebSocketClient(serverUri) {
    private var clientCallback: WebsocketClientCallback? = null
    
    interface WebsocketClientCallback {
        fun onOpen(handshakedata: ServerHandshake?)
        fun onMessage(message: String?)
        fun onClose(code: Int, reason: String?, remote: Boolean?)
        fun onError(ex: Exception?)
    }
}
```

#### 3. SpeechContent.kt

```kotlin
/**
 * 语音识别结果数据模型
 * 用于JSON序列化/反序列化
 */
data class SpeechContent(
    val text: String,    // 识别的文本内容
    val segment: Long    // 语音片段标识
)
```

## 开发流程

### 1. 功能开发流程

```mermaid
flowchart TD
    A["需求分析"] --> B["设计方案"]
    B --> C["编写代码"]
    C --> D["单元测试"]
    D --> E["集成测试"]
    E --> F["代码审查"]
    F --> G["部署测试"]
    G --> H{"测试通过?"}
    H -->|是| I["合并代码"]
    H -->|否| C
```

### 2. 代码开发规范

#### Kotlin 编码规范

```kotlin
// 1. 类命名：使用 PascalCase
class AudioRecordManager

// 2. 函数命名：使用 camelCase
fun startRecording()
fun processAudioData()

// 3. 常量命名：使用 UPPER_SNAKE_CASE
companion object {
    private const val TAG = "MainActivity"
    private const val SAMPLE_RATE = 16000
    private const val REQUEST_RECORD_AUDIO_PERMISSION = 200
}

// 4. 变量命名：使用 camelCase
private var isRecording: Boolean = false
private lateinit var audioRecord: AudioRecord

// 5. 注释规范
/**
 * 处理音频数据的核心方法
 * @param buffer 音频数据缓冲区
 * @return 处理是否成功
 */
private fun processSamples(buffer: ShortArray): Boolean {
    // 实现逻辑
}
```

#### 资源命名规范

```xml
<!-- 字符串资源：功能_类型_描述 -->
<string name="button_record_start">开始录音</string>
<string name="button_record_stop">停止录音</string>
<string name="error_permission_denied">权限被拒绝</string>

<!-- 颜色资源：用途_状态 -->
<color name="button_primary">#2196F3</color>
<color name="button_primary_pressed">#1976D2</color>
<color name="text_error">#F44336</color>

<!-- 布局ID：类型_功能_描述 -->
<Button android:id="@+id/btn_connect_websocket" />
<TextView android:id="@+id/tv_recognition_result" />
<EditText android:id="@+id/et_server_url" />
```

### 3. Git 工作流

```mermaid
gitgraph
    commit id: "Initial"
    branch feature/audio-enhancement
    checkout feature/audio-enhancement
    commit id: "Add noise reduction"
    commit id: "Optimize buffer size"
    checkout main
    merge feature/audio-enhancement
    commit id: "Release v1.1"
    branch hotfix/connection-issue
    checkout hotfix/connection-issue
    commit id: "Fix reconnection bug"
    checkout main
    merge hotfix/connection-issue
    commit id: "Release v1.1.1"
```

#### 分支命名规范

- **feature/**: 新功能开发
- **bugfix/**: Bug修复
- **hotfix/**: 紧急修复
- **refactor/**: 代码重构
- **docs/**: 文档更新

#### 提交信息规范

```bash
# 格式：<type>(<scope>): <description>

# 示例
feat(audio): add noise reduction feature
fix(websocket): resolve connection timeout issue
docs(api): update WebSocket protocol documentation
refactor(ui): simplify button click handlers
test(audio): add unit tests for audio processing
```

## 调试和测试

### 1. 日志调试

```kotlin
// 日志级别使用
class MainActivity {
    companion object {
        private const val TAG = "MainActivity"
    }
    
    private fun debugAudioData(data: FloatArray) {
        Log.d(TAG, "音频数据大小: ${data.size}")
        Log.v(TAG, "音频数据详情: ${data.contentToString()}")
    }
    
    private fun logWebSocketEvent(event: String, details: String) {
        Log.i(TAG, "WebSocket事件: $event, 详情: $details")
    }
    
    private fun handleError(error: Exception) {
        Log.e(TAG, "发生错误", error)
        // 错误处理逻辑
    }
}
```

### 2. 单元测试

```kotlin
// app/src/test/java/com/k2fsa/sherpa/onnx/
class SpeechContentTest {
    
    @Test
    fun testSpeechContentSerialization() {
        val gson = Gson()
        val speechContent = SpeechContent("测试文本", 12345L)
        
        val json = gson.toJson(speechContent)
        val deserializedContent = gson.fromJson(json, SpeechContent::class.java)
        
        assertEquals(speechContent.text, deserializedContent.text)
        assertEquals(speechContent.segment, deserializedContent.segment)
    }
    
    @Test
    fun testEmptyTextHandling() {
        val speechContent = SpeechContent("", 0L)
        assertNotNull(speechContent)
        assertTrue(speechContent.text.isEmpty())
    }
}
```

### 3. 集成测试

```kotlin
// app/src/androidTest/java/com/k2fsa/sherpa/onnx/
@RunWith(AndroidJUnit4::class)
class MainActivityTest {
    
    @get:Rule
    val activityRule = ActivityTestRule(MainActivity::class.java)
    
    @Test
    fun testConnectButtonClick() {
        // 输入服务器地址
        onView(withId(R.id.et_uri))
            .perform(typeText("ws://test.server:6006"))
        
        // 点击连接按钮
        onView(withId(R.id.connect_button))
            .perform(click())
        
        // 验证按钮文本变化
        onView(withId(R.id.connect_button))
            .check(matches(withText("disconnect")))
    }
    
    @Test
    fun testPermissionRequest() {
        // 测试权限请求流程
        val context = InstrumentationRegistry.getInstrumentation().targetContext
        val permission = ContextCompat.checkSelfPermission(
            context, 
            Manifest.permission.RECORD_AUDIO
        )
        
        // 验证权限状态
        assertTrue(permission == PackageManager.PERMISSION_GRANTED)
    }
}
```

### 4. 性能测试

```kotlin
class PerformanceTest {
    
    @Test
    fun testAudioProcessingPerformance() {
        val bufferSize = 1600 // 100ms at 16kHz
        val buffer = ShortArray(bufferSize) { (it * 1000).toShort() }
        
        val startTime = System.nanoTime()
        
        // 执行音频处理
        val samples = FloatArray(bufferSize) { buffer[it] / 32768.0f }
        val byteBuffer = ByteBuffer.allocate(4 * samples.size)
            .order(ByteOrder.LITTLE_ENDIAN)
        
        for (f in samples) {
            byteBuffer.putFloat(f)
        }
        
        val endTime = System.nanoTime()
        val duration = (endTime - startTime) / 1_000_000 // 转换为毫秒
        
        // 验证处理时间小于10ms
        assertTrue("音频处理时间过长: ${duration}ms", duration < 10)
    }
}
```

## 扩展开发

### 1. 添加新功能

#### 示例：添加音频增强功能

```kotlin
// 1. 创建音频增强接口
interface AudioEnhancer {
    fun enhance(audioData: FloatArray): FloatArray
}

// 2. 实现噪声抑制
class NoiseSuppressionEnhancer : AudioEnhancer {
    override fun enhance(audioData: FloatArray): FloatArray {
        // 实现噪声抑制算法
        return audioData.map { sample ->
            if (abs(sample) < NOISE_THRESHOLD) 0.0f else sample
        }.toFloatArray()
    }
    
    companion object {
        private const val NOISE_THRESHOLD = 0.01f
    }
}

// 3. 集成到主流程
class MainActivity {
    private val audioEnhancer: AudioEnhancer = NoiseSuppressionEnhancer()
    
    private fun processSamples() {
        // ... 原有代码 ...
        
        // 添加音频增强
        val enhancedSamples = audioEnhancer.enhance(samples)
        
        // ... 后续处理 ...
    }
}
```

### 2. 自定义配置

```kotlin
// 配置管理类
class AppConfig {
    companion object {
        // 音频配置
        const val SAMPLE_RATE = 16000
        const val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
        const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
        const val BUFFER_INTERVAL_MS = 100
        
        // 网络配置
        const val DEFAULT_SERVER_URL = "ws://172.28.13.167:6006"
        const val CONNECTION_TIMEOUT_MS = 5000
        const val RECONNECT_MAX_ATTEMPTS = 5
        
        // UI配置
        const val RESULT_MAX_LINES = 100
        const val AUTO_SCROLL_ENABLED = true
    }
}

// 使用配置
class MainActivity {
    private fun initAudioRecord() {
        audioRecord = AudioRecord(
            MediaRecorder.AudioSource.MIC,
            AppConfig.SAMPLE_RATE,
            AppConfig.CHANNEL_CONFIG,
            AppConfig.AUDIO_FORMAT,
            bufferSize
        )
    }
}
```

### 3. 插件化架构

```kotlin
// 插件接口定义
interface AudioPlugin {
    fun getName(): String
    fun process(audioData: FloatArray): FloatArray
    fun isEnabled(): Boolean
}

// 插件管理器
class AudioPluginManager {
    private val plugins = mutableListOf<AudioPlugin>()
    
    fun registerPlugin(plugin: AudioPlugin) {
        plugins.add(plugin)
    }
    
    fun processAudio(audioData: FloatArray): FloatArray {
        var processedData = audioData
        
        plugins.filter { it.isEnabled() }.forEach { plugin ->
            processedData = plugin.process(processedData)
        }
        
        return processedData
    }
}

// 示例插件：音量调节
class VolumeControlPlugin(private val gain: Float) : AudioPlugin {
    override fun getName() = "Volume Control"
    
    override fun process(audioData: FloatArray): FloatArray {
        return audioData.map { it * gain }.toFloatArray()
    }
    
    override fun isEnabled() = gain != 1.0f
}
```

## 部署和发布

### 1. 构建配置

```gradle
// app/build.gradle
android {
    compileSdk 34
    
    defaultConfig {
        applicationId "com.k2fsa.sherpa.onnx"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0.0"
    }
    
    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            applicationIdSuffix ".debug"
        }
        
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // 签名配置
            signingConfig signingConfigs.release
        }
    }
    
    // 多渠道配置
    flavorDimensions "version"
    productFlavors {
        free {
            dimension "version"
            applicationIdSuffix ".free"
            versionNameSuffix "-free"
        }
        
        pro {
            dimension "version"
            applicationIdSuffix ".pro"
            versionNameSuffix "-pro"
        }
    }
}
```

### 2. 混淆配置

```proguard
# proguard-rules.pro

# 保留WebSocket相关类
-keep class org.java_websocket.** { *; }
-keep class com.k2fsa.sherpa.onnx.MyWebsocketClient { *; }

# 保留数据模型类
-keep class com.k2fsa.sherpa.onnx.SpeechContent { *; }

# 保留Gson相关
-keepattributes Signature
-keepattributes *Annotation*
-keep class com.google.gson.** { *; }

# 保留Native方法
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保留音频相关类
-keep class android.media.AudioRecord { *; }
-keep class android.media.AudioFormat { *; }
```

### 3. 版本管理

```kotlin
// 版本信息管理
object VersionInfo {
    const val VERSION_NAME = "1.0.0"
    const val VERSION_CODE = 1
    const val BUILD_TIME = "2024-01-01 12:00:00"
    
    fun getVersionInfo(): String {
        return "Version: $VERSION_NAME ($VERSION_CODE)\nBuild: $BUILD_TIME"
    }
}

// 在应用中显示版本信息
class AboutActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        findViewById<TextView>(R.id.tv_version).text = VersionInfo.getVersionInfo()
    }
}
```

### 4. 自动化构建

```yaml
# .github/workflows/android.yml
name: Android CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 11
      uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'temurin'
    
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
    
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
    
    - name: Run tests
      run: ./gradlew test
    
    - name: Build APK
      run: ./gradlew assembleDebug
    
    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: app-debug
        path: app/build/outputs/apk/debug/app-debug.apk
```

## 常见问题解决

### 1. 编译问题

```bash
# 清理项目
./gradlew clean

# 重新构建
./gradlew build

# 检查依赖冲突
./gradlew app:dependencies

# 更新Gradle Wrapper
./gradlew wrapper --gradle-version 7.5
```

### 2. 运行时问题

```kotlin
// 权限检查
private fun checkPermissions(): Boolean {
    val audioPermission = ContextCompat.checkSelfPermission(
        this, Manifest.permission.RECORD_AUDIO
    )
    val internetPermission = ContextCompat.checkSelfPermission(
        this, Manifest.permission.INTERNET
    )
    
    return audioPermission == PackageManager.PERMISSION_GRANTED &&
           internetPermission == PackageManager.PERMISSION_GRANTED
}

// 内存泄漏检查
override fun onDestroy() {
    super.onDestroy()
    
    // 释放AudioRecord
    audioRecord?.let {
        if (it.state == AudioRecord.STATE_INITIALIZED) {
            it.stop()
            it.release()
        }
    }
    
    // 关闭WebSocket连接
    websocketClient?.close()
    websocketClient = null
}
```

### 3. 性能优化

```kotlin
// 音频缓冲区优化
class AudioBufferManager {
    private val bufferPool = ArrayDeque<ShortArray>()
    private val maxPoolSize = 5
    
    fun getBuffer(size: Int): ShortArray {
        return bufferPool.pollFirst() ?: ShortArray(size)
    }
    
    fun returnBuffer(buffer: ShortArray) {
        if (bufferPool.size < maxPoolSize) {
            bufferPool.offerLast(buffer)
        }
    }
}

// UI更新优化
class UIUpdateThrottler {
    private var lastUpdateTime = 0L
    private val updateInterval = 100L // 100ms
    
    fun shouldUpdate(): Boolean {
        val currentTime = System.currentTimeMillis()
        return if (currentTime - lastUpdateTime > updateInterval) {
            lastUpdateTime = currentTime
            true
        } else {
            false
        }
    }
}
```

## 最佳实践

### 1. 代码质量

- **使用静态分析工具**: ktlint, detekt
- **编写单元测试**: 覆盖率 > 80%
- **代码审查**: 所有PR必须经过审查
- **文档更新**: 代码变更同步更新文档

### 2. 性能优化

- **避免内存泄漏**: 及时释放资源
- **优化UI响应**: 避免主线程阻塞
- **网络优化**: 合理的重试机制
- **电池优化**: 合理使用后台任务

### 3. 用户体验

- **错误处理**: 友好的错误提示
- **加载状态**: 清晰的加载指示
- **离线支持**: 网络异常时的降级方案
- **无障碍支持**: 支持辅助功能

### 4. 安全考虑

- **权限最小化**: 只请求必要权限
- **数据加密**: 敏感数据加密传输
- **输入验证**: 验证用户输入
- **安全存储**: 避免明文存储敏感信息

这份开发指南为 SherpaOnnx WebSocket Android 项目提供了全面的开发指导，涵盖了从环境搭建到部署发布的完整流程。开发者可以根据这份指南快速上手项目开发，并遵循最佳实践确保代码质量和用户体验。