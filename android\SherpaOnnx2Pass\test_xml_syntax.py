#!/usr/bin/env python3
"""
简单的XML语法检查脚本，用于验证drawable文件的语法正确性
"""

import xml.etree.ElementTree as ET
import os
import sys

def check_xml_file(file_path):
    """检查单个XML文件的语法"""
    try:
        ET.parse(file_path)
        print(f"✅ {file_path} - 语法正确")
        return True
    except ET.ParseError as e:
        print(f"❌ {file_path} - 语法错误: {e}")
        return False
    except Exception as e:
        print(f"⚠️ {file_path} - 检查失败: {e}")
        return False

def main():
    """主函数"""
    drawable_dir = "app/src/main/res/drawable"
    
    if not os.path.exists(drawable_dir):
        print(f"错误: 目录 {drawable_dir} 不存在")
        sys.exit(1)
    
    print("检查drawable目录中的XML文件...")
    print("=" * 50)
    
    xml_files = [f for f in os.listdir(drawable_dir) if f.endswith('.xml')]
    
    if not xml_files:
        print("没有找到XML文件")
        return
    
    success_count = 0
    total_count = len(xml_files)
    
    for xml_file in sorted(xml_files):
        file_path = os.path.join(drawable_dir, xml_file)
        if check_xml_file(file_path):
            success_count += 1
    
    print("=" * 50)
    print(f"检查完成: {success_count}/{total_count} 文件语法正确")
    
    if success_count == total_count:
        print("🎉 所有XML文件语法都正确！")
        sys.exit(0)
    else:
        print("⚠️ 存在语法错误的文件")
        sys.exit(1)

if __name__ == "__main__":
    main()
