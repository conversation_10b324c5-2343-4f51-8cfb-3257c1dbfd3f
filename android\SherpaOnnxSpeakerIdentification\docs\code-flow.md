# 代码执行流程分析

## 应用启动流程

### 1. 应用初始化流程

```mermaid
flowchart TD
    A[应用启动] --> B[MainActivity.onCreate]
    B --> C[请求录音权限]
    C --> D[初始化 SpeakerRecognition]
    D --> E[加载 ONNX 模型]
    E --> F[创建 SpeakerEmbeddingExtractor]
    F --> G[创建 SpeakerEmbeddingManager]
    G --> H[设置 Compose UI]
    H --> I[显示主界面]
    
    C --> J{权限检查}
    J -->|拒绝| K[显示权限提示]
    K --> L[退出应用]
    J -->|允许| D
```

### 2. 权限处理流程

```mermaid
flowchart TD
    A[权限请求] --> B[onRequestPermissionsResult]
    B --> C{录音权限是否授予?}
    C -->|是| D[记录权限允许日志]
    C -->|否| E[记录权限拒绝日志]
    E --> F[显示Toast提示]
    F --> G[结束应用]
    D --> H[继续应用流程]
```

## 主界面导航流程

### 3. 导航系统流程

```mermaid
flowchart TD
    A[MainScreen] --> B[Scaffold布局]
    B --> C[TopAppBar]
    B --> D[NavigationHost]
    B --> E[BottomNavigationBar]
    
    D --> F[NavHost路由]
    F --> G[Home路由]
    F --> H[Register路由]
    F --> I[View路由]
    F --> J[Help路由]
    
    G --> K[HomeScreen]
    H --> L[RegisterScreen]
    I --> M[ViewScreen]
    J --> N[HelpScreen]
    
    E --> O[底部导航点击]
    O --> P[navController.navigate]
    P --> F
```

## 说话人识别流程 (Home Screen)

### 4. 实时识别流程

```mermaid
flowchart TD
    A[HomeScreen] --> B[点击录音按钮]
    B --> C{检查录音权限}
    C -->|无权限| D[显示权限错误]
    C -->|有权限| E[创建AudioRecord]
    
    E --> F[启动录音线程]
    F --> G[开始录音]
    G --> H[读取音频缓冲区]
    H --> I[转换为FloatArray]
    I --> J[添加到样本列表]
    J --> K{样本数量足够?}
    K -->|否| H
    K -->|是| L[创建OnlineStream]
    
    L --> M[acceptWaveform]
    M --> N[inputFinished]
    N --> O{extractor.isReady?}
    O -->|否| P[等待更多数据]
    O -->|是| Q[extractor.compute]
    
    Q --> R[获取特征向量]
    R --> S[manager.search]
    S --> T{识别结果 > 阈值?}
    T -->|是| U[显示识别的说话人]
    T -->|否| V[显示未知说话人]
    
    U --> W[更新UI]
    V --> W
    W --> X{继续录音?}
    X -->|是| H
    X -->|否| Y[停止录音]
```

## 说话人注册流程 (Register Screen)

### 5. 说话人注册流程

```mermaid
flowchart TD
    A[RegisterScreen] --> B[输入说话人姓名]
    B --> C[点击录音按钮]
    C --> D{检查录音权限}
    D -->|无权限| E[显示权限错误]
    D -->|有权限| F[创建AudioRecord]
    
    F --> G[启动录音线程]
    G --> H[开始录音]
    H --> I[读取音频缓冲区]
    I --> J[转换为FloatArray]
    J --> K[添加到样本列表]
    K --> L{样本数量足够?}
    L -->|否| I
    L -->|是| M[创建OnlineStream]
    
    M --> N[acceptWaveform]
    N --> O[inputFinished]
    O --> P{extractor.isReady?}
    P -->|否| Q[等待更多数据]
    P -->|是| R[extractor.compute]
    
    R --> S[获取特征向量]
    S --> T[添加到embeddingList]
    T --> U[更新录音次数显示]
    U --> V{录音次数足够?}
    V -->|否| W[提示继续录音]
    V -->|是| X[启用注册按钮]
    
    X --> Y[点击注册按钮]
    Y --> Z{说话人姓名有效?}
    Z -->|否| AA[显示错误提示]
    Z -->|是| BB[manager.add]
    BB --> CC[添加说话人到数据库]
    CC --> DD[显示注册成功]
    DD --> EE[清空表单]
```

## 说话人管理流程 (View Screen)

### 6. 说话人查看和删除流程

```mermaid
flowchart TD
    A[ViewScreen] --> B[manager.allSpeakerNames]
    B --> C[获取所有说话人列表]
    C --> D[创建SpeakerName对象列表]
    D --> E[显示LazyColumn列表]
    
    E --> F[用户选择说话人]
    F --> G[更新Checkbox状态]
    G --> H[点击删除按钮]
    H --> I{有选中的说话人?}
    I -->|否| J[按钮保持禁用]
    I -->|是| K[遍历选中项]
    
    K --> L[manager.remove]
    L --> M[从数据库删除说话人]
    M --> N[从UI列表移除]
    N --> O[更新按钮状态]
    O --> P{还有说话人?}
    P -->|否| Q[禁用删除按钮]
    P -->|是| R[保持按钮可用]
```

## 音频处理核心流程

### 7. 音频数据处理流程

```mermaid
flowchart TD
    A[AudioRecord] --> B[16kHz采样率]
    B --> C[MONO声道]
    C --> D[16位PCM格式]
    D --> E[读取ShortArray]
    E --> F[转换为FloatArray]
    F --> G[归一化 /32768.0f]
    G --> H[OnlineStream.acceptWaveform]
    H --> I[JNI调用]
    I --> J[C++音频处理]
    J --> K[ONNX模型推理]
    K --> L[返回特征向量]
    L --> M[Java层处理]
```

### 8. 特征提取和匹配流程

```mermaid
flowchart TD
    A[音频特征向量] --> B[SpeakerEmbeddingManager]
    B --> C{操作类型}
    C -->|添加| D[manager.add]
    C -->|搜索| E[manager.search]
    C -->|验证| F[manager.verify]
    C -->|删除| G[manager.remove]
    
    D --> H[存储特征向量]
    H --> I[关联说话人姓名]
    
    E --> J[计算相似度]
    J --> K[应用阈值]
    K --> L[返回最匹配说话人]
    
    F --> M[与指定说话人比较]
    M --> N[返回验证结果]
    
    G --> O[删除说话人数据]
    O --> P[更新内部状态]
```

## 错误处理流程

### 9. 异常处理流程

```mermaid
flowchart TD
    A[应用运行] --> B{异常类型}
    B -->|权限异常| C[权限处理]
    B -->|音频异常| D[音频错误处理]
    B -->|模型异常| E[模型加载错误]
    B -->|JNI异常| F[原生库错误]
    
    C --> G[显示权限提示]
    G --> H[请求重新授权]
    
    D --> I[停止录音]
    I --> J[释放AudioRecord]
    J --> K[显示错误Toast]
    
    E --> L[检查模型文件]
    L --> M[显示模型错误信息]
    
    F --> N[记录JNI错误日志]
    N --> O[尝试重新初始化]
    
    H --> P[用户响应]
    K --> P
    M --> P
    O --> P
    P --> Q{是否继续?}
    Q -->|是| A
    Q -->|否| R[退出应用]
```

## 内存管理流程

### 10. 资源管理流程

```mermaid
flowchart TD
    A[对象创建] --> B{对象类型}
    B -->|SpeakerEmbeddingExtractor| C[JNI对象]
    B -->|OnlineStream| D[流对象]
    B -->|AudioRecord| E[音频对象]
    
    C --> F[native指针管理]
    F --> G[finalize方法]
    G --> H[delete native对象]
    
    D --> I[流指针管理]
    I --> J[release方法]
    J --> K[清理流资源]
    
    E --> L[音频资源]
    L --> M[stop + release]
    M --> N[释放音频缓冲区]
    
    H --> O[内存回收]
    K --> O
    N --> O
    O --> P[GC清理]
```

## 总结

该应用的核心执行流程围绕以下几个关键点：

1. **初始化阶段**: 权限获取、模型加载、UI构建
2. **音频处理**: 实时录音、格式转换、特征提取
3. **说话人管理**: 注册、识别、删除操作
4. **用户交互**: Compose UI响应、导航管理
5. **资源管理**: 内存释放、异常处理

整个应用采用事件驱动的架构，通过 Compose 的状态管理实现 UI 更新，通过 JNI 调用实现高性能的音频处理和机器学习推理。