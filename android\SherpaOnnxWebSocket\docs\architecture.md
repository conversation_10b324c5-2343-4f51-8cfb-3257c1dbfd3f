# SherpaOnnx WebSocket Android 架构设计文档

## 系统架构概览

### 整体架构图

```mermaid
graph TB
    subgraph "Android Client"
        UI["用户界面层"]
        BL["业务逻辑层"]
        DL["数据处理层"]
        NL["网络通信层"]
        AL["音频处理层"]
    end
    
    subgraph "External Services"
        WS["WebSocket Server"]
        ASR["语音识别服务"]
    end
    
    UI --> BL
    BL --> DL
    BL --> NL
    BL --> AL
    NL --> WS
    WS --> ASR
    ASR --> WS
    WS --> NL
```

## 分层架构设计

### 1. 用户界面层 (UI Layer)

**职责**: 处理用户交互和界面展示

```mermaid
classDiagram
    class MainActivity {
        -recordButton: Button
        -connectButton: Button
        -textView: TextView
        -etUrl: EditText
        +onCreate()
        +updateUI()
        +showResult()
    }
    
    class ActivityMainXML {
        +connect_button
        +record_button
        +my_text
        +et_uri
    }
    
    MainActivity --> ActivityMainXML
```

**组件说明**:
- `MainActivity`: 主活动，管理所有UI交互
- `activity_main.xml`: 布局文件，定义界面结构
- 负责状态更新和用户反馈

### 2. 业务逻辑层 (Business Logic Layer)

**职责**: 协调各个模块，实现核心业务逻辑

```mermaid
stateDiagram-v2
    [*] --> Disconnected
    Disconnected --> Connecting: 用户点击连接
    Connecting --> Connected: 连接成功
    Connecting --> Disconnected: 连接失败
    Connected --> Recording: 开始录音
    Recording --> Connected: 停止录音
    Connected --> Disconnected: 断开连接
    Recording --> Disconnected: 连接断开
```

**状态管理**:
- `isConnected`: WebSocket 连接状态
- `isRecording`: 音频录制状态
- 状态变化触发相应的UI更新

### 3. 网络通信层 (Network Layer)

**职责**: 管理 WebSocket 连接和数据传输

```mermaid
sequenceDiagram
    participant Client as MyWebsocketClient
    participant Server as WebSocket Server
    participant Callback as MainActivity
    
    Client->>Server: connect()
    Server->>Client: onOpen()
    Client->>Callback: onOpen()
    
    loop 音频数据传输
        Client->>Server: send(audioData)
        Server->>Client: onMessage(result)
        Client->>Callback: onMessage(result)
    end
    
    Client->>Server: close()
    Server->>Client: onClose()
    Client->>Callback: onClose()
```

**核心组件**:
- `MyWebsocketClient`: WebSocket 客户端封装
- `WebsocketClientCallback`: 回调接口定义
- 支持二进制数据传输和文本消息接收

### 4. 音频处理层 (Audio Layer)

**职责**: 音频录制、格式转换和数据处理

```mermaid
flowchart LR
    A["麦克风"] --> B["AudioRecord"]
    B --> C["Short[] PCM"]
    C --> D["Float[] 归一化"]
    D --> E["ByteBuffer"]
    E --> F["WebSocket 发送"]
    
    subgraph "音频处理管道"
        C --> D
        D --> E
    end
```

**处理流程**:
1. **音频采集**: 使用 `AudioRecord` 从麦克风获取 PCM 数据
2. **格式转换**: Short[] → Float[] → ByteBuffer
3. **数据归一化**: 除以 32768.0f 进行归一化
4. **字节序处理**: 设置为 LITTLE_ENDIAN

### 5. 数据处理层 (Data Layer)

**职责**: 数据序列化、反序列化和存储

```mermaid
classDiagram
    class SpeechContent {
        +text: String
        +segment: Long
    }
    
    class DataProcessor {
        -gson: Gson
        -recognitionText: HashMap~Long, String~
        +parseResult(json: String)
        +getDisplayResult(): String
        +updateResult(segment: Long, text: String)
    }
    
    DataProcessor --> SpeechContent
```

**数据流**:
- **输入**: JSON 格式的识别结果
- **解析**: 使用 Gson 转换为 `SpeechContent` 对象
- **存储**: 按 segment 存储在 HashMap 中
- **输出**: 格式化的显示文本

## 设计模式应用

### 1. 观察者模式 (Observer Pattern)

```mermaid
classDiagram
    class WebsocketClientCallback {
        <<interface>>
        +onOpen()
        +onMessage()
        +onClose()
        +onError()
    }
    
    class MyWebsocketClient {
        -clientCallback: WebsocketClientCallback
        +setClientCallback()
        +notifyCallbacks()
    }
    
    class MainActivity {
        +onOpen()
        +onMessage()
        +onClose()
        +onError()
    }
    
    WebsocketClientCallback <|.. MainActivity
    MyWebsocketClient --> WebsocketClientCallback
```

**应用场景**: WebSocket 事件通知
- `MyWebsocketClient` 作为被观察者
- `MainActivity` 作为观察者
- 通过回调接口实现松耦合

### 2. 状态模式 (State Pattern)

```mermaid
stateDiagram-v2
    state "应用状态" as AppState {
        [*] --> Idle
        Idle --> Connecting: connect()
        Connecting --> Connected: onOpen()
        Connecting --> Idle: onError()
        Connected --> Recording: startRecord()
        Recording --> Connected: stopRecord()
        Connected --> Idle: disconnect()
        Recording --> Idle: onClose()
    }
```

**状态管理**:
- 使用布尔标志管理状态
- 状态变化触发UI更新
- 防止无效操作

### 3. 策略模式 (Strategy Pattern)

```mermaid
classDiagram
    class AudioProcessor {
        <<interface>>
        +processAudio(data: ShortArray)
    }
    
    class RealtimeProcessor {
        +processAudio(data: ShortArray)
    }
    
    class BatchProcessor {
        +processAudio(data: ShortArray)
    }
    
    AudioProcessor <|.. RealtimeProcessor
    AudioProcessor <|.. BatchProcessor
```

**应用场景**: 音频处理策略
- 实时处理: 立即发送音频数据
- 批量处理: 缓存后批量发送

## 并发处理设计

### 线程模型

```mermaid
gantt
    title 线程执行时序
    dateFormat X
    axisFormat %s
    
    section 主线程
    UI更新           :active, ui, 0, 10
    用户交互处理      :active, interaction, 0, 10
    
    section 录音线程
    音频数据采集      :active, record, 1, 8
    数据格式转换      :active, convert, 1, 8
    
    section WebSocket线程
    数据发送         :active, send, 2, 7
    消息接收         :active, receive, 2, 7
```

**线程职责**:
- **主线程**: UI更新和用户交互
- **录音线程**: 音频数据采集和处理
- **WebSocket线程**: 网络通信

### 线程同步

```mermaid
sequenceDiagram
    participant Main as 主线程
    participant Record as 录音线程
    participant WS as WebSocket线程
    
    Main->>Record: 启动录音
    Record->>Record: 循环采集音频
    Record->>WS: 发送音频数据
    WS->>Main: 回调结果 (runOnUiThread)
    Main->>Main: 更新UI
    Main->>Record: 停止录音
```

**同步机制**:
- 使用 `@Volatile` 标记共享变量
- `runOnUiThread()` 确保UI更新在主线程
- 线程安全的数据传递

## 错误处理机制

### 异常处理层次

```mermaid
flowchart TD
    A["用户操作"] --> B{"权限检查"}
    B -->|失败| C["权限错误处理"]
    B -->|成功| D{"网络连接"}
    D -->|失败| E["网络错误处理"]
    D -->|成功| F{"音频初始化"}
    F -->|失败| G["音频错误处理"]
    F -->|成功| H["正常执行"]
    
    C --> I["显示错误信息"]
    E --> I
    G --> I
    I --> J["恢复到安全状态"]
```

**错误类型**:
1. **权限错误**: 音频录制权限被拒绝
2. **网络错误**: WebSocket 连接失败
3. **音频错误**: AudioRecord 初始化失败
4. **数据错误**: JSON 解析失败

### 容错机制

```mermaid
flowchart LR
    A["错误发生"] --> B["错误捕获"]
    B --> C["日志记录"]
    C --> D["用户通知"]
    D --> E["状态恢复"]
    E --> F["继续执行"]
```

**容错策略**:
- **优雅降级**: 连接失败时禁用录音功能
- **自动重试**: 网络异常时尝试重连
- **状态恢复**: 错误后恢复到安全状态
- **用户反馈**: 清晰的错误信息提示

## 性能优化设计

### 内存管理

```mermaid
flowchart TD
    A["音频缓冲区"] --> B["固定大小分配"]
    B --> C["循环复用"]
    C --> D["及时释放"]
    
    E["WebSocket连接"] --> F["连接池管理"]
    F --> G["自动清理"]
    
    H["识别结果"] --> I["HashMap存储"]
    I --> J["定期清理"]
```

**优化策略**:
- **缓冲区复用**: 避免频繁内存分配
- **及时释放**: AudioRecord 使用后立即释放
- **数据清理**: 定期清理历史识别结果

### 网络优化

```mermaid
flowchart LR
    A["音频数据"] --> B["压缩处理"]
    B --> C["批量发送"]
    C --> D["连接复用"]
    D --> E["错误重试"]
```

**优化措施**:
- **数据压缩**: 音频数据格式优化
- **批量传输**: 减少网络请求次数
- **连接复用**: 保持长连接
- **智能重试**: 指数退避重试策略

## 扩展性设计

### 模块化架构

```mermaid
graph TD
    subgraph "核心模块"
        A["音频处理模块"]
        B["网络通信模块"]
        C["数据处理模块"]
    end
    
    subgraph "扩展模块"
        D["语音增强模块"]
        E["多语言支持模块"]
        F["离线识别模块"]
    end
    
    A -.-> D
    B -.-> E
    C -.-> F
```

**扩展点**:
- **音频预处理**: 噪声抑制、回声消除
- **多协议支持**: HTTP、gRPC 等
- **多格式支持**: 不同音频编码格式
- **离线能力**: 本地识别模型

### 配置化设计

```mermaid
classDiagram
    class AudioConfig {
        +sampleRate: Int
        +channelConfig: Int
        +audioFormat: Int
        +bufferSize: Int
    }
    
    class NetworkConfig {
        +serverUrl: String
        +timeout: Int
        +retryCount: Int
        +protocol: String
    }
    
    class AppConfig {
        +audioConfig: AudioConfig
        +networkConfig: NetworkConfig
        +loadFromFile()
        +saveToFile()
    }
    
    AppConfig --> AudioConfig
    AppConfig --> NetworkConfig
```

**配置项**:
- **音频参数**: 采样率、声道、格式等
- **网络参数**: 服务器地址、超时时间等
- **UI参数**: 主题、语言等
- **功能开关**: 实验性功能控制

## 安全性设计

### 数据安全

```mermaid
flowchart TD
    A["音频数据"] --> B["本地加密"]
    B --> C["安全传输"]
    C --> D["服务器处理"]
    D --> E["结果返回"]
    E --> F["本地解密"]
    F --> G["显示结果"]
```

**安全措施**:
- **传输加密**: 支持 WSS (WebSocket Secure)
- **数据脱敏**: 不存储敏感音频数据
- **权限控制**: 最小权限原则
- **输入验证**: 服务器地址格式验证

### 隐私保护

```mermaid
flowchart LR
    A["用户同意"] --> B["数据采集"]
    B --> C["实时处理"]
    C --> D["立即删除"]
    D --> E["结果展示"]
```

**隐私策略**:
- **用户授权**: 明确的权限请求
- **数据最小化**: 只采集必要数据
- **实时处理**: 不存储音频文件
- **透明度**: 清晰的隐私说明

## 总结

本架构设计文档详细描述了 SherpaOnnx WebSocket Android 应用的系统架构、设计模式、并发处理、错误处理、性能优化、扩展性和安全性等方面的设计考虑。该架构具有以下特点：

1. **模块化**: 清晰的分层架构，职责分离
2. **可扩展**: 支持功能扩展和配置化
3. **高性能**: 优化的内存和网络使用
4. **容错性**: 完善的错误处理机制
5. **安全性**: 数据安全和隐私保护

这种设计确保了应用的稳定性、可维护性和用户体验。