# SherpaOnnx Android Demo 项目架构文档

## 项目概述

SherpaOnnx Android Demo 是一个基于 Sherpa-ONNX 语音识别引擎的 Android 应用程序，实现了实时语音识别功能。该应用使用中英文双语模型，能够实时转录用户的语音输入。

## 主要功能模块

### 1. 核心组件

#### MainActivity
- **功能**: 应用主界面，负责权限管理和UI展示
- **职责**:
  - 请求麦克风权限
  - 启动语音识别服务
  - 观察并显示识别结果
  - 管理UI生命周期

#### AppViewModel
- **功能**: 数据管理层，使用MVVM架构模式
- **职责**:
  - 管理语音识别结果的LiveData
  - 提供数据绑定接口
  - 在Activity和Service之间传递数据

#### Application
- **功能**: 全局应用程序类
- **职责**:
  - 管理全局ViewModel实例
  - 实现ViewModelStoreOwner接口
  - 提供单例访问模式

#### SpeechSherpaRecognitionService
- **功能**: 后台语音识别服务
- **职责**:
  - 管理音频录制
  - 初始化Sherpa-ONNX模型
  - 实时处理音频流
  - 执行语音识别
  - 管理前台服务通知

### 2. 技术架构

#### 语音识别引擎
- **模型**: sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20
- **组件**:
  - Encoder: encoder-epoch-99-avg-1.int8.onnx
  - Decoder: decoder-epoch-99-avg-1.onnx
  - Joiner: joiner-epoch-99-avg-1.int8.onnx
  - Tokens: tokens.txt

#### 音频处理
- **采样率**: 16kHz
- **格式**: PCM 16-bit
- **声道**: 单声道
- **缓冲区**: 0.1秒间隔处理

## 代码执行流程

### 应用启动流程

```mermaid
flowchart TD
    A[应用启动] --> B[Application.onCreate]
    B --> C[创建ViewModelStore]
    C --> D[初始化AppViewModel]
    D --> E[MainActivity.onCreate]
    E --> F[设置布局]
    F --> G[请求麦克风权限]
    G --> H{权限检查}
    H -->|已授权| I[启动语音识别服务]
    H -->|未授权| J[显示权限请求对话框]
    J --> K[用户授权]
    K --> I
    I --> L[服务启动完成]
```

### 语音识别服务流程

```mermaid
flowchart TD
    A[SpeechSherpaRecognitionService.onCreate] --> B[启动前台服务]
    B --> C[获取AppViewModel实例]
    C --> D[初始化AudioRecord]
    D --> E[创建线程池]
    E --> F[异步初始化Sherpa模型]
    F --> G[复制模型文件到内部存储]
    G --> H[配置OnlineRecognizer]
    H --> I[创建识别器实例]
    I --> J[开始音频录制]
    J --> K[启动识别线程]
    K --> L[进入音频处理循环]
```

### 实时语音识别流程

```mermaid
flowchart TD
    A[音频处理循环开始] --> B[读取音频缓冲区]
    B --> C[转换为float数组]
    C --> D[创建OnlineStream]
    D --> E[acceptWaveform]
    E --> F{recognizer.isReady?}
    F -->|是| G[recognizer.decode]
    F -->|否| H[继续读取音频]
    G --> I{isEndpoint?}
    I -->|是| J[添加尾部填充]
    I -->|否| K[获取部分结果]
    J --> L[最终解码]
    L --> M[获取完整文本]
    M --> N[重置stream]
    N --> O[更新ViewModel]
    K --> O
    O --> P[通知UI更新]
    P --> H
    H --> B
```

### 数据流向图

```mermaid
flowchart LR
    A[麦克风音频] --> B[AudioRecord]
    B --> C[SpeechSherpaRecognitionService]
    C --> D[Sherpa-ONNX引擎]
    D --> E[识别结果]
    E --> F[AppViewModel]
    F --> G[LiveData]
    G --> H[MainActivity]
    H --> I[TextView显示]
```

### 组件交互图

```mermaid
sequenceDiagram
    participant M as MainActivity
    participant A as Application
    participant V as AppViewModel
    participant S as SpeechService
    participant R as Recognizer
    
    M->>M: 请求权限
    M->>S: 启动服务
    S->>S: 初始化前台服务
    S->>A: 获取ViewModel
    A->>V: 返回实例
    S->>S: 初始化AudioRecord
    S->>R: 创建识别器
    S->>S: 开始录音
    loop 实时识别
        S->>R: 处理音频数据
        R->>S: 返回识别结果
        S->>V: 更新结果
        V->>M: 通知UI更新
        M->>M: 显示文本
    end
```

## 关键技术特性

### 1. 实时流式识别
- 使用流式Zipformer模型
- 支持端点检测
- 低延迟处理

### 2. 多线程架构
- UI线程处理界面交互
- 后台服务线程处理音频
- 异步初始化模型

### 3. 内存管理
- 模型文件动态复制到内部存储
- 音频缓冲区循环使用
- 及时释放资源

### 4. 权限管理
- 动态请求麦克风权限
- 前台服务权限
- 优雅的权限处理流程

## 配置参数

### 音频配置
```java
private final int sampleRateInHz = 16000;
private int audioSource = MediaRecorder.AudioSource.MIC;
private int channelConfig = AudioFormat.CHANNEL_IN_MONO;
private int audioFormat = AudioFormat.ENCODING_PCM_16BIT;
```

### 模型配置
```java
String modelDir = "sherpa-onnx-streaming-zipformer-bilingual-zh-en-2023-02-20";
onlineModelConfig.setModelType("zipformer");
onlineModelConfig.setDebug(true);
```

## 依赖库

- **sherpa-onnx**: v1.12.1 - 核心语音识别引擎
- **easypermissions**: 3.0.0 - 权限管理
- **androidx**: 标准Android支持库
- **material**: Google Material Design组件

## 部署要求

- **最低SDK**: 28 (Android 9.0)
- **目标SDK**: 34 (Android 14)
- **权限**: RECORD_AUDIO, FOREGROUND_SERVICE
- **架构**: 支持所有Android架构

## 性能优化

1. **模型量化**: 使用int8量化的encoder和joiner模型
2. **内存优化**: 及时释放音频缓冲区和流对象
3. **线程优化**: 使用单线程执行器处理模型初始化
4. **UI优化**: 使用LiveData实现响应式UI更新

## 扩展建议

1. **多语言支持**: 可配置不同语言模型
2. **音频格式**: 支持更多音频输入格式
3. **结果处理**: 添加标点符号、大小写处理
4. **离线模式**: 完全离线的语音识别能力
5. **实时转写**: 支持长时间连续转写