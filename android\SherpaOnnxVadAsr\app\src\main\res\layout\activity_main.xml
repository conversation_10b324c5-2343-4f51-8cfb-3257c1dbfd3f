<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".vad.asr.MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/my_text"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="2.5"
            android:padding="24dp"
            android:scrollbars="vertical"
            android:singleLine="false"
            android:text="@string/hint"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:gravity="bottom"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/record_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="0.5"
            android:text="@string/start" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>