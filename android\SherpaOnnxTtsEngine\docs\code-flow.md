# SherpaOnnx TTS Engine 代码执行流程

## 1. 应用启动流程

### 1.1 应用初始化流程

```mermaid
flowchart TD
    A[应用启动] --> B[MainActivity.onCreate]
    B --> C[TtsEngine.createTts]
    C --> D{TTS实例是否存在?}
    D -->|否| E[initTts]
    D -->|是| F[跳过初始化]
    E --> G[复制Assets资源]
    G --> H[创建OfflineTts实例]
    H --> I[加载用户偏好设置]
    I --> J[初始化AudioTrack]
    J --> K[显示UI界面]
    F --> K
    K --> L[应用就绪]
```

### 1.2 TTS引擎初始化详细流程

```mermaid
flowchart TD
    A[initTts开始] --> B[获取AssetManager]
    B --> C{是否有dataDir?}
    C -->|是| D[copyDataDir]
    C -->|否| E[检查dictDir]
    D --> E
    E --> F{是否有dictDir?}
    F -->|是| G[复制字典目录]
    F -->|否| H[创建TTS配置]
    G --> I[设置规则FST路径]
    I --> H
    H --> J[getOfflineTtsConfig]
    J --> K[加载用户偏好设置]
    K --> L[创建OfflineTts实例]
    L --> M[TTS引擎就绪]
```

## 2. TTS服务注册和启动流程

### 2.1 TTS服务生命周期

```mermaid
flowchart TD
    A[系统启动TtsService] --> B[TtsService.onCreate]
    B --> C[调用onLoadLanguage]
    C --> D[TtsEngine.createTts]
    D --> E[服务就绪]
    E --> F[等待合成请求]
    F --> G[onSynthesizeText]
    G --> H[执行语音合成]
    H --> I[返回音频数据]
    I --> F
    J[系统停止服务] --> K[TtsService.onDestroy]
```

### 2.2 语言支持检查流程

```mermaid
flowchart TD
    A[onIsLanguageAvailable] --> B{请求语言是否为空?}
    B -->|是| C[使用空字符串]
    B -->|否| D[使用请求语言]
    C --> E[与TtsEngine.lang比较]
    D --> E
    E --> F{语言是否匹配?}
    F -->|是| G[返回LANG_AVAILABLE]
    F -->|否| H[返回LANG_NOT_SUPPORTED]
```

## 3. 语音合成执行流程

### 3.1 文本合成主流程

```mermaid
flowchart TD
    A[onSynthesizeText] --> B[验证请求参数]
    B --> C{参数是否有效?}
    C -->|否| D[callback.error]
    C -->|是| E[检查语言支持]
    E --> F{语言是否支持?}
    F -->|否| D
    F -->|是| G[获取TTS实例]
    G --> H[启动音频回调]
    H --> I{文本是否为空?}
    I -->|是| J[callback.done]
    I -->|否| K[创建音频回调函数]
    K --> L[调用generateWithCallback]
    L --> M[流式生成音频]
    M --> N[音频数据转换]
    N --> O[通过callback返回]
    O --> P{生成完成?}
    P -->|否| M
    P -->|是| J
```

### 3.2 音频数据处理流程

```mermaid
flowchart TD
    A[generateWithCallback] --> B[ONNX模型推理]
    B --> C[生成FloatArray音频]
    C --> D[调用ttsCallback]
    D --> E[floatArrayToByteArray]
    E --> F[Float转换为PCM 16-bit]
    F --> G[分块处理音频数据]
    G --> H[callback.audioAvailable]
    H --> I{还有数据?}
    I -->|是| G
    I -->|否| J[处理完成]
```

## 4. 用户界面交互流程

### 4.1 参数调节流程

```mermaid
flowchart TD
    A[用户调节滑块] --> B[更新TtsEngine状态]
    B --> C[保存到SharedPreferences]
    C --> D[UI实时更新显示]
    D --> E[参数生效]
```

### 4.2 文本合成测试流程

```mermaid
flowchart TD
    A[用户输入文本] --> B[点击合成按钮]
    B --> C[获取当前参数]
    C --> D[调用TTS.generate]
    D --> E[生成音频文件]
    E --> F[创建MediaPlayer]
    F --> G[播放音频]
    G --> H[更新播放状态]
```

## 5. 数据检查和安装流程

### 5.1 语音数据检查流程

```mermaid
flowchart TD
    A[系统调用CHECK_TTS_DATA] --> B[CheckVoiceData.onCreate]
    B --> C[创建Intent结果]
    C --> D[设置可用语言列表]
    D --> E[设置不可用语言列表]
    E --> F[返回CHECK_VOICE_DATA_PASS]
    F --> G[结束Activity]
```

### 5.2 示例文本获取流程

```mermaid
flowchart TD
    A[系统调用GET_SAMPLE_TEXT] --> B[GetSampleText.onCreate]
    B --> C[根据语言生成示例文本]
    C --> D[返回示例文本]
    D --> E[结束Activity]
```

## 6. 资源管理流程

### 6.1 Assets资源复制流程

```mermaid
flowchart TD
    A[copyAssets开始] --> B[获取路径下的资源列表]
    B --> C{资源列表是否为空?}
    C -->|是| D[copyFile - 复制单个文件]
    C -->|否| E[创建目录]
    E --> F[遍历子资源]
    F --> G[递归调用copyAssets]
    G --> H{还有子资源?}
    H -->|是| F
    H -->|否| I[复制完成]
    D --> I
```

### 6.2 文件复制详细流程

```mermaid
flowchart TD
    A[copyFile开始] --> B[打开Assets输入流]
    B --> C[创建外部存储输出流]
    C --> D[创建缓冲区]
    D --> E[读取数据块]
    E --> F[写入数据块]
    F --> G{还有数据?}
    G -->|是| E
    G -->|否| H[关闭流]
    H --> I[复制完成]
```

## 7. 错误处理流程

### 7.1 TTS合成错误处理

```mermaid
flowchart TD
    A[合成过程中出错] --> B{错误类型}
    B -->|参数无效| C[callback.error]
    B -->|语言不支持| D[返回LANG_NOT_SUPPORTED]
    B -->|模型加载失败| E[Log错误信息]
    B -->|音频生成失败| F[停止合成]
    C --> G[结束合成]
    D --> G
    E --> G
    F --> G
```

## 8. 配置管理流程

### 8.1 偏好设置管理

```mermaid
flowchart TD
    A[PreferenceHelper初始化] --> B[获取SharedPreferences]
    B --> C[设置默认值]
    C --> D[提供读写接口]
    D --> E[setSpeed/getSpeed]
    D --> F[setSid/getSid]
    E --> G[数据持久化]
    F --> G
```

## 总结

该 TTS 引擎的代码执行流程具有以下特点：

1. **模块化设计**: 各个功能模块职责清晰，便于维护和扩展
2. **标准兼容**: 严格遵循 Android TTS 标准接口规范
3. **资源管理**: 完善的资源复制和管理机制
4. **错误处理**: 全面的错误检查和处理流程
5. **性能优化**: 流式音频生成，减少内存占用
6. **用户体验**: 实时参数调节和音频播放功能

通过这些流程图，可以清晰地理解整个 TTS 引擎的工作原理和代码执行路径。