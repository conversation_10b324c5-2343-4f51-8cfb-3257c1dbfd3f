# SherpaOnnx2Pass API 参考文档

## 概述

本文档详细描述了 SherpaOnnx2Pass 应用中各个核心模块的 API 接口、配置参数和使用方法。

## 核心接口

### 1. SingleModelASREngine

#### 构造函数

```kotlin
class SingleModelASREngine(
    private val assetManager: AssetManager,
    private val sampleRateInHz: Int = 16000,
    private val enableSpeakerIdentification: Boolean = true,
    private val speakerThreshold: Float = 0.5f
)
```

**参数说明:**
- `assetManager`: Android 资源管理器，用于加载模型文件
- `sampleRateInHz`: 音频采样率，默认 16000Hz
- `enableSpeakerIdentification`: 是否启用声纹识别，默认启用
- `speakerThreshold`: 声纹识别阈值，默认 0.5

#### 核心方法

##### initialize()

```kotlin
fun initialize(): Boolean
```

**功能**: 初始化 ASR 引擎和相关组件

**返回值**: 
- `true`: 初始化成功
- `false`: 初始化失败

**异常**: 
- `RuntimeException`: 模型加载失败
- `IllegalStateException`: 重复初始化

##### processAudio()

```kotlin
fun processAudio(audioData: FloatArray)
```

**功能**: 处理音频数据并进行语音识别

**参数**:
- `audioData`: 音频数据数组，格式为 Float 数组

**注意事项**:
- 音频数据应为 16kHz 采样率
- 数据长度建议为 1024 的倍数
- 调用前需确保引擎已初始化

##### setListener()

```kotlin
fun setListener(listener: ASRListener?)
```

**功能**: 设置 ASR 结果监听器

**参数**:
- `listener`: ASR 监听器接口实现，可为 null

##### release()

```kotlin
fun release()
```

**功能**: 释放引擎资源

**注意事项**:
- 应用退出前必须调用
- 调用后引擎不可再使用
- 重复调用安全

#### 监听器接口

```kotlin
interface ASRListener {
    fun onResult(result: ASRResult)
    fun onError(error: String)
    fun onStatusChanged(status: String)
    fun onSpeakerIdentified(speakerInfo: SpeakerInfo)
}
```

**回调说明**:
- `onResult`: 识别结果回调
- `onError`: 错误信息回调
- `onStatusChanged`: 状态变化回调
- `onSpeakerIdentified`: 声纹识别结果回调

#### 数据类

##### ASRResult

```kotlin
data class ASRResult(
    val text: String,
    val isFinal: Boolean,
    val confidence: Float,
    val timestamp: Long,
    val speakerInfo: SpeakerInfo?
)
```

**字段说明**:
- `text`: 识别的文本内容
- `isFinal`: 是否为最终结果
- `confidence`: 识别置信度 (0.0-1.0)
- `timestamp`: 时间戳
- `speakerInfo`: 说话人信息（如果启用声纹识别）

##### SpeakerInfo

```kotlin
data class SpeakerInfo(
    val speakerName: String,
    val confidence: Float,
    val isRegistered: Boolean
)
```

**字段说明**:
- `speakerName`: 说话人姓名
- `confidence`: 识别置信度
- `isRegistered`: 是否为已注册用户

### 2. SpeakerDataManager

#### 构造函数

```kotlin
class SpeakerDataManager(private val context: Context)
```

**参数说明**:
- `context`: Android 上下文对象

#### 核心方法

##### saveSpeaker()

```kotlin
fun saveSpeaker(
    speakerName: String, 
    audioSamples: List<FloatArray>, 
    sampleRate: Int = 16000
): Boolean
```

**功能**: 保存说话人声纹数据

**参数**:
- `speakerName`: 说话人姓名
- `audioSamples`: 音频样本列表
- `sampleRate`: 采样率，默认 16000Hz

**返回值**: 保存是否成功

##### loadSpeaker()

```kotlin
fun loadSpeaker(speakerName: String): SpeakerData?
```

**功能**: 加载指定说话人的声纹数据

**参数**:
- `speakerName`: 说话人姓名

**返回值**: 声纹数据对象，不存在时返回 null

##### getAllSpeakers()

```kotlin
fun getAllSpeakers(): List<String>
```

**功能**: 获取所有已注册说话人列表

**返回值**: 说话人姓名列表

##### deleteSpeaker()

```kotlin
fun deleteSpeaker(speakerName: String): Boolean
```

**功能**: 删除指定说话人的声纹数据

**参数**:
- `speakerName`: 说话人姓名

**返回值**: 删除是否成功

##### updateSpeaker()

```kotlin
fun updateSpeaker(
    speakerName: String, 
    newAudioSamples: List<FloatArray>
): Boolean
```

**功能**: 更新说话人声纹数据

**参数**:
- `speakerName`: 说话人姓名
- `newAudioSamples`: 新的音频样本

**返回值**: 更新是否成功

#### 数据类

##### SpeakerData

```kotlin
data class SpeakerData(
    val speakerName: String,
    val sampleCount: Int,
    val createdTime: Long,
    val updatedTime: Long,
    val audioSamples: List<FloatArray>
)
```

### 3. ApiKeyManager

#### 核心方法

##### getApiKey()

```kotlin
fun getApiKey(context: Context): String?
```

**功能**: 获取 Gemini API 密钥

**优先级**:
1. BuildConfig 中的预设密钥
2. 加密存储中的用户设置密钥
3. 返回 null（需要用户输入）

**参数**:
- `context`: Android 上下文

**返回值**: API 密钥字符串，未设置时返回 null

##### saveApiKey()

```kotlin
fun saveApiKey(context: Context, apiKey: String): Boolean
```

**功能**: 保存 API 密钥到加密存储

**参数**:
- `context`: Android 上下文
- `apiKey`: 要保存的 API 密钥

**返回值**: 保存是否成功

##### hasApiKey()

```kotlin
fun hasApiKey(context: Context): Boolean
```

**功能**: 检查是否已配置 API 密钥

**参数**:
- `context`: Android 上下文

**返回值**: 是否已配置密钥

##### clearApiKey()

```kotlin
fun clearApiKey(context: Context): Boolean
```

**功能**: 清除已保存的 API 密钥

**参数**:
- `context`: Android 上下文

**返回值**: 清除是否成功

### 4. GeminiConfig

#### 配置常量

```kotlin
object GeminiConfig {
    const val GEMINI_API_ENDPOINT = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"
    const val DEFAULT_TIMEOUT = 30000L // 30秒
    const val MAX_RETRY_COUNT = 3
}
```

#### 核心方法

##### isApiKeyConfigured()

```kotlin
fun isApiKeyConfigured(context: Context): Boolean
```

**功能**: 检查 API 密钥是否已配置

##### getApiUrl()

```kotlin
fun getApiUrl(): String
```

**功能**: 获取 Gemini API 端点 URL

##### getApiKey()

```kotlin
fun getApiKey(context: Context): String?
```

**功能**: 获取配置的 API 密钥

##### buildRequest()

```kotlin
fun buildRequest(content: String): JsonObject
```

**功能**: 构建 Gemini API 请求体

**参数**:
- `content`: 要处理的文本内容

**返回值**: JSON 请求对象

### 5. OnlineRecognizer 配置

#### EndpointConfig

```kotlin
data class EndpointConfig(
    val rule1: EndpointRule = EndpointRule(),
    val rule2: EndpointRule = EndpointRule(),
    val rule3: EndpointRule = EndpointRule()
)

data class EndpointRule(
    val mustContainNonSilence: Boolean = false,
    val minTrailingSilence: Float = 2.4f,
    val minUtteranceLength: Float = 0.0f
)
```

#### OnlineModelConfig

```kotlin
data class OnlineModelConfig(
    val transducer: OnlineTransducerModelConfig = OnlineTransducerModelConfig(),
    val paraformer: OnlineParaformerModelConfig = OnlineParaformerModelConfig(),
    val zipformer2Ctc: OnlineZipformer2CtcModelConfig = OnlineZipformer2CtcModelConfig(),
    val nemoCtc: OnlineNeMoCtcModelConfig = OnlineNeMoCtcModelConfig(),
    val tokens: String = "",
    val numThreads: Int = 1,
    val provider: String = "cpu",
    val debug: Boolean = false,
    val modelType: String = "",
    val modelingUnit: String = "cjkchar",
    val bpeVocab: String = ""
)
```

#### OnlineRecognizerConfig

```kotlin
data class OnlineRecognizerConfig(
    val featConfig: FeatureConfig = FeatureConfig(),
    val modelConfig: OnlineModelConfig = OnlineModelConfig(),
    val lmConfig: OnlineLMConfig = OnlineLMConfig(),
    val endpointConfig: EndpointConfig = EndpointConfig(),
    val ctcFstDecoderConfig: OnlineCtcFstDecoderConfig = OnlineCtcFstDecoderConfig(),
    val enableEndpoint: Boolean = true,
    val maxActivePaths: Int = 4,
    val hotwordsFile: String = "",
    val hotwordsScore: Float = 1.5f,
    val ctcFstDecoderConfig: OnlineCtcFstDecoderConfig = OnlineCtcFstDecoderConfig(),
    val ruleFsts: String = "",
    val ruleFars: String = ""
)
```

## 使用示例

### 1. 基本 ASR 使用

```kotlin
class MainActivity : AppCompatActivity(), ASRListener {
    private lateinit var asrEngine: SingleModelASREngine
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化 ASR 引擎
        asrEngine = SingleModelASREngine(
            assetManager = assets,
            sampleRateInHz = 16000,
            enableSpeakerIdentification = true,
            speakerThreshold = 0.6f
        )
        
        // 设置监听器
        asrEngine.setListener(this)
        
        // 初始化引擎
        if (!asrEngine.initialize()) {
            Log.e("ASR", "引擎初始化失败")
            return
        }
    }
    
    private fun startRecording() {
        // 开始录音并处理音频
        val audioRecord = AudioRecord(
            MediaRecorder.AudioSource.MIC,
            16000,
            AudioFormat.CHANNEL_IN_MONO,
            AudioFormat.ENCODING_PCM_FLOAT,
            bufferSize
        )
        
        audioRecord.startRecording()
        
        Thread {
            val buffer = FloatArray(1024)
            while (isRecording) {
                val readCount = audioRecord.read(buffer, 0, buffer.size, AudioRecord.READ_BLOCKING)
                if (readCount > 0) {
                    asrEngine.processAudio(buffer.copyOf(readCount))
                }
            }
        }.start()
    }
    
    override fun onResult(result: ASRResult) {
        runOnUiThread {
            textView.text = result.text
            if (result.speakerInfo != null) {
                speakerLabel.text = "说话人: ${result.speakerInfo.speakerName}"
            }
        }
    }
    
    override fun onError(error: String) {
        Log.e("ASR", "识别错误: $error")
    }
    
    override fun onStatusChanged(status: String) {
        Log.d("ASR", "状态变化: $status")
    }
    
    override fun onSpeakerIdentified(speakerInfo: SpeakerInfo) {
        Log.d("Speaker", "识别到说话人: ${speakerInfo.speakerName}")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        asrEngine.release()
    }
}
```

### 2. 声纹管理使用

```kotlin
class SpeakerManagementActivity : AppCompatActivity() {
    private lateinit var speakerManager: SpeakerDataManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        speakerManager = SpeakerDataManager(this)
        
        // 注册新说话人
        registerSpeaker("张三", recordedAudioSamples)
        
        // 获取所有说话人
        val speakers = speakerManager.getAllSpeakers()
        updateSpeakerList(speakers)
    }
    
    private fun registerSpeaker(name: String, audioSamples: List<FloatArray>) {
        val success = speakerManager.saveSpeaker(name, audioSamples)
        if (success) {
            Toast.makeText(this, "声纹注册成功", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "声纹注册失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun deleteSpeaker(name: String) {
        val success = speakerManager.deleteSpeaker(name)
        if (success) {
            Toast.makeText(this, "删除成功", Toast.LENGTH_SHORT).show()
            // 刷新列表
            updateSpeakerList(speakerManager.getAllSpeakers())
        }
    }
}
```

### 3. API 密钥管理

```kotlin
class SettingsActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 检查是否已配置 API 密钥
        if (!ApiKeyManager.hasApiKey(this)) {
            showApiKeyInputDialog()
        }
        
        // 显示当前配置状态
        updateApiKeyStatus()
    }
    
    private fun showApiKeyInputDialog() {
        val editText = EditText(this)
        editText.inputType = InputType.TYPE_TEXT_VARIATION_PASSWORD
        
        AlertDialog.Builder(this)
            .setTitle("输入 Gemini API 密钥")
            .setView(editText)
            .setPositiveButton("保存") { _, _ ->
                val apiKey = editText.text.toString().trim()
                if (apiKey.isNotEmpty()) {
                    val success = ApiKeyManager.saveApiKey(this, apiKey)
                    if (success) {
                        Toast.makeText(this, "API 密钥保存成功", Toast.LENGTH_SHORT).show()
                        updateApiKeyStatus()
                    } else {
                        Toast.makeText(this, "API 密钥保存失败", Toast.LENGTH_SHORT).show()
                    }
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    private fun updateApiKeyStatus() {
        val hasKey = ApiKeyManager.hasApiKey(this)
        statusText.text = if (hasKey) "已配置" else "未配置"
        statusText.setTextColor(if (hasKey) Color.GREEN else Color.RED)
    }
}
```

## 错误代码

### ASR 引擎错误

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| ASR_001 | 模型文件未找到 | 检查 assets 目录中的模型文件 |
| ASR_002 | 模型加载失败 | 确认模型文件完整性 |
| ASR_003 | 音频格式不支持 | 使用 16kHz 单声道 PCM 格式 |
| ASR_004 | 内存不足 | 释放不必要的资源 |
| ASR_005 | 引擎未初始化 | 先调用 initialize() 方法 |

### 声纹管理错误

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| SPK_001 | 存储空间不足 | 清理应用数据或扩展存储 |
| SPK_002 | 文件读写权限不足 | 检查应用权限设置 |
| SPK_003 | 声纹数据损坏 | 重新注册该说话人 |
| SPK_004 | 说话人已存在 | 使用更新方法或选择其他名称 |

### API 调用错误

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| API_001 | API 密钥无效 | 检查密钥格式和有效性 |
| API_002 | 网络连接失败 | 检查网络连接 |
| API_003 | 请求超时 | 增加超时时间或重试 |
| API_004 | 配额已用完 | 检查 API 使用配额 |
| API_005 | 服务不可用 | 稍后重试或使用本地处理 |

## 性能优化建议

### 1. 内存优化

- 及时释放 AudioRecord 资源
- 使用对象池管理 FloatArray
- 避免在主线程进行重计算
- 定期清理识别结果缓存

### 2. 计算优化

- 使用合适的音频缓冲区大小（建议 1024-4096）
- 启用端点检测减少无效计算
- 合理设置识别阈值
- 使用多线程处理音频数据

### 3. 网络优化

- 实现请求缓存机制
- 使用连接池管理网络连接
- 设置合理的超时时间
- 实现重试机制

### 4. 存储优化

- 定期清理过期的声纹数据
- 压缩音频样本数据
- 使用增量更新机制
- 实现数据备份和恢复

## 版本兼容性

### Android 版本支持

- **最低版本**: Android 7.0 (API 24)
- **目标版本**: Android 14 (API 34)
- **推荐版本**: Android 10+ (API 29+)

### 硬件要求

- **RAM**: 最低 3GB，推荐 4GB+
- **存储**: 最低 100MB 可用空间
- **CPU**: ARM64 架构，支持 NEON 指令集
- **麦克风**: 支持 16kHz 采样率

### 依赖库版本

- **Kotlin**: 1.8.0+
- **Android Gradle Plugin**: 8.0.0+
- **Sherpa-ONNX**: 最新版本
- **OkHttp**: 4.9.0+
- **Gson**: 2.8.9+

这个 API 参考文档提供了完整的接口说明和使用示例，帮助开发者快速集成和使用 SherpaOnnx2Pass 的各项功能。