# SherpaOnnx 模拟流式语音识别 Android 应用

## 项目概述

这是一个基于 Sherpa-ONNX 的 Android 语音识别应用，使用非流式 ASR 模型结合 Silero-VAD 实现实时语音识别功能。应用采用 Jetpack Compose 构建现代化的用户界面。

## 主要功能模块

### 1. 核心模块

#### 1.1 语音识别引擎 (SimulateStreamingAsr)
- **功能**: 初始化和管理离线语音识别器和语音活动检测器
- **核心组件**:
  - `OfflineRecognizer`: 离线语音识别器
  - `Vad`: 语音活动检测器 (Voice Activity Detection)
  - 支持多种模型类型和配置

#### 1.2 音频处理模块
- **功能**: 实时音频录制和处理
- **特性**:
  - 16kHz 采样率
  - 16位 PCM 编码
  - 单声道录制
  - 实时音频流处理

#### 1.3 语音活动检测 (VAD)
- **功能**: 检测语音片段的开始和结束
- **配置**:
  - 窗口大小: 512 样本
  - 最小静音持续时间: 0.25秒
  - 最小语音持续时间: 0.25秒
  - 检测阈值: 0.5

### 2. 用户界面模块

#### 2.1 主界面 (MainActivity)
- **功能**: 应用入口点，权限管理
- **职责**:
  - 请求录音权限
  - 初始化语音识别组件
  - 设置应用主题和导航

#### 2.2 导航系统
- **组件**:
  - `NavRoutes`: 定义路由
  - `NavBarItems`: 底部导航栏配置
  - `BarItem`: 导航项数据模型

#### 2.3 屏幕组件
- **Home 屏幕**: 主要的语音识别界面
- **Help 屏幕**: 帮助和说明信息

### 3. 配置模块

#### 3.1 模型配置
- 支持多种 ASR 模型类型
- 可配置的线程数和处理参数
- 同音词替换配置 (可选)

#### 3.2 主题配置
- Material Design 3 主题
- 自定义颜色和字体
- 响应式设计

## 技术架构

### 技术栈
- **开发语言**: Kotlin
- **UI 框架**: Jetpack Compose
- **导航**: Navigation Compose
- **语音识别**: Sherpa-ONNX
- **构建工具**: Gradle (Kotlin DSL)
- **最低 SDK**: 21 (Android 5.0)
- **目标 SDK**: 34 (Android 14)

### 架构模式
- **MVVM**: Model-View-ViewModel 架构
- **单例模式**: 语音识别器和 VAD 管理
- **协程**: 异步音频处理
- **组合式 UI**: Jetpack Compose 声明式 UI

## 权限要求

- `RECORD_AUDIO`: 录音权限，用于实时语音识别

## 项目结构

```
app/src/main/java/com/k2fsa/sherpa/onnx/
├── simulate/streaming/asr/
│   ├── MainActivity.kt              # 主活动
│   ├── SimulateStreamingAsr.kt      # 语音识别核心管理
│   ├── NavRoutes.kt                 # 路由定义
│   ├── NavBarItems.kt               # 导航栏配置
│   ├── BarItem.kt                   # 导航项模型
│   ├── screens/
│   │   ├── Home.kt                  # 主屏幕
│   │   └── Help.kt                  # 帮助屏幕
│   └── ui/theme/                    # UI 主题
├── OfflineRecognizer.kt             # 离线识别器
├── Vad.kt                          # 语音活动检测
├── FeatureConfig.kt                # 特征配置
└── HomophoneReplacerConfig.kt      # 同音词替换配置
```

## 开发环境

- **Android Studio**: 推荐最新版本
- **Gradle**: 8.0+
- **Kotlin**: 1.9+
- **Compose BOM**: 最新稳定版
- **JVM Target**: 1.8

## 构建和运行

1. 克隆项目到本地
2. 使用 Android Studio 打开项目
3. 同步 Gradle 依赖
4. 连接 Android 设备或启动模拟器
5. 运行应用

## 注意事项

- 应用需要录音权限才能正常工作
- 建议在真实设备上测试语音识别功能
- 模型文件需要放置在 assets 目录中
- 支持的音频格式: 16kHz, 16-bit PCM, 单声道