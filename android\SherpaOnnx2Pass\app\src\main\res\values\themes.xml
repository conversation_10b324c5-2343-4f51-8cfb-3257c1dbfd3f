<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Apple-inspired Voice Assistant Theme -->
    <style name="Theme.VoiceAssistant" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Apple Blue Primary Colors -->
        <item name="colorPrimary">@color/apple_blue</item>
        <item name="colorPrimaryVariant">@color/apple_blue_dark</item>
        <item name="colorOnPrimary">@color/white</item>

        <!-- Apple Green Secondary Colors -->
        <item name="colorSecondary">@color/apple_green</item>
        <item name="colorSecondaryVariant">@color/apple_green_dark</item>
        <item name="colorOnSecondary">@color/white</item>

        <!-- Apple System Background Colors -->
        <item name="android:colorBackground">@color/apple_system_grouped_background</item>
        <item name="colorOnBackground">@color/apple_label</item>
        <item name="colorSurface">@color/apple_system_background</item>
        <item name="colorOnSurface">@color/apple_label</item>

        <!-- Apple Error Colors -->
        <item name="colorError">@color/apple_red</item>
        <item name="colorOnError">@color/white</item>

        <!-- Status Bar - Apple Style -->
        <item name="android:statusBarColor">@color/apple_system_grouped_background</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:navigationBarColor">@color/apple_system_grouped_background</item>
        <item name="android:windowLightNavigationBar">true</item>

        <!-- Window Properties -->
        <item name="android:windowBackground">@color/apple_system_grouped_background</item>
        <item name="android:windowContentTransitions">true</item>
        <item name="android:windowActivityTransitions">true</item>

        <!-- Text Appearance -->
        <item name="android:textColorPrimary">@color/apple_label</item>
        <item name="android:textColorSecondary">@color/apple_secondary_label</item>
        <item name="android:textColorTertiary">@color/apple_tertiary_label</item>

        <!-- Button Styles -->
        <item name="materialButtonStyle">@style/Widget.VoiceAssistant.Button</item>
        <item name="borderlessButtonStyle">@style/Widget.VoiceAssistant.Button.Borderless</item>
    </style>

    <!-- Apple-style Button -->
    <style name="Widget.VoiceAssistant.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/apple_blue</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.01</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="elevation">2dp</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

    <!-- Apple-style Borderless Button -->
    <style name="Widget.VoiceAssistant.Button.Borderless" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/apple_blue</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.01</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="rippleColor">@color/apple_blue_alpha_20</item>
    </style>

    <!-- Apple-style Outlined Button -->
    <style name="Widget.VoiceAssistant.Button.Outlined" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textColor">@color/apple_blue</item>
        <item name="strokeColor">@color/apple_blue</item>
        <item name="strokeWidth">1dp</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.01</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:paddingStart">20dp</item>
        <item name="android:paddingEnd">20dp</item>
        <item name="rippleColor">@color/apple_blue_alpha_20</item>
        <item name="backgroundTint">@android:color/transparent</item>
    </style>

    <!-- Recording Button Style -->
    <style name="Widget.VoiceAssistant.RecordButton" parent="Widget.MaterialComponents.Button">
        <item name="android:layout_width">120dp</item>
        <item name="android:layout_height">120dp</item>
        <item name="cornerRadius">60dp</item>
        <item name="backgroundTint">@color/apple_blue</item>
        <item name="elevation">8dp</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="rippleColor">@color/apple_blue_alpha_30</item>
    </style>

    <!-- Card Style -->
    <style name="Widget.VoiceAssistant.Card" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/apple_system_background</item>
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">2dp</item>
        <item name="contentPadding">20dp</item>
        <item name="android:layout_margin">16dp</item>
    </style>

    <!-- Text Styles -->
    <style name="TextAppearance.VoiceAssistant.Headline" parent="TextAppearance.MaterialComponents.Headline5">
        <item name="android:textColor">@color/apple_label</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">28sp</item>
        <item name="android:letterSpacing">0.01</item>
    </style>

    <style name="TextAppearance.VoiceAssistant.Title" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textColor">@color/apple_label</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">20sp</item>
        <item name="android:letterSpacing">0.01</item>
    </style>

    <style name="TextAppearance.VoiceAssistant.Body" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textColor">@color/apple_label</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textSize">16sp</item>
        <item name="android:lineSpacingExtra">4dp</item>
    </style>

    <style name="TextAppearance.VoiceAssistant.Caption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:textColor">@color/apple_secondary_label</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textSize">14sp</item>
    </style>

    <!-- Legacy Theme for Compatibility -->
    <style name="Theme.SherpaOnnx2Pass" parent="Theme.VoiceAssistant" />
    <style name="Theme.SherpaOnnx2Pass.Optimized" parent="Theme.VoiceAssistant" />
</resources>
