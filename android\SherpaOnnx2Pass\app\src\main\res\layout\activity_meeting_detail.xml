<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/apple_system_grouped_background"
    android:fitsSystemWindows="true">

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Header Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="24dp"
                android:paddingBottom="16dp"
                android:background="@color/apple_system_background"
                android:elevation="2dp">

                <ImageButton
                    android:id="@+id/btn_back"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_arrow_back_apple"
                    android:contentDescription="返回"
                    android:layout_marginEnd="16dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_meeting_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="会议标题"
                        android:textAppearance="@style/TextAppearance.VoiceAssistant.Headline"
                        android:layout_marginBottom="4dp"
                        android:maxLines="2"
                        android:ellipsize="end" />

                    <TextView
                        android:id="@+id/tv_meeting_datetime"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="2024年12月25日 14:30"
                        android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                        android:textColor="@color/apple_secondary_label" />

                </LinearLayout>

                <ImageButton
                    android:id="@+id/btn_delete"
                    android:layout_width="44dp"
                    android:layout_height="44dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_delete_apple"
                    android:contentDescription="删除"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <!-- Stats Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="24dp"
                android:paddingTop="16dp"
                android:paddingBottom="16dp"
                android:background="@color/apple_system_background">

                <TextView
                    android:id="@+id/tv_meeting_duration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="时长: 05:30"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                    android:background="@drawable/stat_background"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="6dp"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:id="@+id/tv_meeting_word_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="字数: 1250"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                    android:background="@drawable/stat_background"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="6dp"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:id="@+id/tv_meeting_speaker_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="说话人: 3 人"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                    android:background="@drawable/stat_background"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="6dp" />

            </LinearLayout>

            <!-- Content Sections -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Audio Recording Card -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/card_audio"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardBackgroundColor="@color/apple_system_background"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="16dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="🎙️ 原始录音"
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Title" />

                            <Button
                                android:id="@+id/btn_play_audio"
                                style="@style/Widget.VoiceAssistant.Button.Borderless"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="▶️ 播放录音"
                                android:textSize="14sp"
                                android:minWidth="0dp"
                                android:paddingHorizontal="16dp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_audio_info"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="录音文件 (2.5 MB)"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                            android:textColor="@color/apple_secondary_label"
                            android:drawableStart="@drawable/ic_mic"
                            android:drawablePadding="8dp"
                            android:gravity="center_vertical" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Smart Summary Card -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/card_summary"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardBackgroundColor="@color/apple_system_background"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="16dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="🤖 智能总结"
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Title" />

                            <Button
                                android:id="@+id/btn_copy_summary"
                                style="@style/Widget.VoiceAssistant.Button.Borderless"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="复制"
                                android:textSize="14sp"
                                android:minWidth="0dp"
                                android:paddingHorizontal="16dp" />

                            <Button
                                android:id="@+id/btn_generate_summary"
                                style="@style/Widget.VoiceAssistant.Button.Borderless"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🤖 生成总结"
                                android:textSize="14sp"
                                android:minWidth="0dp"
                                android:paddingHorizontal="16dp"
                                android:visibility="gone" />

                        </LinearLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <ScrollView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:nestedScrollingEnabled="true"
                                android:overScrollMode="never"
                                android:scrollbars="vertical"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintHeight_max="300dp">

                            <TextView
                                android:id="@+id/tv_summary_content"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="智能总结内容..."
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                                android:lineSpacingExtra="6dp"
                                android:textIsSelectable="true" />

                            </ScrollView>

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Optimized Content Card -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/card_optimized"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardBackgroundColor="@color/apple_system_background"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="16dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="✨ 优化内容"
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Title" />

                            <Button
                                android:id="@+id/btn_copy_optimized"
                                style="@style/Widget.VoiceAssistant.Button.Borderless"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="复制"
                                android:textSize="14sp"
                                android:minWidth="0dp"
                                android:paddingHorizontal="16dp" />

                            <Button
                                android:id="@+id/btn_generate_optimized"
                                style="@style/Widget.VoiceAssistant.Button.Borderless"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="✨ 生成优化"
                                android:textSize="14sp"
                                android:minWidth="0dp"
                                android:paddingHorizontal="16dp"
                                android:visibility="gone" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_optimized_content"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="优化后的内容..."
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                            android:lineSpacingExtra="6dp"
                            android:textIsSelectable="true" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Original Content Card -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/card_original"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardBackgroundColor="@color/apple_system_background"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="16dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="📄 原始记录"
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Title" />

                            <Button
                                android:id="@+id/btn_retranscribe"
                                style="@style/Widget.VoiceAssistant.Button.Borderless"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🔄 重新转录"
                                android:textSize="14sp"
                                android:minWidth="0dp"
                                android:paddingHorizontal="12dp"
                                android:layout_marginEnd="8dp"
                                android:visibility="gone" />

                            <Button
                                android:id="@+id/btn_copy_original"
                                style="@style/Widget.VoiceAssistant.Button.Borderless"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="复制"
                                android:textSize="14sp"
                                android:minWidth="0dp"
                                android:paddingHorizontal="16dp" />

                        </LinearLayout>

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <ScrollView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:nestedScrollingEnabled="true"
                                android:overScrollMode="never"
                                android:scrollbars="vertical"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintHeight_max="300dp">

                            <TextView
                                android:id="@+id/tv_original_content"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="原始识别内容..."
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                                android:lineSpacingExtra="6dp"
                                android:textIsSelectable="true"
                                android:fontFamily="monospace" />

                            </ScrollView>

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- AI Chat Card -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/card_ai_chat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardBackgroundColor="@color/apple_system_background"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="16dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="💬 AI智能问答"
                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Title" />

                            <Button
                                android:id="@+id/btn_open_chat"
                                style="@style/Widget.VoiceAssistant.Button.Borderless"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="💬 开始聊天"
                                android:textSize="14sp"
                                android:minWidth="0dp"
                                android:paddingHorizontal="16dp" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tv_chat_description"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="基于会议记录内容，与AI助手进行智能问答交流"
                            android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                            android:textColor="@color/apple_secondary_label"
                            android:drawableStart="@drawable/ic_chat"
                            android:drawablePadding="8dp"
                            android:gravity="center_vertical" />

                        <!-- Chat History Section (Initially Hidden) -->
                        <LinearLayout
                            android:id="@+id/layout_chat_history"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:layout_marginTop="16dp"
                            android:visibility="gone">

                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="@color/apple_separator"
                                android:layout_marginBottom="16dp" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="💭 最近聊天记录"
                                android:layout_marginBottom="12dp" />
<!--                                android:textAppearance="@style/TextAppearance.VoiceAssistant.Subheadline"-->

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rv_chat_history"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:maxHeight="200dp"
                                android:nestedScrollingEnabled="false" />

                        </LinearLayout>

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Copy All Button -->
                <Button
                    android:id="@+id/btn_copy_all"
                    style="@style/Widget.VoiceAssistant.Button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📋 复制完整记录"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="32dp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
