/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.8);
    padding: 12px 20px;
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background-color: #4CAF50;
}

.status-dot.disconnected {
    background-color: #f44336;
}

.status-dot.reconnecting {
    background-color: #ff9800;
}

.status-dot.error {
    background-color: #f44336;
}

.status-text {
    font-weight: 500;
    color: #555;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes recordingPulse {
    0% { 
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }
    50% { 
        transform: scale(1.05);
        box-shadow: 0 6px 25px rgba(220, 53, 69, 0.5);
    }
    100% { 
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }
}

/* 主内容区域 */
.main-content {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.text-display {
    min-height: 400px;
    max-height: 600px;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
    border: 2px solid #e9ecef;
    font-size: 1.2rem;
    line-height: 1.8;
    position: relative;
}

.text-display::-webkit-scrollbar {
    width: 8px;
}

.text-display::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.text-display::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.text-display::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.welcome-message {
    text-align: center;
    color: #6c757d;
    padding: 60px 20px;
}

.welcome-message p {
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.welcome-message .hint {
    font-size: 1rem;
    opacity: 0.7;
}

.text-line {
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    word-wrap: break-word;
    white-space: pre-wrap;
    position: relative;
}

.text-line.final {
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    border-left: 4px solid #2196F3;
    color: #1565C0;
    animation: slideIn 0.3s ease-out;
}

.text-line.preview {
    background: linear-gradient(135deg, #fff3e0, #fce4ec);
    border-left: 4px solid #ff9800;
    color: #e65100;
    opacity: 0.8;
    animation: pulse 1s infinite;
}

.timestamp {
    position: absolute;
    top: 5px;
    right: 10px;
    font-size: 0.8rem;
    color: #666;
    background: rgba(255, 255, 255, 0.8);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: monospace;
}

/* 兼容旧的class名 */
.current-text {
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.current-text.final {
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    border-left: 4px solid #2196F3;
    color: #1565C0;
}

.current-text.preview {
    background: linear-gradient(135deg, #fff3e0, #fce4ec);
    border-left: 4px solid #ff9800;
    color: #e65100;
    opacity: 0.8;
}

/* 底部控制区域 */
.footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.controls {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.btn-clear {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

.btn-save {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
}

.btn-record {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    position: relative;
    overflow: hidden;
}

.btn-record.recording {
    background: linear-gradient(135deg, #dc3545, #c82333);
    animation: recordingPulse 1s infinite;
}

.btn-reconnect {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.info {
    display: flex;
    gap: 15px;
    color: #6c757d;
    font-size: 0.9rem;
    align-items: center;
    flex-wrap: wrap;
}

.info span {
    white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .text-display {
        min-height: 300px;
        max-height: 400px;
        font-size: 1.1rem;
        padding: 15px;
    }
    
    .controls {
        justify-content: center;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .info {
        justify-content: center;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.5rem;
    }
    
    .text-display {
        font-size: 1rem;
        padding: 10px;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 200px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

.container > * {
    animation: fadeIn 0.6s ease-out;
}

.text-line {
    animation: fadeIn 0.3s ease-out;
}

.current-text {
    animation: fadeIn 0.3s ease-out;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }
    
    .header, .main-content, .footer {
        background: rgba(44, 62, 80, 0.95);
        color: #ecf0f1;
    }
    
    .text-display {
        background: #34495e;
        border-color: #4a6741;
        color: #ecf0f1;
    }
    
    .current-text.final {
        background: linear-gradient(135deg, #2c3e50, #34495e);
        border-left-color: #3498db;
        color: #3498db;
    }
    
    .current-text.preview {
        background: linear-gradient(135deg, #2c3e50, #34495e);
        border-left-color: #f39c12;
        color: #f39c12;
    }
}

/* 总结按钮样式 */
.btn-summary {
    background: linear-gradient(135deg, #9c27b0, #673ab7);
    color: white;
}

/* 弹窗样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background: white;
    border-radius: 20px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease-out;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 25px 30px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 2rem;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.modal-body {
    padding: 30px;
    flex: 1;
    overflow-y: auto;
}

.summary-options {
    margin-bottom: 25px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.summary-options label {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.summary-options label:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.summary-options input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: #667eea;
}

.summary-options input[type="radio"]:checked + span {
    font-weight: 600;
    color: #667eea;
}

/* 移除优化选项的特殊样式，保持与其他选项一致 */

.summary-result {
    min-height: 200px;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 20px;
    background: #f8f9fa;
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
    padding: 40px;
    color: #6c757d;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e9ecef;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.summary-content {
    line-height: 1.8;
    color: #333;
}

.summary-content h2 {
    color: #667eea;
    margin: 20px 0 15px 0;
    font-size: 1.3rem;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
}

.summary-content h3 {
    color: #764ba2;
    margin: 15px 0 10px 0;
    font-size: 1.1rem;
}

.summary-content ul {
    margin: 10px 0;
    padding-left: 20px;
}

.summary-content li {
    margin: 8px 0;
    padding: 5px 0;
}

/* 时间戳通用样式（适用于所有类型） */
.summary-content .timestamp {
    background: #e9ecef;
    color: #495057;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: monospace;
    font-weight: bold;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    background: #f8f9fa;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
}

/* 响应式弹窗 */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        max-height: 95vh;
    }

    .modal-header {
        padding: 20px;
    }

    .modal-header h2 {
        font-size: 1.3rem;
    }

    .modal-body {
        padding: 20px;
    }

    .summary-options {
        gap: 10px;
    }

    .summary-options label {
        padding: 12px;
        font-size: 0.9rem;
    }

    .modal-footer {
        padding: 15px 20px;
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
    }
}

/* 深色模式下的弹窗样式 */
@media (prefers-color-scheme: dark) {
    .modal-content {
        background: #2c3e50;
        color: #ecf0f1;
    }

    .modal-header {
        border-bottom-color: #34495e;
    }

    .modal-body {
        background: #2c3e50;
    }

    .summary-options label {
        background: #34495e;
        border-color: #4a6741;
        color: #ecf0f1;
    }

    .summary-options label:hover {
        border-color: #3498db;
        background: #2c3e50;
    }

    .summary-result {
        background: #34495e;
        border-color: #4a6741;
    }

    .summary-content {
        color: #ecf0f1;
    }

    .summary-content h2 {
        color: #3498db;
        border-bottom-color: #4a6741;
    }

    .summary-content h3 {
        color: #9b59b6;
    }

    .modal-footer {
        background: #34495e;
        border-top-color: #4a6741;
    }
}
