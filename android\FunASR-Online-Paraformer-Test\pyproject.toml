[tool.poetry]
name = "asr_service"
version = "0.1.0"
description = ""
authors = ["q<PERSON><PERSON><PERSON><PERSON><PERSON> <qia<PERSON><PERSON><PERSON><PERSON>@vip.qq.com>"]
readme = "README.md"
packages = [{include = "src", from="."},{include = "app", from="."}]

[tool.poetry.dependencies]
python = "^3.9"
websockets = "^12.0"
numpy = "^1.24.0"
sounddevice = "^0.4.6"
rich = "^13.0.0"
colorama = "^0.4.6"
soundfile = "^0.12.1"
# FastAPI相关依赖
fastapi = "^0.104.0"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
python-multipart = "^0.0.6"
pydantic = "^2.0.0"
pydantic-settings = "^2.0.0"
# 开发和测试依赖
pytest = {version = "^7.0.0", optional = true}
pytest-asyncio = {version = "^0.21.0", optional = true}
httpx = {version = "^0.25.0", optional = true}

[tool.poetry.extras]
test = ["pytest", "pytest-asyncio", "httpx"]


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
