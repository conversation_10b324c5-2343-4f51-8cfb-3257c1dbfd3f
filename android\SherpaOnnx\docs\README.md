# SherpaOnnx Android 项目功能模块总结

## 项目概述

SherpaOnnx 是一个基于 ONNX 的实时语音识别 (ASR) Android 应用，使用 Next-gen <PERSON>ld<PERSON> 技术栈。该项目提供了实时语音转文字功能，支持多种语音识别模型。

## 主要功能模块

### 1. 核心识别模块 (OnlineRecognizer)
- **功能**: 语音识别的核心引擎
- **主要类**: `OnlineRecognizer`
- **职责**:
  - 管理 ONNX 模型加载和初始化
  - 提供语音流处理接口
  - 支持多种模型类型 (Transducer, Paraformer, Zipformer2Ctc, NeMoCtc)
  - 实现端点检测和结果获取

### 2. 音频流处理模块 (OnlineStream)
- **功能**: 音频数据流管理
- **主要类**: `OnlineStream`
- **职责**:
  - 接收音频波形数据
  - 管理音频流状态
  - 与 JNI 层交互处理音频数据

### 3. 特征提取配置模块 (FeatureConfig)
- **功能**: 音频特征提取参数配置
- **主要类**: `FeatureConfig`
- **职责**:
  - 配置采样率 (默认 16kHz)
  - 设置特征维度 (默认 80 维)
  - 管理抖动参数

### 4. 音频文件读取模块 (WaveReader)
- **功能**: 音频文件读取和处理
- **主要类**: `WaveReader`, `WaveData`
- **职责**:
  - 从 Assets 或文件系统读取音频文件
  - 音频格式转换和数据封装

### 5. 同音字替换模块 (HomophoneReplacerConfig)
- **功能**: 语音识别结果后处理
- **主要类**: `HomophoneReplacerConfig`
- **职责**:
  - 配置同音字词典
  - 管理替换规则

### 6. 主界面控制模块 (MainActivity)
- **功能**: 用户界面和应用生命周期管理
- **主要类**: `MainActivity`
- **职责**:
  - 权限管理 (录音权限)
  - 音频录制控制
  - 实时语音识别处理
  - UI 更新和用户交互

## 技术架构特点

### 1. JNI 集成
- 使用 `sherpa-onnx-jni` 原生库
- 高性能的 C++ 底层实现
- Kotlin/Java 与 C++ 的桥接

### 2. 实时处理
- 100ms 音频缓冲区处理
- 流式语音识别
- 端点检测和分段处理

### 3. 多模型支持
- Transducer 模型
- Paraformer 模型
- Zipformer2Ctc 模型
- NeMoCtc 模型

### 4. 配置化设计
- 模型配置可扩展
- 特征提取参数可调
- 端点检测规则可配置

## 依赖关系

- **Android SDK**: API Level 21+
- **Kotlin**: 1.7.20
- **AndroidX**: Core, AppCompat, Material Design
- **原生库**: sherpa-onnx-jni
- **权限**: RECORD_AUDIO

## 项目结构

```
app/src/main/
├── AndroidManifest.xml          # 应用清单文件
├── java/com/k2fsa/sherpa/onnx/  # 主要源代码
│   ├── MainActivity.kt          # 主活动
│   ├── OnlineRecognizer.kt      # 语音识别器
│   ├── OnlineStream.kt          # 音频流处理
│   ├── FeatureConfig.kt         # 特征配置
│   ├── WaveReader.kt            # 音频读取
│   └── HomophoneReplacerConfig.kt # 同音字配置
├── res/layout/                  # 布局文件
│   └── activity_main.xml        # 主界面布局
├── assets/                      # 资源文件
└── jniLibs/                     # 原生库文件
    ├── arm64-v8a/
    ├── armeabi-v7a/
    ├── x86/
    └── x86_64/
```