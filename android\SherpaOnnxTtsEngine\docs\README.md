# SherpaOnnx TTS Engine 项目文档

## 项目概述

SherpaOnnx TTS Engine 是一个基于 Android 平台的文本转语音（Text-to-Speech）引擎，使用 Sherpa-ONNX 库实现高质量的语音合成功能。该项目支持多种 TTS 模型，包括 VITS、Matcha 和 Kokoro 等。

## 主要功能模块

### 1. 核心引擎模块 (TtsEngine)
- **功能**: TTS 引擎的核心管理类，负责模型初始化和配置
- **主要职责**:
  - 管理 TTS 模型配置（VITS、Matcha、Kokoro）
  - 处理模型文件的加载和资源管理
  - 提供语音合成参数配置（语速、说话人ID）
  - 管理资源文件的复制和初始化

### 2. TTS 服务模块 (TtsService)
- **功能**: Android TTS 服务的具体实现
- **主要职责**:
  - 继承 TextToSpeechService，实现标准 TTS 接口
  - 处理语言支持检查和语言加载
  - 执行文本合成并通过回调返回音频数据
  - 音频格式转换（Float 到 PCM 16-bit）

### 3. 用户界面模块 (MainActivity)
- **功能**: TTS 引擎的配置和测试界面
- **主要职责**:
  - 提供语速和说话人ID的调节界面
  - 实现文本输入和语音合成测试功能
  - 音频播放控制和管理
  - 用户偏好设置的保存和加载

### 4. 数据检查模块 (CheckVoiceData)
- **功能**: 检查 TTS 引擎的语音数据完整性
- **主要职责**:
  - 响应系统的语音数据检查请求
  - 返回支持的语言列表
  - 确保 TTS 引擎数据的可用性

### 5. 数据安装模块 (InstallVoiceData)
- **功能**: 处理语音数据的安装请求
- **主要职责**:
  - 响应系统的语音数据安装请求
  - 管理语音模型的下载和安装流程

### 6. 示例文本模块 (GetSampleText)
- **功能**: 提供 TTS 测试用的示例文本
- **主要职责**:
  - 响应系统的示例文本请求
  - 为不同语言提供合适的测试文本

### 7. 偏好设置模块 (PreferencesHelper)
- **功能**: 管理用户偏好设置的持久化存储
- **主要职责**:
  - 保存和读取语速设置
  - 保存和读取说话人ID设置
  - 提供设置的默认值管理

### 8. ONNX 接口模块 (Tts.kt)
- **功能**: Sherpa-ONNX 库的 Kotlin 接口封装
- **主要职责**:
  - 定义 TTS 模型配置数据结构
  - 提供音频生成接口
  - 管理 JNI 调用和本地库交互

## 支持的 TTS 模型

1. **VITS 模型**
   - 支持多语言语音合成
   - 可配置噪声比例和长度比例
   - 支持词典和数据目录配置

2. **Matcha 模型**
   - 分离式声学模型和声码器
   - 高质量语音合成
   - 支持自定义声码器

3. **Kokoro 模型**
   - 多语言支持
   - 声音库管理
   - 灵活的语言配置

## 技术特性

- **多语言支持**: 支持中文、英文、德文等多种语言
- **实时合成**: 支持流式音频生成和播放
- **参数可调**: 可调节语速、说话人等参数
- **标准兼容**: 完全兼容 Android TTS 标准接口
- **高性能**: 基于 ONNX 运行时，优化的推理性能

## 项目结构

```
app/src/main/java/com/k2fsa/sherpa/onnx/tts/engine/
├── TtsEngine.kt          # 核心引擎管理
├── TtsService.kt         # TTS 服务实现
├── MainActivity.kt       # 主界面Activity
├── CheckVoiceData.kt     # 数据检查Activity
├── InstallVoiceData.kt   # 数据安装Activity
├── GetSampleText.kt      # 示例文本Activity
├── PreferencesHelper.kt  # 偏好设置管理
├── Tts.kt               # ONNX接口封装
├── TtsViewModel.kt      # UI状态管理
└── ui/                  # UI主题相关
```

## 配置文件

- **AndroidManifest.xml**: 声明 TTS 服务和相关 Activity
- **tts_engine.xml**: TTS 引擎配置文件
- **build.gradle.kts**: 项目构建配置