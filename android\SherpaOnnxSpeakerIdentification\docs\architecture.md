# 系统架构设计文档

## 整体架构概览

### 1. 系统架构图

```mermaid
graph TB
    subgraph "Android Application Layer"
        A[MainActivity]
        B[Compose UI Layer]
        C[Navigation System]
    end
    
    subgraph "Business Logic Layer"
        D[SpeakerRecognition]
        E[Audio Processing]
        F[Permission Manager]
    end
    
    subgraph "Data Layer"
        G[SpeakerEmbeddingManager]
        H[Model Configuration]
        I[Audio Stream]
    end
    
    subgraph "Native Layer (JNI)"
        J[sherpa-onnx-jni]
        K[ONNX Runtime]
        L[Audio Processing C++]
    end
    
    subgraph "System Layer"
        M[Android AudioRecord]
        N[File System]
        O[Permissions]
    end
    
    A --> B
    B --> C
    A --> D
    D --> E
    D --> F
    E --> G
    G --> H
    E --> I
    G --> J
    J --> K
    J --> L
    E --> M
    H --> N
    F --> O
```

## 分层架构详解

### 2. UI层架构 (Presentation Layer)

```mermaid
graph TD
    subgraph "UI Layer"
        A[MainActivity]
        B[MainScreen Composable]
        C[Navigation Host]
        
        subgraph "Screen Composables"
            D[HomeScreen]
            E[RegisterScreen]
            F[ViewScreen]
            G[HelpScreen]
        end
        
        subgraph "UI Components"
            H[TopAppBar]
            I[BottomNavigationBar]
            J[Custom Buttons]
            K[Input Fields]
        end
        
        subgraph "State Management"
            L[remember State]
            M[mutableStateOf]
            N[State Hoisting]
        end
    end
    
    A --> B
    B --> C
    C --> D
    C --> E
    C --> F
    C --> G
    
    D --> H
    D --> I
    E --> J
    E --> K
    
    D --> L
    E --> M
    F --> N
```

### 3. 业务逻辑层架构 (Business Logic Layer)

```mermaid
graph TD
    subgraph "Business Logic Layer"
        A[SpeakerRecognition Object]
        
        subgraph "Core Components"
            B[SpeakerEmbeddingExtractor]
            C[SpeakerEmbeddingManager]
            D[OnlineStream]
        end
        
        subgraph "Audio Processing"
            E[AudioRecord Manager]
            F[Sample Processing]
            G[Format Conversion]
        end
        
        subgraph "Configuration"
            H[SpeakerEmbeddingExtractorConfig]
            I[Model Parameters]
            J[Thread Configuration]
        end
        
        subgraph "State Management"
            K[Recording State]
            L[Recognition State]
            M[Registration State]
        end
    end
    
    A --> B
    A --> C
    B --> D
    
    A --> E
    E --> F
    F --> G
    
    B --> H
    H --> I
    H --> J
    
    E --> K
    B --> L
    C --> M
```

### 4. 数据层架构 (Data Layer)

```mermaid
graph TD
    subgraph "Data Layer"
        subgraph "Speaker Data Management"
            A[SpeakerEmbeddingManager]
            B[Speaker Database]
            C[Embedding Vectors]
            D[Speaker Names]
        end
        
        subgraph "Audio Data"
            E[OnlineStream]
            F[Audio Buffers]
            G[Sample Lists]
            H[Feature Vectors]
        end
        
        subgraph "Model Data"
            I[ONNX Model File]
            J[Model Configuration]
            K[Asset Manager]
        end
        
        subgraph "Temporary Data"
            L[Recording Samples]
            M[Processing Queue]
            N[Result Cache]
        end
    end
    
    A --> B
    B --> C
    B --> D
    
    E --> F
    F --> G
    G --> H
    
    I --> J
    J --> K
    
    F --> L
    G --> M
    H --> N
```

## 组件交互架构

### 5. 说话人识别流程架构

```mermaid
sequenceDiagram
    participant UI as HomeScreen
    participant BL as Business Logic
    participant Audio as AudioRecord
    participant Extractor as SpeakerEmbeddingExtractor
    participant Manager as SpeakerEmbeddingManager
    participant JNI as Native Layer
    
    UI->>BL: Start Recording
    BL->>Audio: Create AudioRecord
    Audio->>BL: Audio Samples
    BL->>Extractor: Create Stream
    BL->>Extractor: Accept Waveform
    Extractor->>JNI: Process Audio
    JNI->>Extractor: Feature Vector
    Extractor->>Manager: Search Speaker
    Manager->>JNI: Compare Embeddings
    JNI->>Manager: Match Result
    Manager->>BL: Speaker Name
    BL->>UI: Update Recognition Result
```

### 6. 说话人注册流程架构

```mermaid
sequenceDiagram
    participant UI as RegisterScreen
    participant BL as Business Logic
    participant Audio as AudioRecord
    participant Extractor as SpeakerEmbeddingExtractor
    participant Manager as SpeakerEmbeddingManager
    participant Storage as Data Storage
    
    UI->>BL: Start Registration
    BL->>Audio: Record Multiple Samples
    loop For Each Sample
        Audio->>BL: Audio Data
        BL->>Extractor: Extract Features
        Extractor->>BL: Feature Vector
        BL->>BL: Collect Embeddings
    end
    UI->>BL: Submit Registration
    BL->>Manager: Add Speaker
    Manager->>Storage: Store Speaker Data
    Storage->>Manager: Confirmation
    Manager->>UI: Registration Success
```

## 技术架构模式

### 7. MVVM架构模式

```mermaid
graph TD
    subgraph "View Layer"
        A[Compose Screens]
        B[UI Components]
        C[User Interactions]
    end
    
    subgraph "ViewModel Layer"
        D[State Management]
        E[UI Logic]
        F[Event Handling]
    end
    
    subgraph "Model Layer"
        G[SpeakerRecognition]
        H[Audio Processing]
        I[Data Management]
    end
    
    A --> D
    B --> E
    C --> F
    
    D --> G
    E --> H
    F --> I
    
    G --> D
    H --> E
    I --> F
```

### 8. 依赖注入架构

```mermaid
graph TD
    subgraph "Dependency Injection"
        A[Application Context]
        B[Asset Manager]
        C[Audio Permissions]
        
        subgraph "Singleton Objects"
            D[SpeakerRecognition]
            E[SpeakerEmbeddingExtractor]
            F[SpeakerEmbeddingManager]
        end
        
        subgraph "Factory Pattern"
            G[Stream Factory]
            H[Config Factory]
            I[Audio Factory]
        end
    end
    
    A --> D
    B --> E
    C --> I
    
    D --> E
    D --> F
    
    E --> G
    E --> H
    I --> G
```

## 性能优化架构

### 9. 内存管理架构

```mermaid
graph TD
    subgraph "Memory Management"
        A[Object Lifecycle]
        
        subgraph "Native Memory"
            B[JNI Pointers]
            C[ONNX Model Memory]
            D[Audio Buffers]
        end
        
        subgraph "Java Memory"
            E[Compose State]
            F[Collection Objects]
            G[Temporary Variables]
        end
        
        subgraph "Cleanup Strategies"
            H[finalize Methods]
            I[release Methods]
            J[Garbage Collection]
        end
    end
    
    A --> B
    A --> E
    
    B --> C
    B --> D
    
    E --> F
    E --> G
    
    C --> H
    D --> I
    F --> J
```

### 10. 线程架构

```mermaid
graph TD
    subgraph "Threading Architecture"
        A[Main UI Thread]
        
        subgraph "Background Threads"
            B[Audio Recording Thread]
            C[Audio Processing Thread]
            D[Model Inference Thread]
        end
        
        subgraph "Thread Communication"
            E[State Updates]
            F[Callback Mechanisms]
            G[Coroutines]
        end
        
        subgraph "Synchronization"
            H[Thread Safety]
            I[Atomic Operations]
            J[Synchronized Blocks]
        end
    end
    
    A --> E
    B --> F
    C --> G
    D --> F
    
    E --> H
    F --> I
    G --> J
```

## 安全架构

### 11. 权限和安全架构

```mermaid
graph TD
    subgraph "Security Architecture"
        A[Permission Management]
        
        subgraph "Runtime Permissions"
            B[RECORD_AUDIO]
            C[Permission Checks]
            D[Permission Requests]
        end
        
        subgraph "Data Security"
            E[Local Data Storage]
            F[Memory Protection]
            G[Audio Data Handling]
        end
        
        subgraph "Error Handling"
            H[Permission Denied]
            I[Audio Access Errors]
            J[Model Loading Errors]
        end
    end
    
    A --> B
    B --> C
    C --> D
    
    A --> E
    E --> F
    F --> G
    
    C --> H
    G --> I
    E --> J
```

## 扩展性架构

### 12. 模块化扩展架构

```mermaid
graph TD
    subgraph "Extensibility Architecture"
        A[Core Framework]
        
        subgraph "Plugin Architecture"
            B[Model Plugins]
            C[Audio Plugins]
            D[UI Plugins]
        end
        
        subgraph "Configuration System"
            E[Dynamic Configuration]
            F[Feature Flags]
            G[Runtime Parameters]
        end
        
        subgraph "API Interfaces"
            H[Speaker Recognition API]
            I[Audio Processing API]
            J[UI Component API]
        end
    end
    
    A --> B
    A --> C
    A --> D
    
    A --> E
    E --> F
    F --> G
    
    B --> H
    C --> I
    D --> J
```

## 总结

该应用采用了现代化的 Android 架构设计原则：

1. **分层架构**: 清晰的职责分离，便于维护和测试
2. **组件化设计**: 模块化的组件，提高代码复用性
3. **响应式编程**: 基于 Compose 的声明式 UI
4. **性能优化**: 合理的内存管理和线程架构
5. **安全考虑**: 完善的权限管理和错误处理
6. **扩展性**: 支持未来功能扩展的架构设计

这种架构设计确保了应用的稳定性、性能和可维护性，同时为未来的功能扩展提供了良好的基础。