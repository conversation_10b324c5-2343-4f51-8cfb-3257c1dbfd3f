# SherpaOnnx Speaker Identification Android App

## 项目概述

SherpaOnnx Speaker Identification 是一个基于 Android 平台的说话人识别应用，使用 Sherpa-ONNX 框架实现实时语音识别和说话人身份验证功能。该应用采用现代化的 Jetpack Compose UI 框架构建，提供直观的用户界面和流畅的用户体验。

## 主要功能模块

### 1. 核心功能模块

#### 1.1 说话人识别引擎 (Speaker Recognition Engine)
- **SpeakerEmbeddingExtractor**: 语音特征提取器，负责从音频中提取说话人特征向量
- **SpeakerEmbeddingManager**: 说话人管理器，负责存储、搜索和验证说话人身份
- **OnlineStream**: 在线音频流处理，实时处理音频数据

#### 1.2 音频处理模块 (Audio Processing)
- 实时音频录制和处理
- 16kHz 采样率的 PCM 音频格式支持
- 音频权限管理和错误处理

#### 1.3 用户界面模块 (UI Modules)
- **MainActivity**: 主活动，应用入口点
- **Navigation System**: 基于 Jetpack Navigation 的导航系统
- **Bottom Navigation**: 底部导航栏，提供四个主要功能页面

### 2. 功能页面模块

#### 2.1 主页 (Home Screen)
- 实时说话人识别功能
- 可调节识别阈值
- 显示识别结果
- 录音控制按钮

#### 2.2 注册页面 (Register Screen)
- 新说话人注册功能
- 多次录音采样以提高识别准确性
- 说话人姓名输入
- 语音样本收集和处理

#### 2.3 查看页面 (View Screen)
- 显示已注册的所有说话人
- 批量删除说话人功能
- 说话人列表管理

#### 2.4 帮助页面 (Help Screen)
- 项目相关链接和文档
- 开源信息展示

### 3. 配置和数据模块

#### 3.1 模型配置
- **SpeakerEmbeddingExtractorConfig**: 模型配置参数
- 使用 3dspeaker_speech_eres2net_base_sv_zh-cn 模型
- CPU 推理提供商配置

#### 3.2 导航配置
- **NavRoutes**: 路由定义
- **NavBarItems**: 导航栏项目配置
- **BarItem**: 导航项数据类

## 技术架构

### 技术栈
- **开发语言**: Kotlin
- **UI 框架**: Jetpack Compose
- **导航**: Navigation Compose
- **音频处理**: Android AudioRecord API
- **机器学习**: Sherpa-ONNX (ONNX Runtime)
- **JNI**: 原生库集成

### 架构模式
- **MVVM**: Model-View-ViewModel 架构模式
- **组件化**: 模块化的 Compose 组件设计
- **单一活动**: Single Activity + Multiple Composables

## 依赖关系

### 主要依赖
- androidx.core:core-ktx
- androidx.lifecycle:lifecycle-runtime-ktx
- androidx.activity:activity-compose
- androidx.compose.ui (BOM)
- androidx.compose.material3
- androidx.navigation:navigation-compose

### 原生库
- sherpa-onnx-jni: 核心语音处理库

## 权限要求
- **RECORD_AUDIO**: 录音权限，用于实时音频采集

## 模型要求
- 需要下载 3dspeaker_speech_eres2net_base_sv_zh-cn_3dspeaker_16k.onnx 模型文件
- 模型文件需放置在 assets 目录下
- 支持中文语音识别

## 构建配置
- **最低 SDK**: 21 (Android 5.0)
- **目标 SDK**: 34 (Android 14)
- **编译 SDK**: 34
- **Java 版本**: 1.8
- **Kotlin 编译器**: 1.9.0