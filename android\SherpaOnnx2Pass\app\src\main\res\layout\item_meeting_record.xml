<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardBackgroundColor="@color/apple_system_background"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <TextView
                android:id="@+id/tv_meeting_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="会议标题"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Title"
                android:maxLines="2"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/tv_meeting_datetime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="12/25 14:30"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                android:textColor="@color/apple_secondary_label"
                android:layout_marginStart="12dp" />

        </LinearLayout>

        <!-- Content Preview -->
        <TextView
            android:id="@+id/tv_meeting_preview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="会议内容预览..."
            android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
            android:textColor="@color/apple_secondary_label"
            android:maxLines="2"
            android:ellipsize="end"
            android:lineSpacingExtra="2dp"
            android:layout_marginBottom="16dp" />

        <!-- Stats Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Duration -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/stat_background"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="⏱️"
                    android:textSize="12sp"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:id="@+id/tv_meeting_duration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="05:30"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                    android:textColor="@color/apple_blue" />

            </LinearLayout>

            <!-- Word Count -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/stat_background"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:layout_marginStart="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📝"
                    android:textSize="12sp"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:id="@+id/tv_meeting_word_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="1250 字"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                    android:textColor="@color/apple_green" />

            </LinearLayout>

            <!-- Speaker Count -->
            <LinearLayout
                android:id="@+id/ll_speaker_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/stat_background"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:layout_marginStart="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="👥"
                    android:textSize="12sp"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:id="@+id/tv_meeting_speaker_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3 人"
                    android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                    android:textColor="@color/apple_orange" />

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <!-- Content Types -->
            <TextView
                android:id="@+id/tv_content_types"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="📄 原始 • ✨ 优化 • 🤖 总结"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
                android:textColor="@color/apple_blue"
                android:textSize="11sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
