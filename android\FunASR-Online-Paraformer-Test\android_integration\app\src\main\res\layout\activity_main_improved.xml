<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_blue"
            app:title="@string/app_name"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 服务器配置卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/background_light">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/server_config"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:hint="@string/server_address">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_server_host"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="*************"
                                android:inputType="text" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_width="100dp"
                            android:layout_height="wrap_content"
                            android:hint="@string/server_port">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/et_server_port"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="8000"
                                android:inputType="number" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_connect"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/connect_server"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:padding="12dp"
                        app:backgroundTint="@color/primary_blue" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 状态显示卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/background_light">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/status"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_status"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/not_connected"
                        android:textSize="16sp"
                        android:textColor="@color/text_secondary"
                        android:padding="8dp"
                        android:background="@drawable/status_background" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 录音控制 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_record"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="@string/start_recording"
                android:textSize="16sp"
                android:textStyle="bold"
                android:padding="12dp"
                android:enabled="false"
                app:backgroundTint="@color/accent_green"
                app:icon="@drawable/ic_mic"
                app:iconGravity="textStart" />

            <!-- 识别结果卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:minHeight="200dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/recognition_results"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:padding="16dp"
                        android:paddingBottom="8dp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider" />

                    <androidx.core.widget.NestedScrollView
                        android:id="@+id/scroll_results"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1">

                        <TextView
                            android:id="@+id/tv_results"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/waiting_results"
                            android:textSize="14sp"
                            android:textColor="@color/text_primary"
                            android:lineSpacingExtra="2dp"
                            android:padding="16dp"
                            android:textIsSelectable="true" />

                    </androidx.core.widget.NestedScrollView>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- 浮动操作按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_clear"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        app:srcCompat="@drawable/ic_clear"
        app:backgroundTint="@color/accent_orange" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
