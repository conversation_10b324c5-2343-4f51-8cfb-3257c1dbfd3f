# SherpaOnnxKws 代码执行流程分析

## 应用启动流程

### 1. 应用初始化流程

```mermaid
flowchart TD
    A[应用启动] --> B[MainActivity.onCreate]
    B --> C[请求录音权限]
    C --> D[初始化模型 initModel]
    D --> E[创建KeywordSpotter实例]
    E --> F[创建OnlineStream实例]
    F --> G[初始化UI组件]
    G --> H[设置按钮监听器]
    H --> I[应用就绪]
```

### 2. 模型初始化详细流程

```mermaid
flowchart TD
    A[initModel开始] --> B[获取特征配置 getFeatureConfig]
    B --> C[获取模型配置 getKwsModelConfig]
    C --> D[获取关键词文件 getKeywordsFile]
    D --> E[创建KeywordSpotterConfig]
    E --> F[实例化KeywordSpotter]
    F --> G[加载JNI库 sherpa-onnx-jni]
    G --> H[创建默认音频流]
    H --> I[模型初始化完成]
```

## 关键词检测主流程

### 3. 录音和检测流程

```mermaid
flowchart TD
    A[用户点击开始按钮] --> B[onclick方法]
    B --> C{是否正在录音?}
    C -->|否| D[获取用户输入关键词]
    D --> E[创建新的音频流]
    E --> F[初始化麦克风 initMicrophone]
    F --> G[开始录音 AudioRecord.startRecording]
    G --> H[启动录音线程 processSamples]
    H --> I[更新UI状态]
    
    C -->|是| J[停止录音]
    J --> K[释放AudioRecord资源]
    K --> L[释放音频流]
    L --> M[更新UI状态]
```

### 4. 音频处理和检测循环

```mermaid
flowchart TD
    A[processSamples线程开始] --> B[创建音频缓冲区]
    B --> C[录音循环开始]
    C --> D[读取音频数据 AudioRecord.read]
    D --> E[转换为浮点数组]
    E --> F[发送到音频流 acceptWaveform]
    F --> G{检测器是否就绪?}
    G -->|是| H[执行解码 decode]
    H --> I[获取检测结果 getResult]
    I --> J{检测到关键词?}
    J -->|是| K[重置检测器状态]
    K --> L[更新显示文本]
    L --> M[UI线程更新界面]
    J -->|否| N[继续检测]
    G -->|否| N
    M --> N
    N --> O{是否继续录音?}
    O -->|是| C
    O -->|否| P[线程结束]
```

## 核心组件交互流程

### 5. KeywordSpotter与OnlineStream交互

```mermaid
sequenceDiagram
    participant UI as MainActivity
    participant KWS as KeywordSpotter
    participant Stream as OnlineStream
    participant Audio as AudioRecord
    
    UI->>KWS: 创建实例
    KWS->>Stream: createStream(keywords)
    Stream-->>KWS: 返回流对象
    
    UI->>Audio: 开始录音
    loop 音频处理循环
        Audio->>UI: 音频数据
        UI->>Stream: acceptWaveform(samples)
        UI->>KWS: isReady(stream)
        KWS-->>UI: true/false
        alt 检测器就绪
            UI->>KWS: decode(stream)
            UI->>KWS: getResult(stream)
            KWS-->>UI: KeywordSpotterResult
            alt 检测到关键词
                UI->>KWS: reset(stream)
                UI->>UI: 更新界面显示
            end
        end
    end
```

### 6. 权限管理流程

```mermaid
flowchart TD
    A[应用启动] --> B[检查录音权限]
    B --> C{权限已授予?}
    C -->|是| D[继续初始化]
    C -->|否| E[请求权限]
    E --> F[用户响应]
    F --> G{权限被授予?}
    G -->|是| H[权限回调成功]
    G -->|否| I[权限回调失败]
    H --> D
    I --> J[显示错误并退出]
```

## 数据流转换过程

### 7. 音频数据处理流程

```mermaid
flowchart LR
    A[麦克风输入] --> B[AudioRecord]
    B --> C[16位PCM数据]
    C --> D[ShortArray缓冲区]
    D --> E[转换为FloatArray]
    E --> F[归一化 /32768.0f]
    F --> G[OnlineStream.acceptWaveform]
    G --> H[特征提取]
    H --> I[模型推理]
    I --> J[关键词检测结果]
```

### 8. 结果处理和显示流程

```mermaid
flowchart TD
    A[获取检测结果] --> B[KeywordSpotterResult]
    B --> C[提取关键词字符串]
    C --> D{关键词非空?}
    D -->|是| E[格式化显示文本]
    E --> F[添加序号和时间戳]
    F --> G[更新历史记录]
    G --> H[runOnUiThread更新UI]
    D -->|否| I[保持当前显示]
    H --> J[用户看到检测结果]
    I --> J
```

## 错误处理和异常流程

### 9. 异常处理机制

```mermaid
flowchart TD
    A[操作开始] --> B{权限检查}
    B -->|失败| C[显示权限错误]
    B -->|成功| D{模型初始化}
    D -->|失败| E[显示模型加载错误]
    D -->|成功| F{音频设备初始化}
    F -->|失败| G[显示音频设备错误]
    F -->|成功| H{关键词流创建}
    H -->|失败| I[显示关键词设置错误]
    H -->|成功| J[正常执行]
    
    C --> K[应用退出或重试]
    E --> K
    G --> K
    I --> L[回退到默认设置]
```

## 资源管理流程

### 10. 内存和资源释放

```mermaid
flowchart TD
    A[应用生命周期事件] --> B{停止录音?}
    B -->|是| C[停止AudioRecord]
    C --> D[释放AudioRecord资源]
    D --> E[停止录音线程]
    E --> F[释放OnlineStream]
    F --> G[调用JNI释放native资源]
    
    B -->|否| H{应用销毁?}
    H -->|是| I[释放KeywordSpotter]
    I --> J[释放所有JNI资源]
    J --> K[垃圾回收]
    
    H -->|否| L[继续运行]
```

## 配置和参数流程

### 11. 配置参数设置流程

```mermaid
flowchart TD
    A[应用配置] --> B[FeatureConfig]
    B --> C[sampleRate: 16000Hz]
    C --> D[featureDim: 80]
    D --> E[KeywordSpotterConfig]
    E --> F[maxActivePaths: 4]
    F --> G[keywordsThreshold: 0.25]
    G --> H[numTrailingBlanks: 2]
    H --> I[OnlineModelConfig]
    I --> J[模型文件路径]
    J --> K[线程数配置]
    K --> L[配置完成]
```

这个流程文档详细描述了SherpaOnnxKws应用的完整执行流程，包括初始化、录音检测、结果处理、错误处理和资源管理等各个方面，帮助开发者理解代码的执行逻辑和数据流转过程。