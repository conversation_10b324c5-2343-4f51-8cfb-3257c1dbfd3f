# SherpaOnnx VAD ASR Android 项目文档

## 项目概述

SherpaOnnx VAD ASR 是一个基于 Android 平台的语音活动检测（Voice Activity Detection, VAD）和自动语音识别（Automatic Speech Recognition, ASR）应用。该项目集成了 Sherpa-ONNX 库，提供实时语音检测和离线语音识别功能。

## 主要功能模块

### 1. 核心模块

#### 1.1 MainActivity
- **功能**: 应用主界面和核心控制逻辑
- **职责**: 
  - 管理录音权限
  - 控制录音开始/停止
  - 协调 VAD 和 ASR 模块
  - 显示识别结果

#### 1.2 VAD (Voice Activity Detection) 模块
- **功能**: 语音活动检测
- **实现**: `Vad.kt`
- **职责**:
  - 检测音频流中的语音片段
  - 分离语音和静音部分
  - 使用 Silero VAD 模型

#### 1.3 ASR (Automatic Speech Recognition) 模块
- **功能**: 离线语音识别
- **实现**: `OfflineRecognizer.kt`
- **职责**:
  - 将语音片段转换为文本
  - 支持多种模型类型（Transducer, Paraformer, Whisper 等）
  - 提供离线识别能力

#### 1.4 音频处理模块
- **功能**: 音频数据采集和处理
- **实现**: MainActivity 中的音频相关方法
- **职责**:
  - 初始化麦克风
  - 实时音频数据采集
  - 音频格式转换

### 2. 配置模块

#### 2.1 FeatureConfig
- **功能**: 特征提取配置
- **参数**: 采样率、特征维度、抖动参数

#### 2.2 VadModelConfig
- **功能**: VAD 模型配置
- **参数**: 阈值、静音/语音持续时间、窗口大小等

#### 2.3 OfflineRecognizerConfig
- **功能**: ASR 模型配置
- **参数**: 模型路径、解码方法、热词等

### 3. 数据流模块

#### 3.1 OfflineStream
- **功能**: 音频流处理
- **职责**: 接收音频数据并传递给识别器

#### 3.2 SpeechSegment
- **功能**: 语音片段数据结构
- **内容**: 起始位置和音频样本数据

## 代码执行流程

```mermaid
flowchart TD
    A[应用启动] --> B[MainActivity.onCreate]
    B --> C[请求录音权限]
    C --> D[初始化VAD模型]
    D --> E[初始化离线识别器]
    E --> F[设置UI界面]
    F --> G[等待用户操作]
    
    G --> H{用户点击录音按钮}
    H -->|开始录音| I[初始化麦克风]
    I --> J[启动AudioRecord]
    J --> K[创建录音线程]
    K --> L[开始音频采集循环]
    
    L --> M[读取音频数据]
    M --> N[转换为Float数组]
    N --> O[VAD处理音频]
    O --> P{VAD检测到语音片段?}
    
    P -->|是| Q[获取语音片段]
    Q --> R[创建OfflineStream]
    R --> S[ASR识别处理]
    S --> T[获取识别结果]
    T --> U[更新UI显示]
    U --> V[释放Stream资源]
    
    P -->|否| W[继续监听]
    V --> W
    W --> X{继续录音?}
    X -->|是| M
    
    H -->|停止录音| Y[停止AudioRecord]
    Y --> Z[释放音频资源]
    Z --> AA[重置VAD状态]
    AA --> G
    
    X -->|否| Y
```

## 详细流程说明

### 1. 应用初始化流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant MA as MainActivity
    participant VAD as VAD模块
    participant ASR as OfflineRecognizer
    participant Audio as 音频系统
    
    User->>MA: 启动应用
    MA->>MA: onCreate()
    MA->>Audio: 请求录音权限
    Audio-->>MA: 权限结果
    MA->>VAD: 初始化VAD模型
    VAD-->>MA: 初始化完成
    MA->>ASR: 初始化离线识别器
    ASR-->>MA: 初始化完成
    MA->>User: 显示界面
```

### 2. 录音和识别流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant MA as MainActivity
    participant VAD as VAD模块
    participant ASR as OfflineRecognizer
    participant Audio as AudioRecord
    
    User->>MA: 点击开始录音
    MA->>Audio: 初始化麦克风
    MA->>Audio: 开始录音
    MA->>MA: 启动录音线程
    
    loop 录音循环
        Audio->>MA: 音频数据
        MA->>MA: 转换为Float数组
        MA->>VAD: acceptWaveform(samples)
        VAD->>VAD: 检测语音活动
        
        alt 检测到语音片段
            VAD-->>MA: 语音片段
            MA->>ASR: 创建Stream
            MA->>ASR: 识别语音片段
            ASR-->>MA: 识别结果文本
            MA->>MA: 更新UI显示
            MA->>ASR: 释放Stream
        end
    end
    
    User->>MA: 点击停止录音
    MA->>Audio: 停止录音
    MA->>Audio: 释放资源
    MA->>VAD: 重置状态
```

### 3. VAD 处理流程

```mermaid
flowchart TD
    A[接收音频数据] --> B[VAD.acceptWaveform]
    B --> C[Silero模型处理]
    C --> D{检测到语音?}
    D -->|是| E[累积语音片段]
    D -->|否| F[处理静音]
    E --> G{语音片段完整?}
    G -->|是| H[生成SpeechSegment]
    G -->|否| I[继续累积]
    H --> J[加入队列]
    F --> K[检查静音持续时间]
    K --> L{超过阈值?}
    L -->|是| M[结束当前语音片段]
    L -->|否| N[继续监听]
    M --> H
    I --> O[等待更多数据]
    J --> P[可供ASR处理]
    N --> O
    O --> A
```

## 技术特点

### 1. 实时性能
- 使用独立线程处理音频数据
- VAD 实时检测语音活动
- 协程异步处理 ASR 识别

### 2. 离线能力
- 完全离线的语音识别
- 本地模型文件存储
- 无需网络连接

### 3. 模块化设计
- VAD 和 ASR 模块独立
- 配置参数可调
- 支持多种模型类型

### 4. 资源管理
- 自动释放音频资源
- Stream 对象生命周期管理
- 内存优化处理

## 依赖和配置

### 1. 主要依赖
- Android SDK (API 21+)
- Kotlin
- Sherpa-ONNX JNI 库
- AndroidX 组件

### 2. 权限要求
- `RECORD_AUDIO`: 录音权限

### 3. 模型文件
- VAD 模型: Silero VAD ONNX 模型
- ASR 模型: 支持多种 ONNX 格式模型

### 4. 音频配置
- 采样率: 16kHz
- 格式: 16-bit PCM
- 声道: 单声道
- 缓冲区: 512 样本

## 使用说明

1. **启动应用**: 自动请求录音权限
2. **开始录音**: 点击"开始"按钮
3. **语音识别**: 说话时自动检测和识别
4. **查看结果**: 识别结果实时显示在界面上
5. **停止录音**: 点击"停止"按钮结束录音

## 扩展性

该项目具有良好的扩展性，可以：
- 添加新的 ASR 模型支持
- 集成不同的 VAD 算法
- 扩展音频预处理功能
- 添加后处理和文本优化
- 支持多语言识别