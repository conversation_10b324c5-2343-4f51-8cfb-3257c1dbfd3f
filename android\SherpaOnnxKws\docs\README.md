# SherpaOnnxKws - Android关键词检测应用

## 项目概述

SherpaOnnxKws是一个基于Android平台的关键词检测(Keyword Spotting)应用，使用Sherpa-ONNX框架实现实时语音关键词识别功能。该应用能够实时监听麦克风输入，检测用户预设的关键词，并在检测到关键词时进行提示。

## 主要功能模块

### 1. 核心模块

#### 1.1 KeywordSpotter (关键词检测器)
- **文件**: `KeywordSpotter.kt`
- **功能**: 核心的关键词检测引擎
- **主要方法**:
  - `createStream()`: 创建音频流处理对象
  - `decode()`: 解码音频数据
  - `isReady()`: 检查是否准备好处理
  - `getResult()`: 获取检测结果
  - `reset()`: 重置检测状态

#### 1.2 OnlineStream (在线音频流)
- **文件**: `OnlineStream.kt`
- **功能**: 处理实时音频流数据
- **主要方法**:
  - `acceptWaveform()`: 接收音频波形数据
  - `inputFinished()`: 标记输入结束

#### 1.3 MainActivity (主活动)
- **文件**: `MainActivity.kt`
- **功能**: 应用主界面和业务逻辑控制
- **主要功能**:
  - 权限管理(录音权限)
  - 音频录制控制
  - 关键词设置和管理
  - 实时检测结果显示

### 2. 配置模块

#### 2.1 FeatureConfig (特征配置)
- **文件**: `FeatureConfig.kt`
- **功能**: 音频特征提取配置
- **参数**:
  - `sampleRate`: 采样率(默认16000Hz)
  - `featureDim`: 特征维度(默认80)
  - `dither`: 抖动参数

#### 2.2 OnlineRecognizer (在线识别器)
- **文件**: `OnlineRecognizer.kt`
- **功能**: 在线语音识别相关配置和实现
- **包含多种模型配置**:
  - Transducer模型配置
  - Paraformer模型配置
  - Zipformer2 CTC模型配置
  - NeMo CTC模型配置

### 3. 用户界面模块

#### 3.1 布局文件
- **文件**: `activity_main.xml`
- **组件**:
  - `EditText`: 关键词输入框
  - `TextView`: 检测结果显示区域
  - `Button`: 开始/停止录制按钮

#### 3.2 资源文件
- **字符串资源**: `strings.xml`
- **应用图标**: 各种分辨率的启动图标
- **主题样式**: Material Design主题

## 技术架构

### 开发环境
- **语言**: Kotlin
- **最小SDK**: API 21 (Android 5.0)
- **目标SDK**: API 34 (Android 14)
- **构建工具**: Gradle 7.3.1
- **框架**: Android Jetpack, Material Design

### 核心依赖
- **Sherpa-ONNX**: 语音识别核心引擎(通过JNI调用)
- **AndroidX**: 现代Android开发库
- **Material Components**: UI组件库

### 权限要求
- `RECORD_AUDIO`: 录音权限(必需)

## 项目特点

1. **实时性**: 支持实时音频流处理和关键词检测
2. **可配置**: 用户可以自定义关键词列表
3. **多语言支持**: 支持中文关键词检测
4. **高性能**: 基于ONNX优化的模型推理
5. **跨平台**: 基于Sherpa-ONNX框架，支持多平台部署

## 使用场景

- 智能家居语音控制
- 语音助手唤醒词检测
- 会议关键词监控
- 语音交互应用
- 教育培训语音评测