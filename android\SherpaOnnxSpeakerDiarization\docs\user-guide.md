# 用户使用指南

## 应用简介

SherpaOnnx Speaker Diarization 是一款基于人工智能的说话人分离应用，能够自动识别音频文件中的不同说话人，并标记出每个说话人的发言时间段。

## 功能特点

- ✅ **离线处理**: 无需网络连接，保护隐私
- ✅ **高精度识别**: 基于先进的深度学习模型
- ✅ **实时进度**: 显示处理进度和状态
- ✅ **结果导出**: 支持复制结果到剪贴板
- ✅ **多说话人支持**: 自动检测或手动指定说话人数量

## 系统要求

### 硬件要求
- **最低内存**: 4GB RAM
- **推荐内存**: 6GB RAM 或更高
- **存储空间**: 至少 100MB 可用空间
- **处理器**: ARM64 或 x86_64 架构

### 软件要求
- **Android 版本**: Android 5.0 (API 21) 或更高
- **目标版本**: Android 14 (API 34)

### 音频文件要求
- **格式**: WAV 文件
- **采样率**: 16kHz
- **位深**: 16-bit
- **声道**: 单声道 (Mono)
- **编码**: PCM

## 安装指南

### 1. 下载应用
从 GitHub Releases 页面下载最新版本的 APK 文件。

### 2. 安装应用
1. 在 Android 设备上启用"未知来源"安装
2. 点击下载的 APK 文件进行安装
3. 按照提示完成安装过程

### 3. 准备模型文件
应用需要两个模型文件才能正常工作：

#### 下载模型文件
1. **分割模型** (`segmentation.onnx`):
   - 下载地址: [sherpa-onnx-pyannote-segmentation-3-0.tar.bz2](https://github.com/k2-fsa/sherpa-onnx/releases/download/speaker-segmentation-models/sherpa-onnx-pyannote-segmentation-3-0.tar.bz2)
   - 解压后将 `model.onnx` 重命名为 `segmentation.onnx`

2. **嵌入模型** (`embedding.onnx`):
   - 下载地址: [3dspeaker_speech_eres2net_base_sv_zh-cn_3dspeaker_16k.onnx](https://github.com/k2-fsa/sherpa-onnx/releases/download/speaker-recongition-models/3dspeaker_speech_eres2net_base_sv_zh-cn_3dspeaker_16k.onnx)
   - 重命名为 `embedding.onnx`

#### 放置模型文件
将两个模型文件放置在应用的 `assets` 目录中（开发者需要在编译前完成）。

## 使用教程

### 界面介绍

```mermaid
flowchart TD
    A["顶部标题栏<br/>Next-gen Kaldi: Speaker Diarization"] --> B[主要内容区域]
    B --> C["文件选择按钮<br/>选择音频文件"]
    B --> D["参数设置区域<br/>说话人数量和阈值"]
    B --> E["处理按钮<br/>开始分析"]
    B --> F["进度显示<br/>处理状态"]
    B --> G["结果显示<br/>分离结果"]
    G --> H["底部导航栏<br/>主页 | 帮助"]
```

### 基本使用流程

#### 步骤 1: 选择音频文件
1. 点击"选择文件"按钮
2. 从文件管理器中选择符合要求的 WAV 音频文件
3. 应用会自动验证文件格式和采样率

#### 步骤 2: 设置参数

##### 说话人数量设置
- **已知说话人数量**: 如果您知道音频中有几个说话人，请输入具体数字
- **未知说话人数量**: 输入 `0` 或 `-1`，让系统自动检测

##### 聚类阈值设置
- **范围**: 0.0 - 1.0
- **默认值**: 0.5
- **调整原则**:
  - 阈值越高 → 识别的说话人越少（更保守）
  - 阈值越低 → 识别的说话人越多（更敏感）

#### 步骤 3: 开始处理
1. 点击"开始分析"按钮
2. 观察进度条显示处理进度
3. 等待处理完成

#### 步骤 4: 查看结果
处理完成后，结果会显示为时间段列表：
```
说话人 0: 0.00s - 2.50s
说话人 1: 2.50s - 5.20s
说话人 0: 5.20s - 8.10s
...
```

#### 步骤 5: 导出结果
点击"复制结果"按钮将结果复制到剪贴板，可以粘贴到其他应用中使用。

### 高级使用技巧

#### 1. 参数优化

##### 根据音频特点调整阈值

| 音频特点 | 推荐阈值 | 说明 |
|---------|---------|------|
| 声音相似的说话人 | 0.3-0.4 | 更敏感，避免混淆 |
| 声音差异明显 | 0.5-0.6 | 标准设置 |
| 噪音较多 | 0.6-0.7 | 更保守，减少误判 |
| 短对话片段 | 0.4-0.5 | 平衡敏感度 |
| 长时间独白 | 0.5-0.6 | 避免过度分割 |

##### 说话人数量设置策略

```mermaid
flowchart TD
    A["确定说话人数量"] --> B{"是否知道确切人数？"}
    B -->|是| C["输入确切数字<br/>(如: 2, 3, 4)"]
    B -->|否| D["设置为 0 或 -1"]
    D --> E["调整阈值参数"]
    E --> F["开始处理"]
    F --> G["检查结果"]
    G --> H{"结果满意？"}
    H -->|是| I["完成"]
    H -->|否| J["调整阈值"]
    J --> F
    C --> F
```

#### 2. 音频预处理建议

##### 音频质量要求
- **清晰度**: 确保音频清晰，噪音较少
- **音量**: 适中音量，避免过大或过小
- **格式**: 严格按照要求的格式准备音频

##### 格式转换
如果您的音频不符合要求，可以使用以下工具进行转换：

**使用 FFmpeg 转换:**
```bash
# 转换为 16kHz 16-bit 单声道 WAV
ffmpeg -i input.mp3 -ar 16000 -ac 1 -sample_fmt s16 output.wav
```

**使用 Audacity:**
1. 打开音频文件
2. 选择"轨道" → "重新采样" → 设置为 16000 Hz
3. 选择"轨道" → "混音并渲染" → "混音为单声道"
4. 导出为 WAV 格式，选择 16-bit PCM

#### 3. 结果解读

##### 结果格式说明
```
说话人 ID: 开始时间 - 结束时间
```

- **说话人 ID**: 从 0 开始的数字标识符
- **时间格式**: 秒为单位，保留两位小数
- **时间段**: 该说话人在此时间段内发言

##### 结果分析技巧

1. **检查连续性**: 同一说话人的相邻片段可能表示短暂停顿
2. **验证合理性**: 检查时间段是否符合实际对话情况
3. **交叉验证**: 可以播放对应时间段验证识别准确性

#### 4. 常见问题处理

##### 识别结果不准确

**问题**: 说话人识别错误或混淆

**解决方案**:
1. 调整聚类阈值
2. 检查音频质量
3. 确认说话人数量设置
4. 尝试不同的参数组合

**参数调整流程**:
```mermaid
flowchart TD
    A["识别结果不满意"] --> B{"问题类型"}
    B -->|"说话人过多"| C["增加阈值<br/>(+0.1)"]
    B -->|"说话人过少"| D["减少阈值<br/>(-0.1)"]
    B -->|"时间段不准"| E["调整最小持续时间"]
    C --> F["重新处理"]
    D --> F
    E --> F
    F --> G{"结果改善？"}
    G -->|是| H["完成"]
    G -->|否| I["继续调整"]
    I --> B
```

##### 处理速度慢

**问题**: 音频处理时间过长

**解决方案**:
1. 关闭其他应用释放内存
2. 确保设备有足够存储空间
3. 考虑分段处理长音频
4. 降低音频质量（如果可接受）

##### 应用崩溃

**问题**: 处理过程中应用意外退出

**解决方案**:
1. 检查音频文件大小（建议小于 100MB）
2. 重启应用重试
3. 检查设备内存是否充足
4. 确认音频格式完全符合要求

### 最佳实践

#### 1. 音频准备
- 使用高质量录音设备
- 确保环境安静，减少背景噪音
- 说话人之间保持适当距离
- 避免同时说话的情况

#### 2. 参数设置
- 首次使用建议使用默认参数
- 根据结果逐步调整参数
- 记录有效的参数组合供后续使用

#### 3. 结果验证
- 抽样检查识别结果的准确性
- 对比实际音频内容验证时间段
- 必要时进行人工校正

#### 4. 数据管理
- 及时保存重要的分析结果
- 定期清理临时文件
- 备份重要的音频文件

## 技术支持

### 获取帮助
- **应用内帮助**: 点击底部导航栏的"帮助"按钮
- **GitHub 项目**: [sherpa-onnx](https://github.com/k2-fsa/sherpa-onnx)
- **技术文档**: 查看项目 docs 目录

### 反馈问题
如果遇到问题或有改进建议，请：
1. 在 GitHub 项目页面提交 Issue
2. 提供详细的问题描述和复现步骤
3. 包含设备信息和音频文件特征

### 贡献代码
欢迎开发者贡献代码：
1. Fork 项目仓库
2. 创建功能分支
3. 提交 Pull Request
4. 参与代码审查

## 版本更新

### 当前版本: 1.0
- 初始版本发布
- 支持基本的说话人分离功能
- Material Design 3 界面
- 离线处理能力

### 计划功能
- [ ] 支持更多音频格式
- [ ] 实时说话人分离
- [ ] 批量处理功能
- [ ] 结果导出为多种格式
- [ ] 性能优化和内存管理改进

## 许可证

本项目采用开源许可证，详情请查看项目根目录的 LICENSE 文件。

---

**注意**: 本应用仅用于学习和研究目的，请确保您有权处理所使用的音频文件，并遵守相关的隐私和版权法律法规。