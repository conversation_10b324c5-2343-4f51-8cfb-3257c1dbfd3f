# Sherpa-ONNX Android AAR 项目文档

## 项目概述

Sherpa-ONNX Android AAR 是一个基于 ONNX 的语音处理 Android 库，提供了完整的语音识别、语音合成、语音活动检测等功能。该项目将 Sherpa-ONNX 的核心功能封装为 Android AAR 包，方便在 Android 应用中集成使用。

## 主要功能模块

### 1. 语音识别模块 (Speech Recognition)

#### 离线语音识别 (OfflineRecognizer)
- **功能**: 提供离线语音识别能力，支持多种模型架构
- **支持的模型类型**:
  - Transducer 模型
  - Paraformer 模型
  - Whisper 模型
  - FireRed ASR 模型
  - Moonshine 模型
  - NeMo EncDec CTC 模型
  - SenseVoice 模型
  - Dolphin 模型
- **主要特性**:
  - 支持多语言识别
  - 情感识别
  - 事件检测
  - 时间戳输出
  - 热词支持

#### 在线语音识别 (OnlineRecognizer)
- **功能**: 提供实时流式语音识别能力
- **支持的模型类型**:
  - Transducer 模型
  - Paraformer 模型
  - Zipformer2 CTC 模型
  - NeMo CTC 模型
- **主要特性**:
  - 实时流式处理
  - 端点检测
  - 语言模型支持
  - CTC FST 解码器
  - 热词支持

### 2. 语音合成模块 (Text-to-Speech)

#### 离线语音合成 (OfflineTts)
- **功能**: 将文本转换为语音
- **支持的模型类型**:
  - VITS 模型
  - Matcha 模型
  - Kokoro 模型
- **主要特性**:
  - 多说话人支持
  - 语速控制
  - 音频生成和保存
  - 回调函数支持

### 3. 语音活动检测模块 (Voice Activity Detection)

#### VAD (Vad)
- **功能**: 检测音频中的语音活动
- **支持的模型**: Silero VAD 模型
- **主要特性**:
  - 实时语音检测
  - 语音段分割
  - 可配置的阈值和参数
  - 静音检测

### 4. 关键词检测模块 (Keyword Spotting)

#### 关键词识别 (KeywordSpotter)
- **功能**: 检测音频中的特定关键词
- **主要特性**:
  - 自定义关键词
  - 实时检测
  - 置信度评分
  - 时间戳输出

### 5. 其他功能模块

#### 音频标记 (AudioTagging)
- **功能**: 音频内容分类和标记

#### 离线标点符号 (OfflinePunctuation)
- **功能**: 为识别结果添加标点符号

#### 说话人分离 (OfflineSpeakerDiarization)
- **功能**: 识别和分离不同说话人

#### 语音降噪 (OfflineSpeechDenoiser)
- **功能**: 音频降噪处理

#### 说话人识别 (Speaker)
- **功能**: 说话人身份识别

#### 语言识别 (SpokenLanguageIdentification)
- **功能**: 识别音频中的语言类型

#### 音频读取 (WaveReader)
- **功能**: 读取和处理音频文件

#### 特征配置 (FeatureConfig)
- **功能**: 音频特征提取配置

## 项目结构

```
SherpaOnnxAar/
├── build.gradle.kts              # 项目构建配置
├── settings.gradle.kts           # 项目设置
├── gradle.properties             # Gradle 属性
├── README.md                     # 项目说明
└── sherpa_onnx/                  # 主模块
    ├── build.gradle.kts          # 模块构建配置
    ├── src/main/
    │   ├── AndroidManifest.xml   # Android 清单文件
    │   ├── java/com/k2fsa/sherpa/onnx/  # 主要源代码
    │   │   ├── OfflineRecognizer.kt     # 离线识别器
    │   │   ├── OnlineRecognizer.kt      # 在线识别器
    │   │   ├── Tts.kt                   # 语音合成
    │   │   ├── Vad.kt                   # 语音活动检测
    │   │   ├── KeywordSpotter.kt        # 关键词检测
    │   │   ├── WaveReader.kt            # 音频读取
    │   │   ├── FeatureConfig.kt         # 特征配置
    │   │   ├── OfflineStream.kt         # 离线流处理
    │   │   ├── OnlineStream.kt          # 在线流处理
    │   │   └── ...                      # 其他功能模块
    │   └── jniLibs/                     # 原生库文件
    │       ├── arm64-v8a/
    │       ├── armeabi-v7a/
    │       ├── x86/
    │       └── x86_64/
    ├── src/test/                        # 单元测试
    └── src/androidTest/                 # Android 测试
```

## 技术特点

1. **多架构支持**: 支持 ARM64、ARMv7、x86、x86_64 等多种 CPU 架构
2. **JNI 集成**: 通过 JNI 调用底层 C++ 实现，保证性能
3. **模块化设计**: 各功能模块独立，便于按需使用
4. **配置灵活**: 提供丰富的配置选项，适应不同场景需求
5. **资源管理**: 支持从 Android Assets 和文件系统加载模型
6. **内存管理**: 提供完善的资源释放机制

## 使用场景

- **智能语音助手**: 结合语音识别、合成、关键词检测等功能
- **语音转文字应用**: 利用离线或在线识别功能
- **语音合成应用**: 文本转语音功能
- **语音质量检测**: 使用 VAD 进行语音活动检测
- **多语言应用**: 支持多种语言的识别和合成
- **实时语音处理**: 流式处理音频数据

## 构建和部署

项目使用 Gradle 构建系统，支持生成 AAR 包供其他 Android 项目使用。构建完成后会生成 `sherpa_onnx-release.aar` 文件，可以直接集成到 Android 应用中。