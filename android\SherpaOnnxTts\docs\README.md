# SherpaOnnxTts Android 项目文档

## 项目概述

SherpaOnnxTts 是一个基于 Sherpa-ONNX 的 Android 文本转语音 (TTS) 应用。该应用支持多种 TTS 模型，包括 VITS、Matcha 和 Kokoro 模型，能够将文本转换为高质量的语音输出。

## 主要功能模块

### 1. 核心 TTS 引擎模块 (`Tts.kt`)

#### 1.1 模型配置类
- **OfflineTtsVitsModelConfig**: VITS 模型配置
- **OfflineTtsMatchaModelConfig**: Matcha 模型配置  
- **OfflineTtsKokoroModelConfig**: Kokoro 模型配置
- **OfflineTtsModelConfig**: 统一模型配置容器
- **OfflineTtsConfig**: 完整的 TTS 配置

#### 1.2 音频生成类
- **GeneratedAudio**: 封装生成的音频数据和采样率
- **OfflineTts**: 核心 TTS 引擎类，提供文本转语音功能

#### 1.3 主要功能
- 支持多种 TTS 模型 (VITS/Matcha/Kokoro)
- 文本转语音生成
- 实时音频回调
- 音频文件保存
- JNI 本地库集成

### 2. 用户界面模块 (`MainActivity.kt`)

#### 2.1 UI 组件
- **文本输入框**: 用户输入待转换的文本
- **说话人ID输入**: 选择不同的说话人声音
- **语速控制**: 调整语音播放速度
- **生成按钮**: 触发文本转语音
- **播放按钮**: 播放生成的音频
- **停止按钮**: 停止当前操作

#### 2.2 音频处理
- **AudioTrack**: 实时音频播放
- **MediaPlayer**: 音频文件播放
- **音频回调机制**: 实时音频流处理

#### 2.3 资源管理
- **模型文件管理**: 从 assets 复制模型文件
- **配置文件处理**: 处理词典、规则等配置文件
- **文件系统操作**: 管理应用内部存储

### 3. 资源模块

#### 3.1 布局资源 (`activity_main.xml`)
- 响应式 ConstraintLayout 布局
- 用户友好的界面设计
- 多行文本输入支持

#### 3.2 字符串资源 (`strings.xml`)
- 国际化支持
- UI 文本集中管理

#### 3.3 模型资源 (`assets/`)
- TTS 模型文件存储
- 词典和配置文件
- JNI 本地库文件

### 4. 配置模块

#### 4.1 应用配置 (`AndroidManifest.xml`)
- 权限声明
- 应用基本信息
- Activity 配置

#### 4.2 构建配置 (`build.gradle`)
- 依赖管理
- 编译配置
- 版本控制

## 支持的 TTS 模型

### 1. VITS 模型
- **vits-melo-tts-zh_en**: 中英文混合模型
- **vits-piper-en_US-amy-low**: 英文女声模型
- **vits-icefall-zh-aishell3**: 中文多说话人模型
- **vits-zh-hf-fanchen-C**: 中文 187 说话人模型

### 2. Matcha 模型
- **matcha-icefall-zh-baker**: 中文女声模型
- **matcha-icefall-en_US-ljspeech**: 英文女声模型

### 3. Kokoro 模型
- **kokoro-en-v0_19**: 英文模型
- **kokoro-multi-lang-v1_0**: 多语言模型

## 技术特性

- **多线程处理**: 支持并发音频生成
- **实时回调**: 流式音频输出
- **内存管理**: 自动资源释放
- **错误处理**: 完善的异常处理机制
- **性能优化**: CPU 优化的推理引擎
- **跨平台**: 基于 ONNX 的模型支持

## 开发环境要求

- **Android SDK**: API Level 21+
- **Kotlin**: 1.8+
- **Gradle**: 7.0+
- **NDK**: 支持 JNI 开发
- **目标平台**: arm64-v8a, armeabi-v7a, x86, x86_64