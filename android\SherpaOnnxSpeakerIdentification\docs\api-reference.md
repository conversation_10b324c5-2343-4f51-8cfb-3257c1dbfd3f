# API 参考文档

## 核心 API 接口

### 1. SpeakerRecognition 单例对象

#### 概述
全局单例对象，负责管理说话人识别系统的初始化和核心组件访问。

#### API 接口

```kotlin
object SpeakerRecognition {
    // 属性
    val extractor: SpeakerEmbeddingExtractor
    val manager: SpeakerEmbeddingManager
    
    // 方法
    fun initExtractor(assetManager: AssetManager? = null)
}
```

#### 方法详解

##### initExtractor()
```kotlin
fun initExtractor(assetManager: AssetManager? = null)
```
**功能**: 初始化说话人特征提取器和管理器

**参数**:
- `assetManager`: Android AssetManager，用于加载模型文件

**使用示例**:
```kotlin
// 在 MainActivity.onCreate() 中调用
SpeakerRecognition.initExtractor(this.assets)
```

**注意事项**:
- 必须在使用其他功能前调用
- 线程安全，使用 synchronized 保护
- 重复调用会被忽略

---

### 2. SpeakerEmbeddingExtractor 类

#### 概述
说话人特征提取器，负责从音频中提取说话人特征向量。

#### 构造函数
```kotlin
class SpeakerEmbeddingExtractor(
    assetManager: AssetManager? = null,
    config: SpeakerEmbeddingExtractorConfig
)
```

#### API 接口

```kotlin
class SpeakerEmbeddingExtractor {
    // 流管理
    fun createStream(): OnlineStream
    
    // 特征提取
    fun isReady(stream: OnlineStream): Boolean
    fun compute(stream: OnlineStream): FloatArray
    
    // 配置信息
    fun dim(): Int
    
    // 资源管理
    fun release()
}
```

#### 方法详解

##### createStream()
```kotlin
fun createStream(): OnlineStream
```
**功能**: 创建音频流对象

**返回值**: `OnlineStream` - 音频流对象

**使用示例**:
```kotlin
val stream = SpeakerRecognition.extractor.createStream()
```

##### isReady()
```kotlin
fun isReady(stream: OnlineStream): Boolean
```
**功能**: 检查流是否准备好进行特征提取

**参数**:
- `stream`: 音频流对象

**返回值**: `Boolean` - 是否准备就绪

##### compute()
```kotlin
fun compute(stream: OnlineStream): FloatArray
```
**功能**: 计算说话人特征向量

**参数**:
- `stream`: 音频流对象

**返回值**: `FloatArray` - 特征向量数组

##### dim()
```kotlin
fun dim(): Int
```
**功能**: 获取特征向量维度

**返回值**: `Int` - 特征向量维度（通常为512）

---

### 3. SpeakerEmbeddingManager 类

#### 概述
说话人管理器，负责存储、搜索和管理说话人特征向量。

#### 构造函数
```kotlin
class SpeakerEmbeddingManager(val dim: Int)
```

#### API 接口

```kotlin
class SpeakerEmbeddingManager {
    // 说话人管理
    fun add(name: String, embedding: FloatArray): Boolean
    fun add(name: String, embedding: Array<FloatArray>): Boolean
    fun remove(name: String): Boolean
    
    // 识别和验证
    fun search(embedding: FloatArray, threshold: Float): String
    fun verify(name: String, embedding: FloatArray, threshold: Float): Boolean
    
    // 查询功能
    fun contains(name: String): Boolean
    fun numSpeakers(): Int
    fun allSpeakerNames(): Array<String>
    
    // 资源管理
    fun release()
}
```

#### 方法详解

##### add() - 单个特征向量
```kotlin
fun add(name: String, embedding: FloatArray): Boolean
```
**功能**: 添加单个说话人特征向量

**参数**:
- `name`: 说话人姓名
- `embedding`: 特征向量

**返回值**: `Boolean` - 是否添加成功

##### add() - 多个特征向量
```kotlin
fun add(name: String, embedding: Array<FloatArray>): Boolean
```
**功能**: 添加多个说话人特征向量（提高识别准确性）

**参数**:
- `name`: 说话人姓名
- `embedding`: 特征向量数组

**返回值**: `Boolean` - 是否添加成功

**使用示例**:
```kotlin
val success = SpeakerRecognition.manager.add("张三", embeddingList.toTypedArray())
if (success) {
    // 注册成功
}
```

##### search()
```kotlin
fun search(embedding: FloatArray, threshold: Float): String
```
**功能**: 搜索最匹配的说话人

**参数**:
- `embedding`: 待识别的特征向量
- `threshold`: 相似度阈值

**返回值**: `String` - 匹配的说话人姓名，未找到返回空字符串

**使用示例**:
```kotlin
val result = SpeakerRecognition.manager.search(embedding, 0.5f)
if (result.isNotEmpty()) {
    // 识别成功
    println("识别到说话人: $result")
}
```

##### verify()
```kotlin
fun verify(name: String, embedding: FloatArray, threshold: Float): Boolean
```
**功能**: 验证特征向量是否属于指定说话人

**参数**:
- `name`: 说话人姓名
- `embedding`: 特征向量
- `threshold`: 相似度阈值

**返回值**: `Boolean` - 是否匹配

##### remove()
```kotlin
fun remove(name: String): Boolean
```
**功能**: 删除指定说话人

**参数**:
- `name`: 说话人姓名

**返回值**: `Boolean` - 是否删除成功

##### allSpeakerNames()
```kotlin
fun allSpeakerNames(): Array<String>
```
**功能**: 获取所有已注册说话人姓名

**返回值**: `Array<String>` - 说话人姓名数组

---

### 4. OnlineStream 类

#### 概述
在线音频流处理类，负责接收和处理音频数据。

#### API 接口

```kotlin
class OnlineStream {
    // 音频输入
    fun acceptWaveform(samples: FloatArray, sampleRate: Int)
    fun inputFinished()
    
    // 资源管理
    fun release()
    fun use(block: (OnlineStream) -> Unit)
}
```

#### 方法详解

##### acceptWaveform()
```kotlin
fun acceptWaveform(samples: FloatArray, sampleRate: Int)
```
**功能**: 输入音频波形数据

**参数**:
- `samples`: 音频样本数组（浮点格式）
- `sampleRate`: 采样率（通常为16000Hz）

**使用示例**:
```kotlin
val samples = FloatArray(bufferSize) { buffer[it] / 32768.0f }
stream.acceptWaveform(samples, 16000)
```

##### inputFinished()
```kotlin
fun inputFinished()
```
**功能**: 标记音频输入结束

**使用场景**: 在完成音频输入后调用，准备进行特征提取

##### use()
```kotlin
fun use(block: (OnlineStream) -> Unit)
```
**功能**: 使用资源管理模式处理流

**参数**:
- `block`: 处理逻辑

**使用示例**:
```kotlin
SpeakerRecognition.extractor.createStream().use { stream ->
    stream.acceptWaveform(samples, 16000)
    stream.inputFinished()
    if (SpeakerRecognition.extractor.isReady(stream)) {
        val embedding = SpeakerRecognition.extractor.compute(stream)
        // 处理特征向量
    }
}
```

---

### 5. SpeakerEmbeddingExtractorConfig 数据类

#### 概述
说话人特征提取器配置参数。

#### 数据结构
```kotlin
data class SpeakerEmbeddingExtractorConfig(
    val model: String = "",
    var numThreads: Int = 1,
    var debug: Boolean = false,
    var provider: String = "cpu"
)
```

#### 参数说明

- **model**: 模型文件名
  - 类型: `String`
  - 默认值: `""`
  - 示例: `"3dspeaker_speech_eres2net_base_sv_zh-cn_3dspeaker_16k.onnx"`

- **numThreads**: 推理线程数
  - 类型: `Int`
  - 默认值: `1`
  - 建议值: `2-4`

- **debug**: 调试模式
  - 类型: `Boolean`
  - 默认值: `false`

- **provider**: 推理提供商
  - 类型: `String`
  - 默认值: `"cpu"`
  - 可选值: `"cpu"`, `"gpu"`

---

## 导航和UI API

### 6. NavRoutes 密封类

#### 概述
定义应用导航路由。

```kotlin
sealed class NavRoutes(val route: String) {
    object Home : NavRoutes("home")
    object Register : NavRoutes("register")
    object View : NavRoutes("view")
    object Help : NavRoutes("help")
}
```

### 7. BarItem 数据类

#### 概述
底部导航栏项目数据结构。

```kotlin
data class BarItem(
    val title: String,
    val image: ImageVector,
    val route: String
)
```

### 8. NavBarItems 对象

#### 概述
导航栏配置对象。

```kotlin
object NavBarItems {
    val BarItems = listOf(
        BarItem("Home", Icons.Filled.Home, "home"),
        BarItem("Register", Icons.Filled.Add, "register"),
        BarItem("View", Icons.Filled.AccountCircle, "view"),
        BarItem("Help", Icons.Filled.Info, "help")
    )
}
```

---

## 使用示例

### 完整的说话人识别流程

```kotlin
// 1. 初始化系统
SpeakerRecognition.initExtractor(assets)

// 2. 创建音频流
val stream = SpeakerRecognition.extractor.createStream()

// 3. 输入音频数据
stream.acceptWaveform(audioSamples, 16000)
stream.inputFinished()

// 4. 检查是否准备就绪
if (SpeakerRecognition.extractor.isReady(stream)) {
    // 5. 提取特征向量
    val embedding = SpeakerRecognition.extractor.compute(stream)
    
    // 6. 搜索匹配的说话人
    val result = SpeakerRecognition.manager.search(embedding, 0.5f)
    
    if (result.isNotEmpty()) {
        println("识别到说话人: $result")
    } else {
        println("未知说话人")
    }
}

// 7. 释放资源
stream.release()
```

### 说话人注册流程

```kotlin
// 1. 收集多个音频样本
val embeddingList = mutableListOf<FloatArray>()

repeat(3) { // 录制3次
    val stream = SpeakerRecognition.extractor.createStream()
    stream.acceptWaveform(audioSamples, 16000)
    stream.inputFinished()
    
    if (SpeakerRecognition.extractor.isReady(stream)) {
        val embedding = SpeakerRecognition.extractor.compute(stream)
        embeddingList.add(embedding)
    }
    stream.release()
}

// 2. 注册说话人
val success = SpeakerRecognition.manager.add(
    "张三", 
    embeddingList.toTypedArray()
)

if (success) {
    println("注册成功")
} else {
    println("注册失败")
}
```

---

## 错误处理

### 常见错误和处理方法

1. **权限错误**
```kotlin
if (ActivityCompat.checkSelfPermission(
    context, 
    Manifest.permission.RECORD_AUDIO
) != PackageManager.PERMISSION_GRANTED) {
    // 请求权限
    ActivityCompat.requestPermissions(
        activity, 
        arrayOf(Manifest.permission.RECORD_AUDIO), 
        REQUEST_CODE
    )
}
```

2. **模型加载错误**
```kotlin
try {
    SpeakerRecognition.initExtractor(assets)
} catch (e: Exception) {
    Log.e(TAG, "模型加载失败: ${e.message}")
    // 处理错误
}
```

3. **音频录制错误**
```kotlin
try {
    audioRecord?.startRecording()
} catch (e: IllegalStateException) {
    Log.e(TAG, "录音启动失败: ${e.message}")
    // 处理错误
}
```

---

## 性能优化建议

1. **及时释放资源**
```kotlin
// 使用完毕后立即释放
stream.release()
audioRecord?.release()
```

2. **合理设置线程数**
```kotlin
val config = SpeakerEmbeddingExtractorConfig(
    model = modelName,
    numThreads = 2, // 根据设备性能调整
    provider = "cpu"
)
```

3. **批量处理音频**
```kotlin
// 收集足够的音频数据后再处理
if (sampleList.size >= minSamples) {
    // 进行特征提取
}
```

这份 API 文档提供了完整的接口说明和使用示例，开发者可以根据这些信息正确使用各个组件的功能。