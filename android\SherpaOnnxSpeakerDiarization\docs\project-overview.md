# SherpaOnnx Speaker Diarization Android 项目概述

## 项目简介

SherpaOnnx Speaker Diarization 是一个基于 Android 平台的说话人分离应用，使用 Sherpa-ONNX 框架实现离线说话人分离功能。该应用能够处理音频文件，识别并分离不同的说话人。

## 主要功能模块

### 1. 核心模块

#### 1.1 说话人分离引擎 (OfflineSpeakerDiarization)
- **文件**: `OfflineSpeakerDiarization.kt`
- **功能**: 核心的说话人分离算法实现
- **主要组件**:
  - 说话人分割模型配置 (OfflineSpeakerSegmentationModelConfig)
  - 说话人嵌入提取器配置 (SpeakerEmbeddingExtractorConfig)
  - 快速聚类配置 (FastClusteringConfig)
  - JNI 接口调用原生 C++ 库

#### 1.2 说话人分离对象管理 (SpeakerDiarizationObject)
- **文件**: `SpeakerDiarizationObject.kt`
- **功能**: 单例模式管理说话人分离实例
- **主要职责**:
  - 初始化说话人分离配置
  - 管理模型文件 (segmentation.onnx, embedding.onnx)
  - 提供全局访问接口

### 2. 用户界面模块

#### 2.1 主活动 (MainActivity)
- **文件**: `MainActivity.kt`
- **功能**: 应用入口点和主界面框架
- **主要组件**:
  - Material Design 3 界面
  - 导航控制器
  - 顶部应用栏和底部导航栏

#### 2.2 主屏幕 (HomeScreen)
- **文件**: `screens/Home.kt`
- **功能**: 主要的用户交互界面
- **主要功能**:
  - 文件选择器
  - 说话人分离参数设置
  - 处理进度显示
  - 结果展示

#### 2.3 帮助屏幕 (HelpScreen)
- **文件**: `screens/Help.kt`
- **功能**: 应用使用说明和帮助信息

### 3. 工具模块

#### 3.1 音频文件读取 (ReadWaveFile)
- **文件**: `ReadWaveFile.kt`
- **功能**: 读取和解析音频文件
- **支持格式**: 16kHz 16-bit 单声道 WAV 文件
- **使用技术**: MediaExtractor 和 MediaCodec

#### 3.2 导航路由 (NavRoutes)
- **文件**: `NavRoutes.kt`
- **功能**: 定义应用内导航路由

#### 3.3 导航栏配置
- **文件**: `NavBarItems.kt`, `BarItem.kt`
- **功能**: 底部导航栏配置和项目定义

### 4. UI 主题模块
- **目录**: `ui/theme/`
- **文件**: `Color.kt`, `Theme.kt`, `Type.kt`
- **功能**: Material Design 3 主题配置

## 技术架构

### 前端技术栈
- **UI 框架**: Jetpack Compose
- **导航**: Navigation Compose
- **设计系统**: Material Design 3
- **语言**: Kotlin

### 后端技术栈
- **AI 框架**: Sherpa-ONNX
- **模型格式**: ONNX
- **原生库**: JNI + C++
- **音频处理**: Android MediaCodec

### 核心算法
1. **说话人分割**: 基于 pyannote-audio 的分割模型
2. **说话人嵌入**: 基于 3D-Speaker 的嵌入提取
3. **聚类算法**: 快速聚类算法

## 应用执行流程

```mermaid
flowchart TD
    A[应用启动] --> B[MainActivity.onCreate]
    B --> C[初始化 SpeakerDiarizationObject]
    C --> D[加载模型文件]
    D --> E[显示主界面]
    E --> F[用户选择音频文件]
    F --> G[ReadWaveFile 解析音频]
    G --> H{文件格式检查}
    H -->|格式正确| I[设置分离参数]
    H -->|格式错误| J[显示错误信息]
    I --> K[开始说话人分离]
    K --> L[OfflineSpeakerDiarization.process]
    L --> M[显示处理进度]
    M --> N[返回分离结果]
    N --> O[显示结果界面]
    O --> P[用户可复制结果]
    J --> F
    P --> F
```

## 详细执行流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 用户界面
    participant Main as MainActivity
    participant SDO as SpeakerDiarizationObject
    participant SD as OfflineSpeakerDiarization
    participant Reader as ReadWaveFile
    participant Native as 原生库(JNI)

    User->>UI: 启动应用
    UI->>Main: onCreate()
    Main->>SDO: initSpeakerDiarization()
    SDO->>SD: 创建实例
    SD->>Native: 加载模型文件
    Native-->>SD: 模型加载完成
    SD-->>SDO: 初始化完成
    SDO-->>Main: 返回
    Main->>UI: 显示主界面
    
    User->>UI: 选择音频文件
    UI->>Reader: readUri()
    Reader->>Reader: MediaExtractor 解析
    Reader->>Reader: MediaCodec 解码
    Reader-->>UI: 返回音频数据
    
    UI->>UI: 验证采样率
    User->>UI: 设置参数并开始处理
    UI->>SDO: 获取 SD 实例
    SDO-->>UI: 返回实例
    UI->>SD: processWithCallback()
    SD->>Native: 调用原生分离算法
    
    loop 处理过程
        Native-->>UI: 回调进度更新
        UI->>UI: 更新进度显示
    end
    
    Native-->>SD: 返回分离结果
    SD-->>UI: 返回结果数组
    UI->>UI: 格式化并显示结果
    User->>UI: 查看/复制结果
```

## 核心数据流

```mermaid
flowchart LR
    A[音频文件] --> B[MediaExtractor]
    B --> C[MediaCodec]
    C --> D[FloatArray 音频数据]
    D --> E[说话人分割模型]
    E --> F[分割片段]
    F --> G[说话人嵌入提取]
    G --> H[特征向量]
    H --> I[聚类算法]
    I --> J[说话人标签]
    J --> K[分离结果]
    K --> L[UI 显示]
```

## 模型文件依赖

1. **分割模型**: `segmentation.onnx`
   - 来源: pyannote-audio segmentation-3.0
   - 功能: 检测语音活动和说话人变化点

2. **嵌入模型**: `embedding.onnx`
   - 来源: 3D-Speaker eres2net
   - 功能: 提取说话人特征向量

## 配置参数

- **numClusters**: 说话人数量 (-1 为自动检测)
- **threshold**: 聚类阈值 (默认 0.5)
- **minDurationOn**: 最小语音持续时间 (0.2秒)
- **minDurationOff**: 最小静音持续时间 (0.5秒)
- **numThreads**: 处理线程数

## 支持的音频格式

- **采样率**: 16kHz
- **位深**: 16-bit
- **声道**: 单声道
- **格式**: WAV 文件

## 权限要求

- `READ_EXTERNAL_STORAGE`: 读取外部存储中的音频文件 (API 32 及以下)
- 文档访问权限: 通过 Storage Access Framework 访问文件