<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp">

    <!-- User Message -->
    <LinearLayout
        android:id="@+id/layout_user_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:layout_marginBottom="8dp"
        android:visibility="gone">

        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="48dp"
            app:cardBackgroundColor="@color/apple_blue"
            app:cardCornerRadius="18dp"
            app:cardElevation="2dp">

            <TextView
                android:id="@+id/tv_user_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="12dp"
                android:textColor="@android:color/white"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                android:lineSpacingExtra="2dp" />

        </androidx.cardview.widget.CardView>

    </LinearLayout>

    <!-- AI Message -->
    <LinearLayout
        android:id="@+id/layout_ai_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="start"
        android:visibility="gone">

        <TextView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:text="🤖"
            android:textSize="20sp"
            android:gravity="center"
            android:layout_marginEnd="8dp"
            android:layout_marginTop="4dp" />

        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="48dp"
            app:cardBackgroundColor="@color/apple_system_background"
            app:cardCornerRadius="18dp"
            app:cardElevation="2dp">

            <TextView
                android:id="@+id/tv_ai_message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="12dp"
                android:textColor="@color/apple_label"
                android:textAppearance="@style/TextAppearance.VoiceAssistant.Body"
                android:lineSpacingExtra="2dp"
                android:textIsSelectable="true" />

        </androidx.cardview.widget.CardView>

    </LinearLayout>

    <!-- Timestamp -->
    <TextView
        android:id="@+id/tv_timestamp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:textAppearance="@style/TextAppearance.VoiceAssistant.Caption"
        android:textColor="@color/apple_tertiary_label"
        android:layout_marginTop="4dp"
        android:visibility="gone" />

</LinearLayout>