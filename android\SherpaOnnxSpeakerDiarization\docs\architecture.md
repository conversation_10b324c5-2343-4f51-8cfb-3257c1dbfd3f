# SherpaOnnx Speaker Diarization 架构设计文档

## 整体架构

```mermaid
graph TB
    subgraph "UI Layer (Jetpack Compose)"
        A[MainActivity]
        B[HomeScreen]
        C[HelpScreen]
        D[Navigation]
    end
    
    subgraph "Business Logic Layer"
        E[SpeakerDiarizationObject]
        F[ReadWaveFile]
        G[Configuration]
    end
    
    subgraph "Core Engine Layer"
        H[OfflineSpeakerDiarization]
        I[SpeakerEmbeddingExtractorConfig]
        J[FastClusteringConfig]
    end
    
    subgraph "Native Layer (JNI)"
        K[C++ Implementation]
        L[ONNX Runtime]
    end
    
    subgraph "Model Assets"
        M[segmentation.onnx]
        N[embedding.onnx]
    end
    
    A --> E
    B --> E
    B --> F
    E --> H
    H --> K
    K --> L
    L --> M
    L --> N
    F --> H
```

## 模块详细设计

### 1. UI 层 (Presentation Layer)

#### MainActivity
```mermaid
classDiagram
    class MainActivity {
        +onCreate(Bundle)
        +MainScreen()
        +NavigationHost()
        +BottomNavigationBar()
    }
    
    class NavigationController {
        +NavHostController
        +navigate(route: String)
    }
    
    MainActivity --> NavigationController
    MainActivity --> SpeakerDiarizationObject : initializes
```

#### HomeScreen 组件架构
```mermaid
flowchart TD
    A[HomeScreen] --> B[File Picker]
    A --> C[Parameter Controls]
    A --> D[Progress Display]
    A --> E[Results Display]
    
    B --> F[DocumentFile Launcher]
    C --> G[Speaker Count Input]
    C --> H[Threshold Slider]
    D --> I[Progress Callback]
    E --> J[Clipboard Manager]
    
    F --> K[ReadWaveFile]
    I --> L[Processing Thread]
    L --> M[SpeakerDiarizationObject]
```

### 2. 业务逻辑层 (Business Logic Layer)

#### SpeakerDiarizationObject 单例模式
```mermaid
classDiagram
    class SpeakerDiarizationObject {
        -_sd: OfflineSpeakerDiarization?
        +sd: OfflineSpeakerDiarization
        +initSpeakerDiarization(AssetManager?)
        -segmentationModel: String
        -embeddingModel: String
    }
    
    class OfflineSpeakerDiarization {
        -ptr: Long
        +process(FloatArray): Array~OfflineSpeakerDiarizationSegment~
        +processWithCallback(FloatArray, callback, Long)
        +sampleRate(): Int
        +setConfig(OfflineSpeakerDiarizationConfig)
    }
    
    SpeakerDiarizationObject --> OfflineSpeakerDiarization : manages
```

#### 音频文件处理流程
```mermaid
sequenceDiagram
    participant UI as HomeScreen
    participant Reader as ReadWaveFile
    participant Extractor as MediaExtractor
    participant Decoder as MediaCodec
    
    UI->>Reader: readUri(context, uri)
    Reader->>Extractor: setDataSource()
    Reader->>Extractor: getTrackFormat()
    
    loop for each track
        Extractor->>Reader: track format info
        Reader->>Reader: check if audio track
        Reader->>Reader: validate PCM_16BIT encoding
    end
    
    Reader->>Decoder: createDecoderByType()
    Reader->>Decoder: configure() & start()
    
    loop decode audio data
        Reader->>Decoder: queueInputBuffer()
        Decoder->>Reader: dequeueOutputBuffer()
        Reader->>Reader: convert to FloatArray
    end
    
    Reader-->>UI: WaveData(sampleRate, samples)
```

### 3. 核心引擎层 (Core Engine Layer)

#### 配置类层次结构
```mermaid
classDiagram
    class OfflineSpeakerDiarizationConfig {
        +segmentation: OfflineSpeakerSegmentationModelConfig
        +embedding: SpeakerEmbeddingExtractorConfig
        +clustering: FastClusteringConfig
        +minDurationOn: Float
        +minDurationOff: Float
    }
    
    class OfflineSpeakerSegmentationModelConfig {
        +pyannote: OfflineSpeakerSegmentationPyannoteModelConfig
        +numThreads: Int
        +debug: Boolean
        +provider: String
    }
    
    class SpeakerEmbeddingExtractorConfig {
        +model: String
        +numThreads: Int
        +debug: Boolean
        +provider: String
    }
    
    class FastClusteringConfig {
        +numClusters: Int
        +threshold: Float
    }
    
    OfflineSpeakerDiarizationConfig --> OfflineSpeakerSegmentationModelConfig
    OfflineSpeakerDiarizationConfig --> SpeakerEmbeddingExtractorConfig
    OfflineSpeakerDiarizationConfig --> FastClusteringConfig
```

#### 说话人分离处理流程
```mermaid
flowchart TD
    A[音频输入 FloatArray] --> B[说话人分割]
    B --> C[语音活动检测]
    C --> D[说话人变化点检测]
    D --> E[音频片段提取]
    E --> F[说话人嵌入提取]
    F --> G[特征向量计算]
    G --> H[聚类分析]
    H --> I{聚类方式}
    I -->|固定数量| J[K-means 聚类]
    I -->|阈值聚类| K[层次聚类]
    J --> L[说话人标签分配]
    K --> L
    L --> M[结果片段生成]
    M --> N[OfflineSpeakerDiarizationSegment Array]
```

### 4. 原生层 (Native Layer)

#### JNI 接口设计
```mermaid
classDiagram
    class OfflineSpeakerDiarization {
        -ptr: Long
        -external delete(Long)
        -external newFromAsset(AssetManager, Config): Long
        -external newFromFile(Config): Long
        -external setConfig(Long, Config)
        -external getSampleRate(Long): Int
        -external process(Long, FloatArray): Array
        -external processWithCallback(Long, FloatArray, callback, Long): Array
    }
    
    note for OfflineSpeakerDiarization "JNI 方法映射到 C++ 实现"
```

#### 原生库加载流程
```mermaid
sequenceDiagram
    participant App as Android App
    participant JNI as JNI Layer
    participant CPP as C++ Engine
    participant ONNX as ONNX Runtime
    participant Models as Model Files
    
    App->>JNI: System.loadLibrary("sherpa-onnx-jni")
    JNI->>CPP: 加载原生库
    App->>JNI: newFromAsset(assetManager, config)
    JNI->>CPP: 创建 SpeakerDiarization 实例
    CPP->>ONNX: 初始化 ONNX Runtime
    CPP->>Models: 加载 segmentation.onnx
    CPP->>Models: 加载 embedding.onnx
    Models-->>CPP: 模型加载完成
    CPP-->>JNI: 返回实例指针
    JNI-->>App: 返回 ptr (Long)
```

## 数据流架构

### 音频数据流
```mermaid
flowchart LR
    A["WAV 文件<br/>(16kHz, 16-bit, Mono)"] --> B["MediaExtractor<br/>格式解析"]
    B --> C["MediaCodec<br/>音频解码"]
    C --> D["ByteBuffer<br/>PCM 数据"]
    D --> E["FloatArray<br/>归一化音频"]
    E --> F["JNI 调用<br/>原生处理"]
    F --> G["C++ 引擎<br/>说话人分离"]
    G --> H["结果数组<br/>时间段+说话人ID"]
    H --> I["UI 显示<br/>格式化结果"]
```

### 配置数据流
```mermaid
flowchart TD
    A[用户输入参数] --> B[UI 状态管理]
    B --> C[配置对象构建]
    C --> D[SpeakerDiarizationObject]
    D --> E[OfflineSpeakerDiarization]
    E --> F[JNI 配置传递]
    F --> G[C++ 引擎配置]
    
    subgraph "配置参数"
        H[说话人数量]
        I[聚类阈值]
        J[最小持续时间]
        K[线程数]
    end
    
    H --> A
    I --> A
    J --> A
    K --> A
```

## 错误处理架构

```mermaid
flowchart TD
    A[用户操作] --> B{输入验证}
    B -->|验证失败| C[显示错误信息]
    B -->|验证通过| D[执行操作]
    D --> E{操作结果}
    E -->|成功| F[显示结果]
    E -->|失败| G[错误分类]
    
    G --> H{错误类型}
    H -->|文件格式错误| I["格式不支持<br/>显示要求"]
    H -->|采样率错误| J["采样率不匹配<br/>显示期望值"]
    H -->|内存不足| K["内存错误<br/>建议重启"]
    H -->|模型加载失败| L["模型错误<br/>检查文件"]
    H -->|其他错误| M["通用错误<br/>显示详情"]
    
    I --> C
    J --> C
    K --> C
    L --> C
    M --> C
    C --> A
```

## 性能优化策略

### 1. 内存管理
```mermaid
flowchart LR
    A[音频文件] --> B[流式读取]
    B --> C[分块处理]
    C --> D[及时释放]
    D --> E[垃圾回收]
    
    F[模型缓存] --> G[单例模式]
    G --> H[延迟加载]
    H --> I[生命周期管理]
```

### 2. 线程管理
```mermaid
flowchart TD
    A[主线程 UI] --> B[后台线程处理]
    B --> C[进度回调]
    C --> D[UI 线程更新]
    
    E[原生线程池] --> F[并行计算]
    F --> G[ONNX 推理]
    G --> H[结果聚合]
```

### 3. 缓存策略
```mermaid
flowchart LR
    A[模型文件] --> B[内存缓存]
    B --> C[会话保持]
    C --> D[复用实例]
    
    E[配置参数] --> F[状态保存]
    F --> G[快速恢复]
```

## 扩展性设计

### 1. 模型扩展
```mermaid
classDiagram
    class ModelConfig {
        <<interface>>
        +getModelPath(): String
        +getModelType(): String
    }
    
    class SegmentationModelConfig {
        +pyannote: PyannoteConfig
    }
    
    class EmbeddingModelConfig {
        +model: String
        +provider: String
    }
    
    ModelConfig <|-- SegmentationModelConfig
    ModelConfig <|-- EmbeddingModelConfig
```

### 2. 算法扩展
```mermaid
classDiagram
    class ClusteringAlgorithm {
        <<interface>>
        +cluster(embeddings: Array): Array
    }
    
    class FastClustering {
        +threshold: Float
        +numClusters: Int
    }
    
    class HierarchicalClustering {
        +linkage: String
        +distance: String
    }
    
    ClusteringAlgorithm <|-- FastClustering
    ClusteringAlgorithm <|-- HierarchicalClustering
```

## 测试架构

```mermaid
flowchart TD
    A[单元测试] --> B[组件测试]
    B --> C[集成测试]
    C --> D[端到端测试]
    
    subgraph "测试层次"
        E["UI 测试<br/>Compose Testing"]
        F["业务逻辑测试<br/>JUnit"]
        G["JNI 测试<br/>Native Testing"]
        H["性能测试<br/>Benchmark"]
    end
    
    A --> F
    B --> E
    C --> G
    D --> H
```