# SherpaOnnxTts API 参考文档

## 核心类 API

### OfflineTts 类

#### 概述
`OfflineTts` 是核心的文本转语音引擎类，提供离线TTS功能。

#### 构造函数

```kotlin
class OfflineTts(
    assetManager: AssetManager? = null,
    var config: OfflineTtsConfig
)
```

**参数**:
- `assetManager`: Android AssetManager，用于从assets加载模型
- `config`: TTS配置对象

#### 主要方法

##### sampleRate()
```kotlin
fun sampleRate(): Int
```
**功能**: 获取音频采样率  
**返回值**: 采样率 (Hz)

##### numSpeakers()
```kotlin
fun numSpeakers(): Int
```
**功能**: 获取模型支持的说话人数量  
**返回值**: 说话人数量

##### generate()
```kotlin
fun generate(
    text: String,
    sid: Int = 0,
    speed: Float = 1.0f
): GeneratedAudio
```
**功能**: 生成语音音频  
**参数**:
- `text`: 待转换的文本
- `sid`: 说话人ID (默认0)
- `speed`: 语速倍率 (默认1.0)

**返回值**: `GeneratedAudio` 对象

##### generateWithCallback()
```kotlin
fun generateWithCallback(
    text: String,
    sid: Int = 0,
    speed: Float = 1.0f,
    callback: (samples: FloatArray) -> Int
): GeneratedAudio
```
**功能**: 带回调的语音生成，支持实时播放  
**参数**:
- `text`: 待转换的文本
- `sid`: 说话人ID
- `speed`: 语速倍率
- `callback`: 音频回调函数，返回0停止生成，返回1继续

**返回值**: `GeneratedAudio` 对象

##### 资源管理方法
```kotlin
fun allocate(assetManager: AssetManager? = null)
fun free()
fun release()
```
**功能**: 分配、释放和清理TTS资源

---

### GeneratedAudio 类

#### 概述
封装生成的音频数据和相关信息。

#### 属性
```kotlin
class GeneratedAudio(
    val samples: FloatArray,  // 音频样本数据 [-1, 1]
    val sampleRate: Int,      // 采样率
)
```

#### 方法

##### save()
```kotlin
fun save(filename: String): Boolean
```
**功能**: 保存音频到文件  
**参数**: `filename` - 文件路径  
**返回值**: 保存是否成功

---

### 配置类 API

### OfflineTtsConfig 类

```kotlin
data class OfflineTtsConfig(
    var model: OfflineTtsModelConfig = OfflineTtsModelConfig(),
    var ruleFsts: String = "",           // FST规则文件路径
    var ruleFars: String = "",           // FAR规则文件路径
    var maxNumSentences: Int = 1,        // 最大句子数
    var silenceScale: Float = 0.2f,      // 静音比例
)
```

### OfflineTtsModelConfig 类

```kotlin
data class OfflineTtsModelConfig(
    var vits: OfflineTtsVitsModelConfig = OfflineTtsVitsModelConfig(),
    var matcha: OfflineTtsMatchaModelConfig = OfflineTtsMatchaModelConfig(),
    var kokoro: OfflineTtsKokoroModelConfig = OfflineTtsKokoroModelConfig(),
    var numThreads: Int = 1,             // 推理线程数
    var debug: Boolean = false,          // 调试模式
    var provider: String = "cpu",        // 推理提供者
)
```

### VITS 模型配置

```kotlin
data class OfflineTtsVitsModelConfig(
    var model: String = "",              // 模型文件路径
    var lexicon: String = "",            // 词典文件路径
    var tokens: String = "",             // 词元文件路径
    var dataDir: String = "",            // 数据目录
    var dictDir: String = "",            // 字典目录
    var noiseScale: Float = 0.667f,      // 噪声比例
    var noiseScaleW: Float = 0.8f,       // 噪声宽度比例
    var lengthScale: Float = 1.0f,       // 长度比例
)
```

### Matcha 模型配置

```kotlin
data class OfflineTtsMatchaModelConfig(
    var acousticModel: String = "",      // 声学模型路径
    var vocoder: String = "",            // 声码器路径
    var lexicon: String = "",            // 词典文件路径
    var tokens: String = "",             // 词元文件路径
    var dataDir: String = "",            // 数据目录
    var dictDir: String = "",            // 字典目录
    var noiseScale: Float = 1.0f,        // 噪声比例
    var lengthScale: Float = 1.0f,       // 长度比例
)
```

### Kokoro 模型配置

```kotlin
data class OfflineTtsKokoroModelConfig(
    var model: String = "",              // 模型文件路径
    var voices: String = "",             // 声音文件路径
    var tokens: String = "",             // 词元文件路径
    var dataDir: String = "",            // 数据目录
    var lexicon: String = "",            // 词典文件路径
    var dictDir: String = "",            // 字典目录
    var lengthScale: Float = 1.0f,       // 长度比例
)
```

---

## 工具函数 API

### getOfflineTtsConfig()

```kotlin
fun getOfflineTtsConfig(
    modelDir: String,                    // 模型目录
    modelName: String,                   // VITS模型文件名
    acousticModelName: String,           // Matcha声学模型文件名
    vocoder: String,                     // Matcha声码器文件名
    voices: String,                      // Kokoro声音文件名
    lexicon: String,                     // 词典文件名
    dataDir: String,                     // 数据目录
    dictDir: String,                     // 字典目录
    ruleFsts: String,                    // FST规则文件
    ruleFars: String,                    // FAR规则文件
    numThreads: Int? = null              // 线程数
): OfflineTtsConfig?
```

**功能**: 创建TTS配置对象的工厂函数  
**返回值**: 配置好的 `OfflineTtsConfig` 对象

**使用示例**:
```kotlin
val config = getOfflineTtsConfig(
    modelDir = "vits-melo-tts-zh_en",
    modelName = "model.onnx",
    acousticModelName = "",
    vocoder = "",
    voices = "",
    lexicon = "lexicon.txt",
    dataDir = "",
    dictDir = "vits-melo-tts-zh_en/dict",
    ruleFsts = "",
    ruleFars = ""
)
```

---

## MainActivity API

### 主要方法

#### onCreate()
```kotlin
override fun onCreate(savedInstanceState: Bundle?)
```
**功能**: Activity初始化，设置TTS引擎和UI组件

#### 事件处理方法

##### onClickGenerate()
```kotlin
private fun onClickGenerate()
```
**功能**: 处理生成按钮点击事件，执行文本转语音

##### onClickPlay()
```kotlin
private fun onClickPlay()
```
**功能**: 处理播放按钮点击事件，播放生成的音频文件

##### onClickStop()
```kotlin
private fun onClickStop()
```
**功能**: 处理停止按钮点击事件，停止当前操作

#### 回调方法

##### callback()
```kotlin
private fun callback(samples: FloatArray): Int
```
**功能**: 音频生成回调函数，用于实时播放  
**参数**: `samples` - 音频样本数组  
**返回值**: 1继续生成，0停止生成

#### 初始化方法

##### initTts()
```kotlin
private fun initTts()
```
**功能**: 初始化TTS引擎，配置模型参数

##### initAudioTrack()
```kotlin
private fun initAudioTrack()
```
**功能**: 初始化AudioTrack，设置音频播放参数

#### 资源管理方法

##### copyAssets()
```kotlin
private fun copyAssets(path: String)
```
**功能**: 递归复制assets资源到内部存储  
**参数**: `path` - 资源路径

##### copyFile()
```kotlin
private fun copyFile(filename: String)
```
**功能**: 复制单个文件  
**参数**: `filename` - 文件名

##### copyDataDir()
```kotlin
private fun copyDataDir(dataDir: String): String
```
**功能**: 复制数据目录  
**参数**: `dataDir` - 数据目录名  
**返回值**: 新的数据目录路径

---

## 使用示例

### 基本使用流程

```kotlin
// 1. 创建配置
val config = getOfflineTtsConfig(
    modelDir = "vits-melo-tts-zh_en",
    modelName = "model.onnx",
    // ... 其他参数
)

// 2. 创建TTS实例
val tts = OfflineTts(assetManager = assets, config = config!!)

// 3. 生成语音
val audio = tts.generate(
    text = "你好，世界！",
    sid = 0,
    speed = 1.0f
)

// 4. 保存音频
val success = audio.save("/path/to/output.wav")

// 5. 清理资源
tts.free()
```

### 实时播放示例

```kotlin
// 带回调的实时生成
val audio = tts.generateWithCallback(
    text = "实时播放测试",
    sid = 0,
    speed = 1.0f
) { samples ->
    // 实时播放音频块
    audioTrack.write(samples, 0, samples.size, AudioTrack.WRITE_BLOCKING)
    if (shouldContinue) 1 else 0  // 返回继续标志
}
```

### 错误处理示例

```kotlin
try {
    val audio = tts.generate(text, sid, speed)
    if (audio.samples.isNotEmpty()) {
        // 处理成功生成的音频
        audio.save(filename)
    }
} catch (e: Exception) {
    Log.e(TAG, "TTS生成失败: ${e.message}")
    // 错误处理逻辑
}
```

## 注意事项

### 资源管理
- 使用完毕后必须调用 `free()` 释放本地资源
- 避免内存泄漏，及时清理AudioTrack和MediaPlayer

### 线程安全
- TTS生成操作应在后台线程执行
- UI更新必须在主线程进行
- 音频回调可能在不同线程执行

### 性能优化
- 合理设置线程数 (`numThreads`)
- 避免频繁创建TTS实例
- 使用音频回调实现流式播放

### 模型兼容性
- 确保模型文件与配置匹配
- 检查必需的配置文件是否存在
- 验证模型支持的语言和说话人