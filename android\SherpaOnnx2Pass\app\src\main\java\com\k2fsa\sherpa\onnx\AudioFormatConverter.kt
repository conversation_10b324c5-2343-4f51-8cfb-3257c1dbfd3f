package com.k2fsa.sherpa.onnx

import android.content.Context
import android.media.*
import android.net.Uri
import android.util.Log
import java.io.*
import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * 音频格式转换器
 * 支持将MP3、M4A等格式转换为WAV格式供ASR使用
 */
class AudioFormatConverter(private val context: Context) {
    
    companion object {
        private const val TAG = "AudioFormatConverter"
        private const val TARGET_SAMPLE_RATE = 16000
        private const val TARGET_CHANNEL_COUNT = 1
        private const val TARGET_BIT_DEPTH = 16
    }
    
    /**
     * 转换音频文件为WAV格式
     */
    fun convertToWav(inputUri: Uri, outputFile: File): Boolean {
        return try {
            when (getAudioFormat(inputUri)) {
                AudioFormat.WAV -> {
                    // WAV文件直接复制
                    copyWavFile(inputUri, outputFile)
                }
                AudioFormat.MP3 -> {
                    // MP3转换为WAV
                    convertMp3ToWav(inputUri, outputFile)
                }
                AudioFormat.M4A -> {
                    // M4A转换为WAV
                    convertM4aToWav(inputUri, outputFile)
                }
                else -> {
                    Log.w(TAG, "不支持的音频格式")
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "音频格式转换失败", e)
            false
        }
    }
    
    /**
     * 获取音频格式
     */
    private fun getAudioFormat(uri: Uri): AudioFormat {
        return try {
            val fileName = getFileName(uri).lowercase()
            when {
                fileName.endsWith(".wav") -> AudioFormat.WAV
                fileName.endsWith(".mp3") -> AudioFormat.MP3
                fileName.endsWith(".m4a") || fileName.endsWith(".aac") -> AudioFormat.M4A
                else -> AudioFormat.UNKNOWN
            }
        } catch (e: Exception) {
            AudioFormat.UNKNOWN
        }
    }
    
    /**
     * 复制WAV文件
     */
    private fun copyWavFile(inputUri: Uri, outputFile: File): Boolean {
        return try {
            context.contentResolver.openInputStream(inputUri)?.use { inputStream ->
                FileOutputStream(outputFile).use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
            }
            Log.i(TAG, "WAV文件复制完成")
            true
        } catch (e: Exception) {
            Log.e(TAG, "WAV文件复制失败", e)
            false
        }
    }
    
    /**
     * 转换MP3为WAV
     */
    private fun convertMp3ToWav(inputUri: Uri, outputFile: File): Boolean {
        var extractor: MediaExtractor? = null
        var decoder: MediaCodec? = null

        return try {
            // 检查可用内存
            val runtime = Runtime.getRuntime()
            val freeMemory = runtime.freeMemory()
            val totalMemory = runtime.totalMemory()
            val maxMemory = runtime.maxMemory()

            Log.i(TAG, "内存状态 - 可用: ${freeMemory / 1024 / 1024}MB, 总计: ${totalMemory / 1024 / 1024}MB, 最大: ${maxMemory / 1024 / 1024}MB")

            // 如果可用内存过低，先进行垃圾回收
            if (freeMemory < 10 * 1024 * 1024) { // 小于10MB
                System.gc()
                Log.i(TAG, "执行垃圾回收以释放内存")
            }

            extractor = MediaExtractor()
            extractor.setDataSource(context, inputUri, null)

            // 查找音频轨道
            val audioTrackIndex = findAudioTrack(extractor)
            if (audioTrackIndex < 0) {
                Log.e(TAG, "未找到音频轨道")
                return false
            }

            extractor.selectTrack(audioTrackIndex)
            val format = extractor.getTrackFormat(audioTrackIndex)

            // 获取原始音频参数
            val originalSampleRate = format.getInteger(MediaFormat.KEY_SAMPLE_RATE)
            val originalChannelCount = format.getInteger(MediaFormat.KEY_CHANNEL_COUNT)

            Log.i(TAG, "原始音频参数 - 采样率: ${originalSampleRate}Hz, 声道数: $originalChannelCount")
            Log.i(TAG, "目标音频参数 - 采样率: ${TARGET_SAMPLE_RATE}Hz, 声道数: $TARGET_CHANNEL_COUNT")

            // 创建解码器
            val mime = format.getString(MediaFormat.KEY_MIME) ?: ""
            decoder = MediaCodec.createDecoderByType(mime)
            decoder.configure(format, null, null, 0)
            decoder.start()

            // 解码并转换
            val success = decodeAndConvert(extractor, decoder, outputFile, originalSampleRate, originalChannelCount)

            if (success) {
                Log.i(TAG, "MP3转WAV完成: $success, 输出文件大小: ${outputFile.length()} bytes")

                // 验证输出文件
                if (outputFile.length() < 100) {
                    Log.w(TAG, "输出文件过小，可能转换失败")
                    return false
                }
            } else {
                Log.e(TAG, "MP3转WAV失败")
            }

            success

        } catch (e: OutOfMemoryError) {
            Log.e(TAG, "MP3转WAV内存不足", e)
            // 删除可能创建的不完整文件
            if (outputFile.exists()) {
                outputFile.delete()
            }
            false
        } catch (e: Exception) {
            Log.e(TAG, "MP3转WAV失败", e)
            false
        } finally {
            // 确保资源被释放
            try {
                decoder?.stop()
                decoder?.release()
                extractor?.release()
            } catch (e: Exception) {
                Log.w(TAG, "释放资源时出错", e)
            }
        }
    }
    
    /**
     * 转换M4A为WAV
     */
    private fun convertM4aToWav(inputUri: Uri, outputFile: File): Boolean {
        // M4A和MP3使用相同的解码流程
        return convertMp3ToWav(inputUri, outputFile)
    }
    
    /**
     * 查找音频轨道
     */
    private fun findAudioTrack(extractor: MediaExtractor): Int {
        for (i in 0 until extractor.trackCount) {
            val format = extractor.getTrackFormat(i)
            val mime = format.getString(MediaFormat.KEY_MIME) ?: ""
            if (mime.startsWith("audio/")) {
                return i
            }
        }
        return -1
    }
    
    /**
     * 解码并转换为WAV（流式处理，避免内存溢出）
     */
    private fun decodeAndConvert(
        extractor: MediaExtractor,
        decoder: MediaCodec,
        outputFile: File,
        originalSampleRate: Int,
        originalChannelCount: Int
    ): Boolean {
        var fileOutputStream: FileOutputStream? = null
        return try {
            val info = MediaCodec.BufferInfo()
            var totalBytes = 0

            // 创建输出流，先写入占位的WAV头
            fileOutputStream = FileOutputStream(outputFile)
            val wavHeaderPlaceholder = ByteArray(44) // WAV头占位符
            fileOutputStream.write(wavHeaderPlaceholder)

            var inputDone = false
            var outputDone = false

            while (!outputDone) {
                // 输入数据
                if (!inputDone) {
                    val inputBufferIndex = decoder.dequeueInputBuffer(10000)
                    if (inputBufferIndex >= 0) {
                        val inputBuffer = decoder.getInputBuffer(inputBufferIndex)
                        if (inputBuffer != null) {
                            val sampleSize = extractor.readSampleData(inputBuffer, 0)

                            if (sampleSize < 0) {
                                decoder.queueInputBuffer(inputBufferIndex, 0, 0, 0, MediaCodec.BUFFER_FLAG_END_OF_STREAM)
                                inputDone = true
                            } else {
                                val presentationTime = extractor.sampleTime
                                decoder.queueInputBuffer(inputBufferIndex, 0, sampleSize, presentationTime, 0)
                                extractor.advance()
                            }
                        }
                    }
                }

                // 输出数据
                val outputBufferIndex = decoder.dequeueOutputBuffer(info, 10000)
                if (outputBufferIndex >= 0) {
                    val outputBuffer = decoder.getOutputBuffer(outputBufferIndex)

                    if (outputBuffer != null && info.size > 0) {
                        val chunk = ByteArray(info.size)
                        outputBuffer.get(chunk)
                        outputBuffer.clear()

                        // 转换音频格式（采样率、声道数、位深度）
                        val convertedChunk = convertAudioFormat(
                            chunk,
                            originalSampleRate,
                            originalChannelCount,
                            TARGET_SAMPLE_RATE,
                            TARGET_CHANNEL_COUNT
                        )

                        // 写入转换后的数据
                        fileOutputStream.write(convertedChunk)
                        totalBytes += convertedChunk.size
                    }

                    decoder.releaseOutputBuffer(outputBufferIndex, false)

                    if ((info.flags and MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                        outputDone = true
                    }
                }
            }

            fileOutputStream.close()

            // 更新WAV文件头
            updateWavHeader(outputFile, totalBytes)

            true

        } catch (e: Exception) {
            Log.e(TAG, "解码转换失败", e)
            fileOutputStream?.close()
            false
        }
    }
    
    /**
     * 更新WAV文件头（在数据写入完成后）
     */
    private fun updateWavHeader(outputFile: File, dataSize: Int): Boolean {
        return try {
            RandomAccessFile(outputFile, "rw").use { raf ->
                // 移动到文件开头
                raf.seek(0)

                // 写入正确的WAV文件头
                val header = createWavHeader(dataSize)
                raf.write(header)
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "更新WAV文件头失败", e)
            false
        }
    }
    
    /**
     * 创建WAV文件头
     */
    private fun createWavHeader(dataSize: Int): ByteArray {
        val header = ByteArray(44)
        val totalDataLen = dataSize + 36
        val byteRate = TARGET_SAMPLE_RATE * TARGET_CHANNEL_COUNT * TARGET_BIT_DEPTH / 8
        
        // RIFF header
        header[0] = 'R'.code.toByte()
        header[1] = 'I'.code.toByte()
        header[2] = 'F'.code.toByte()
        header[3] = 'F'.code.toByte()
        header[4] = (totalDataLen and 0xff).toByte()
        header[5] = ((totalDataLen shr 8) and 0xff).toByte()
        header[6] = ((totalDataLen shr 16) and 0xff).toByte()
        header[7] = ((totalDataLen shr 24) and 0xff).toByte()
        
        // WAVE header
        header[8] = 'W'.code.toByte()
        header[9] = 'A'.code.toByte()
        header[10] = 'V'.code.toByte()
        header[11] = 'E'.code.toByte()
        
        // fmt chunk
        header[12] = 'f'.code.toByte()
        header[13] = 'm'.code.toByte()
        header[14] = 't'.code.toByte()
        header[15] = ' '.code.toByte()
        header[16] = 16 // fmt chunk size
        header[17] = 0
        header[18] = 0
        header[19] = 0
        header[20] = 1 // PCM format
        header[21] = 0
        header[22] = TARGET_CHANNEL_COUNT.toByte() // channels
        header[23] = 0
        header[24] = (TARGET_SAMPLE_RATE and 0xff).toByte()
        header[25] = ((TARGET_SAMPLE_RATE shr 8) and 0xff).toByte()
        header[26] = ((TARGET_SAMPLE_RATE shr 16) and 0xff).toByte()
        header[27] = ((TARGET_SAMPLE_RATE shr 24) and 0xff).toByte()
        header[28] = (byteRate and 0xff).toByte()
        header[29] = ((byteRate shr 8) and 0xff).toByte()
        header[30] = ((byteRate shr 16) and 0xff).toByte()
        header[31] = ((byteRate shr 24) and 0xff).toByte()
        header[32] = (TARGET_CHANNEL_COUNT * TARGET_BIT_DEPTH / 8).toByte() // block align
        header[33] = 0
        header[34] = TARGET_BIT_DEPTH.toByte() // bits per sample
        header[35] = 0
        
        // data chunk
        header[36] = 'd'.code.toByte()
        header[37] = 'a'.code.toByte()
        header[38] = 't'.code.toByte()
        header[39] = 'a'.code.toByte()
        header[40] = (dataSize and 0xff).toByte()
        header[41] = ((dataSize shr 8) and 0xff).toByte()
        header[42] = ((dataSize shr 16) and 0xff).toByte()
        header[43] = ((dataSize shr 24) and 0xff).toByte()
        
        return header
    }

    /**
     * 转换音频格式（采样率、声道数、位深度）
     */
    private fun convertAudioFormat(
        inputData: ByteArray,
        inputSampleRate: Int,
        inputChannelCount: Int,
        outputSampleRate: Int,
        outputChannelCount: Int
    ): ByteArray {
        return try {
            if (inputData.isEmpty()) {
                Log.w(TAG, "输入音频数据为空")
                return byteArrayOf()
            }

            Log.d(TAG, "转换音频格式: ${inputSampleRate}Hz/${inputChannelCount}ch -> ${outputSampleRate}Hz/${outputChannelCount}ch, 数据大小: ${inputData.size} bytes")
            // 将字节数组转换为Float数组（假设输入是16位PCM）
            val inputSamples = FloatArray(inputData.size / 2)
            val inputBuffer = ByteBuffer.wrap(inputData).order(ByteOrder.LITTLE_ENDIAN)

            for (i in inputSamples.indices) {
                val shortSample = inputBuffer.short
                inputSamples[i] = shortSample / 32768.0f // 归一化到[-1, 1]
            }

            // 声道转换（立体声转单声道）
            val monoSamples = if (inputChannelCount == 2 && outputChannelCount == 1) {
                convertStereoToMono(inputSamples)
            } else if (inputChannelCount == 1 && outputChannelCount == 1) {
                inputSamples
            } else {
                // 其他情况暂时不支持，直接使用原始数据
                Log.w(TAG, "不支持的声道转换: $inputChannelCount -> $outputChannelCount")
                inputSamples
            }

            // 采样率转换
            val resampledSamples = if (inputSampleRate != outputSampleRate) {
                resampleAudio(monoSamples, inputSampleRate, outputSampleRate)
            } else {
                monoSamples
            }

            // 转换回16位PCM字节数组
            val outputData = ByteArray(resampledSamples.size * 2)
            val outputBuffer = ByteBuffer.wrap(outputData).order(ByteOrder.LITTLE_ENDIAN)

            for (sample in resampledSamples) {
                // 限制范围并转换为16位整数
                val clampedSample = sample.coerceIn(-1.0f, 1.0f)
                val shortSample = (clampedSample * 32767).toInt().toShort()
                outputBuffer.putShort(shortSample)
            }

            outputData

        } catch (e: Exception) {
            Log.e(TAG, "音频格式转换失败", e)
            inputData // 转换失败时返回原始数据
        }
    }

    /**
     * 立体声转单声道（取平均值）
     */
    private fun convertStereoToMono(stereoSamples: FloatArray): FloatArray {
        val monoSamples = FloatArray(stereoSamples.size / 2)
        for (i in monoSamples.indices) {
            val left = stereoSamples[i * 2]
            val right = stereoSamples[i * 2 + 1]
            monoSamples[i] = (left + right) / 2.0f
        }
        return monoSamples
    }

    /**
     * 简单的线性插值重采样
     */
    private fun resampleAudio(inputSamples: FloatArray, inputSampleRate: Int, outputSampleRate: Int): FloatArray {
        if (inputSampleRate == outputSampleRate) {
            return inputSamples
        }

        val ratio = inputSampleRate.toFloat() / outputSampleRate.toFloat()
        val outputLength = (inputSamples.size / ratio).toInt()
        val outputSamples = FloatArray(outputLength)

        for (i in outputSamples.indices) {
            val inputIndex = i * ratio
            val index = inputIndex.toInt()

            if (index < inputSamples.size - 1) {
                // 线性插值
                val fraction = inputIndex - index
                val sample1 = inputSamples[index]
                val sample2 = inputSamples[index + 1]
                outputSamples[i] = sample1 + fraction * (sample2 - sample1)
            } else if (index < inputSamples.size) {
                outputSamples[i] = inputSamples[index]
            }
        }

        return outputSamples
    }

    /**
     * 获取文件名
     */
    private fun getFileName(uri: Uri): String {
        return try {
            context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                val nameIndex = cursor.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                cursor.moveToFirst()
                cursor.getString(nameIndex)
            } ?: "unknown"
        } catch (e: Exception) {
            "unknown"
        }
    }
    
    /**
     * 音频格式枚举
     */
    enum class AudioFormat {
        WAV, MP3, M4A, UNKNOWN
    }
}
