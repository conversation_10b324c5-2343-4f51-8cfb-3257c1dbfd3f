# Sherpa-ONNX Android 代码执行流程详解

本文档详细描述了 Sherpa-ONNX Android 项目中各个功能模块的代码执行流程，包含完整的流程图和关键代码路径。

## 1. 离线语音识别流程 (OfflineRecognizer)

### 1.1 初始化流程

```mermaid
flowchart TD
    A[创建 OfflineRecognizer] --> B{是否使用 AssetManager?}
    B -->|是| C[newFromAsset]
    B -->|否| D[newFromFile]
    C --> E[加载模型配置]
    D --> E
    E --> F[初始化特征提取器]
    F --> G[加载模型文件]
    G --> H[创建解码器]
    H --> I[初始化热词]
    I --> J[OfflineRecognizer 就绪]
    
    subgraph "模型配置"
        E --> E1[OfflineModelConfig]
        E1 --> E2[Transducer/Paraformer/Whisper]
        E2 --> E3[设置模型路径]
        E3 --> E4[配置解码参数]
    end
    
    subgraph "特征提取配置"
        F --> F1[FeatureConfig]
        F1 --> F2[采样率: 16kHz]
        F2 --> F3[特征维度: 80]
        F3 --> F4[窗口大小配置]
    end
```

### 1.2 识别执行流程

```mermaid
flowchart TD
    A[创建 OfflineStream] --> B[acceptWaveform]
    B --> C[音频预处理]
    C --> D[特征提取]
    D --> E[模型推理]
    E --> F[解码处理]
    F --> G[后处理]
    G --> H[生成识别结果]
    H --> I[返回 OfflineRecognizerResult]
    I --> J[包含文本、tokens、时间戳等]
    
    subgraph "音频预处理"
        C --> C1[音频格式转换]
        C1 --> C2[采样率重采样]
        C2 --> C3[音量归一化]
        C3 --> C4[静音检测]
    end
    
    subgraph "特征提取"
        D --> D1[短时傅里叶变换]
        D1 --> D2[梅尔频谱计算]
        D2 --> D3[对数变换]
        D3 --> D4[特征归一化]
    end
    
    subgraph "模型推理"
        E --> E1[编码器前向传播]
        E1 --> E2[解码器推理]
        E2 --> E3[连接器处理]
        E3 --> E4[输出概率计算]
    end
```

## 2. 在线流式语音识别流程 (OnlineRecognizer)

### 2.1 流式识别主流程

```mermaid
flowchart TD
    A[创建 OnlineStream] --> B[acceptWaveform]
    B --> C[音频缓冲]
    C --> D[实时特征提取]
    D --> E[流式模型推理]
    E --> F[部分结果解码]
    F --> G{检测到端点?}
    G -->|否| H[继续接收音频]
    G -->|是| I[最终解码]
    H --> B
    I --> J[生成最终结果]
    J --> K[返回 OnlineRecognizerResult]
    
    subgraph "端点检测"
        G --> G1[静音检测]
        G1 --> G2[语音活动分析]
        G2 --> G3[上下文判断]
        G3 --> G4[端点决策]
    end
    
    subgraph "流式缓冲管理"
        C --> C1[环形缓冲区]
        C1 --> C2[重叠窗口]
        C2 --> C3[缓冲区同步]
        C3 --> C4[内存管理]
    end
```

### 2.2 实时音频处理流程

```mermaid
sequenceDiagram
    participant AudioRecord
    participant AudioThread
    participant OnlineStream
    participant OnlineRecognizer
    participant UI
    
    AudioRecord->>AudioThread: 音频数据
    loop 实时处理循环
        AudioThread->>AudioThread: PCM转Float
        AudioThread->>OnlineStream: acceptWaveform()
        AudioThread->>OnlineRecognizer: isReady()
        OnlineRecognizer-->>AudioThread: true/false
        alt 识别器就绪
            AudioThread->>OnlineRecognizer: decode()
            OnlineRecognizer-->>AudioThread: 部分结果
            AudioThread->>UI: 更新显示
        end
        alt 检测到端点
            AudioThread->>OnlineRecognizer: isEndpoint()
            OnlineRecognizer-->>AudioThread: true
            AudioThread->>OnlineRecognizer: reset()
        end
    end
```

## 3. 关键词检测流程 (KeywordSpotter)

### 3.1 关键词检测主流程

```mermaid
flowchart TD
    A[用户点击开始按钮] --> B[获取用户输入关键词]
    B --> C[创建新的音频流]
    C --> D[初始化麦克风]
    D --> E[开始录音]
    E --> F[启动录音线程]
    F --> G[音频处理循环]
    
    G --> H[读取音频数据]
    H --> I[转换为浮点数组]
    I --> J[发送到音频流]
    J --> K{检测器是否就绪?}
    K -->|是| L[执行解码]
    K -->|否| M[继续采集]
    L --> N[获取检测结果]
    N --> O{检测到关键词?}
    O -->|是| P[重置检测器状态]
    O -->|否| M
    P --> Q[更新显示文本]
    Q --> R[UI线程更新界面]
    R --> M
    M --> S{是否继续录音?}
    S -->|是| H
    S -->|否| T[线程结束]
    
    subgraph "关键词匹配算法"
        N --> N1[计算相似度分数]
        N1 --> N2[阈值比较]
        N2 --> N3[时间窗口验证]
        N3 --> N4[置信度评估]
    end
```

### 3.2 关键词配置流程

```mermaid
flowchart TD
    A[关键词输入] --> B[文本预处理]
    B --> C[分词处理]
    C --> D[音素转换]
    D --> E[构建关键词图]
    E --> F[优化搜索路径]
    F --> G[加载到检测器]
    
    subgraph "关键词预处理"
        B --> B1[去除特殊字符]
        B1 --> B2[大小写标准化]
        B2 --> B3[多语言处理]
        B3 --> B4[同义词扩展]
    end
```

## 4. 说话人识别流程 (SpeakerIdentification)

### 4.1 说话人注册流程

```mermaid
flowchart TD
    A[说话人注册开始] --> B[录制注册音频]
    B --> C[音频质量检查]
    C --> D{音频质量合格?}
    D -->|否| E[提示重新录制]
    D -->|是| F[特征提取]
    E --> B
    F --> G[生成说话人向量]
    G --> H[向量质量评估]
    H --> I{向量质量合格?}
    I -->|否| E
    I -->|是| J[存储说话人信息]
    J --> K[更新说话人数据库]
    K --> L[注册完成]
    
    subgraph "特征提取过程"
        F --> F1[音频预处理]
        F1 --> F2[语音活动检测]
        F2 --> F3[特征向量计算]
        F3 --> F4[向量归一化]
    end
    
    subgraph "质量评估标准"
        H --> H1[信噪比检查]
        H1 --> H2[语音长度验证]
        H2 --> H3[特征稳定性]
        H3 --> H4[唯一性检查]
    end
```

### 4.2 实时识别流程

```mermaid
flowchart TD
    A[开始实时识别] --> B[音频采集]
    B --> C[语音活动检测]
    C --> D{检测到语音?}
    D -->|否| B
    D -->|是| E[特征提取]
    E --> F[生成查询向量]
    F --> G[相似度计算]
    G --> H[数据库搜索]
    H --> I[相似度排序]
    I --> J{最高相似度 > 阈值?}
    J -->|是| K[返回识别结果]
    J -->|否| L[返回未知说话人]
    K --> M[更新UI显示]
    L --> M
    M --> N{继续识别?}
    N -->|是| B
    N -->|否| O[结束识别]
    
    subgraph "相似度计算"
        G --> G1[余弦相似度]
        G1 --> G2[欧氏距离]
        G2 --> G3[加权融合]
        G3 --> G4[置信度计算]
    end
```

## 5. 语音合成流程 (OfflineTts)

### 5.1 TTS 初始化流程

```mermaid
flowchart TD
    A[创建 OfflineTts] --> B{是否使用 AssetManager?}
    B -->|是| C[newFromAsset]
    B -->|否| D[newFromFile]
    C --> E[加载 TTS 配置]
    D --> E
    E --> F[初始化声学模型]
    F --> G[加载声码器]
    G --> H[加载词典和tokens]
    H --> I[初始化文本处理器]
    I --> J[OfflineTts 就绪]
    
    subgraph "模型组件"
        F --> F1[前端文本处理]
        F1 --> F2[声学特征预测]
        F2 --> F3[韵律建模]
        G --> G1[梅尔频谱转音频]
        G1 --> G2[神经声码器]
    end
```

### 5.2 语音生成流程

```mermaid
flowchart TD
    A[输入文本] --> B[文本预处理]
    B --> C[文本规范化]
    C --> D[分词处理]
    D --> E[音素转换]
    E --> F[韵律预测]
    F --> G[声学模型推理]
    G --> H[梅尔频谱生成]
    H --> I[声码器合成]
    I --> J[音频后处理]
    J --> K[输出音频]
    
    subgraph "文本处理"
        B --> B1[数字转文字]
        B1 --> B2[缩写展开]
        B2 --> B3[标点处理]
        B3 --> B4[多语言检测]
    end
    
    subgraph "韵律建模"
        F --> F1[语调预测]
        F1 --> F2[重音标记]
        F2 --> F3[停顿插入]
        F3 --> F4[语速调整]
    end
    
    subgraph "音频合成"
        I --> I1[频谱转换]
        I1 --> I2[相位重建]
        I2 --> I3[音频拼接]
        I3 --> I4[平滑处理]
    end
```

## 6. 音频标记流程 (AudioTagging)

### 6.1 音频分类流程

```mermaid
flowchart TD
    A[开始音频录制] --> B[音频数据采集]
    B --> C[音频预处理]
    C --> D[特征提取]
    D --> E[模型推理]
    E --> F[分类结果]
    F --> G[后处理过滤]
    G --> H[置信度排序]
    H --> I[结果显示]
    
    subgraph "音频预处理"
        C --> C1[音频分段]
        C1 --> C2[窗口重叠]
        C2 --> C3[音量归一化]
        C3 --> C4[噪声抑制]
    end
    
    subgraph "特征提取"
        D --> D1[短时傅里叶变换]
        D1 --> D2[梅尔频谱]
        D2 --> D3[MFCC特征]
        D3 --> D4[时频特征融合]
    end
    
    subgraph "分类处理"
        E --> E1[多标签分类]
        E1 --> E2[概率计算]
        E2 --> E3[阈值过滤]
        E3 --> E4[类别映射]
    end
```

### 6.2 实时标记流程

```mermaid
sequenceDiagram
    participant User
    participant UI
    participant AudioRecord
    participant Tagger
    participant Model
    
    User->>UI: 点击开始录制
    UI->>AudioRecord: 启动录音
    loop 实时处理
        AudioRecord->>UI: 音频数据块
        UI->>UI: 数据缓存
        UI->>Tagger: 处理音频段
        Tagger->>Model: 特征提取
        Model-->>Tagger: 特征向量
        Tagger->>Model: 分类推理
        Model-->>Tagger: 分类结果
        Tagger-->>UI: 标记结果
        UI->>User: 更新显示
    end
    User->>UI: 点击停止
    UI->>AudioRecord: 停止录音
    UI->>Tagger: 最终处理
    Tagger-->>UI: 完整结果
```

## 7. VAD (语音活动检测) 流程

### 7.1 VAD 检测流程

```mermaid
flowchart TD
    A[音频输入] --> B[帧分割]
    B --> C[特征计算]
    C --> D[VAD模型推理]
    D --> E[概率输出]
    E --> F[阈值判断]
    F --> G{是否为语音?}
    G -->|是| H[标记为语音段]
    G -->|否| I[标记为静音段]
    H --> J[语音段处理]
    I --> K[静音段处理]
    J --> L[输出语音段]
    K --> M[丢弃静音段]
    L --> N[后续处理]
    M --> N
    
    subgraph "特征计算"
        C --> C1[能量特征]
        C1 --> C2[过零率]
        C2 --> C3[频谱特征]
        C3 --> C4[倒谱特征]
    end
    
    subgraph "后处理"
        F --> F1[平滑滤波]
        F1 --> F2[最小语音长度]
        F2 --> F3[最小静音长度]
        F3 --> F4[边界调整]
    end
```

## 8. WebSocket 通信流程

### 8.1 WebSocket 连接流程

```mermaid
flowchart TD
    A[应用启动] --> B[配置WebSocket参数]
    B --> C[建立WebSocket连接]
    C --> D{连接成功?}
    D -->|否| E[显示连接错误]
    D -->|是| F[发送认证信息]
    F --> G[等待服务器响应]
    G --> H{认证成功?}
    H -->|否| I[显示认证失败]
    H -->|是| J[连接就绪]
    J --> K[开始音频传输]
    
    subgraph "连接配置"
        B --> B1[服务器地址]
        B1 --> B2[端口号]
        B2 --> B3[协议版本]
        B3 --> B4[超时设置]
    end
```

### 8.2 实时音频传输流程

```mermaid
sequenceDiagram
    participant Client
    participant WebSocket
    participant Server
    participant ASR
    
    Client->>WebSocket: 建立连接
    WebSocket->>Server: 连接请求
    Server-->>WebSocket: 连接确认
    WebSocket-->>Client: 连接成功
    
    loop 音频流传输
        Client->>WebSocket: 音频数据包
        WebSocket->>Server: 转发音频
        Server->>ASR: 语音识别
        ASR-->>Server: 识别结果
        Server-->>WebSocket: 返回结果
        WebSocket-->>Client: 显示结果
    end
    
    Client->>WebSocket: 结束信号
    WebSocket->>Server: 结束请求
    Server-->>WebSocket: 最终结果
    WebSocket-->>Client: 完整转录
```

## 9. 通用错误处理流程

### 9.1 异常处理流程

```mermaid
flowchart TD
    A[操作执行] --> B{是否发生异常?}
    B -->|否| C[正常执行]
    B -->|是| D[捕获异常]
    D --> E[异常类型判断]
    E --> F{权限异常?}
    E --> G{网络异常?}
    E --> H{模型异常?}
    E --> I{其他异常?}
    
    F -->|是| J[请求权限]
    G -->|是| K[网络重连]
    H -->|是| L[重新加载模型]
    I -->|是| M[通用错误处理]
    
    J --> N[用户响应处理]
    K --> O[重试机制]
    L --> P[模型验证]
    M --> Q[错误日志记录]
    
    N --> R{处理成功?}
    O --> R
    P --> R
    Q --> R
    
    R -->|是| C
    R -->|否| S[显示错误信息]
    S --> T[用户决策]
    T --> U{重试?}
    U -->|是| A
    U -->|否| V[退出操作]
```

## 10. 资源管理流程

### 10.1 内存管理流程

```mermaid
flowchart TD
    A[组件创建] --> B[分配内存资源]
    B --> C[注册资源引用]
    C --> D[正常使用]
    D --> E{组件销毁?}
    E -->|否| D
    E -->|是| F[释放JNI资源]
    F --> G[释放Java对象]
    G --> H[清理缓存]
    H --> I[垃圾回收]
    I --> J[资源释放完成]
    
    subgraph "资源监控"
        D --> D1[内存使用监控]
        D1 --> D2[泄漏检测]
        D2 --> D3[性能分析]
        D3 --> D4[优化建议]
    end
    
    subgraph "异常处理"
        F --> F1{释放失败?}
        F1 -->|是| F2[强制释放]
        F1 -->|否| G
        F2 --> F3[记录错误]
        F3 --> G
    end
```

这些流程图详细展示了 Sherpa-ONNX Android 项目中各个功能模块的代码执行路径，有助于开发者理解系统架构和实现细节。每个流程都包含了关键的决策点、错误处理和资源管理，确保系统的稳定性和可靠性。