<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 会议文本预览 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/meeting_text_preview"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/tv_meeting_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/loading_meeting_text"
            android:textSize="14sp"
            android:padding="12dp"
            android:background="@drawable/results_background"
            android:layout_marginBottom="24dp"
            android:maxLines="6"
            android:ellipsize="end" />

        <!-- 总结类型选择 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/summary_type_selection"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <RadioGroup
            android:id="@+id/rg_summary_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp">

            <RadioButton
                android:id="@+id/rb_brief"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/summary_type_brief"
                android:checked="true"
                android:padding="8dp" />

            <RadioButton
                android:id="@+id/rb_detailed"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/summary_type_detailed"
                android:padding="8dp" />

            <RadioButton
                android:id="@+id/rb_action"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/summary_type_action"
                android:padding="8dp" />

        </RadioGroup>

        <!-- 生成总结按钮 -->
        <Button
            android:id="@+id/btn_generate_summary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/generate_summary"
            android:textSize="16sp"
            android:layout_marginBottom="16dp" />

        <!-- 加载进度 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="16dp">

            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/generating_summary"
                android:textSize="14sp"
                android:layout_marginTop="8dp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- 总结结果 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/summary_result"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <ScrollView
            android:id="@+id/scroll_summary"
            android:layout_width="match_parent"
            android:layout_height="300dp"
            android:background="@drawable/results_background"
            android:layout_marginBottom="16dp"
            android:visibility="gone">

            <TextView
                android:id="@+id/tv_summary_result"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text=""
                android:textSize="14sp"
                android:padding="12dp"
                android:textIsSelectable="true"
                android:lineSpacingExtra="2dp" />

        </ScrollView>

        <!-- 操作按钮 -->
        <LinearLayout
            android:id="@+id/layout_buttons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="8dp">

                <Button
                    android:id="@+id/btn_save_summary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/save_summary"
                    android:layout_marginEnd="8dp" />

                <Button
                    android:id="@+id/btn_copy_summary"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/copy_summary"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <Button
                android:id="@+id/btn_back_to_main"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/back_to_main"
                android:background="?android:attr/selectableItemBackground"
                android:textColor="?android:attr/textColorSecondary" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
